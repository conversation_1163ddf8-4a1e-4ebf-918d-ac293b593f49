function OS(n,r){for(var l=0;l<r.length;l++){const i=r[l];if(typeof i!="string"&&!Array.isArray(i)){for(const u in i)if(u!=="default"&&!(u in n)){const c=Object.getOwnPropertyDescriptor(i,u);c&&Object.defineProperty(n,u,c.get?c:{enumerable:!0,get:()=>i[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))i(u);new MutationObserver(u=>{for(const c of u)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&i(d)}).observe(document,{childList:!0,subtree:!0});function l(u){const c={};return u.integrity&&(c.integrity=u.integrity),u.referrerPolicy&&(c.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?c.credentials="include":u.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function i(u){if(u.ep)return;u.ep=!0;const c=l(u);fetch(u.href,c)}})();function Vd(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var nd={exports:{}},di={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sy;function MS(){if(sy)return di;sy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function l(i,u,c){var d=null;if(c!==void 0&&(d=""+c),u.key!==void 0&&(d=""+u.key),"key"in u){c={};for(var p in u)p!=="key"&&(c[p]=u[p])}else c=u;return u=c.ref,{$$typeof:n,type:i,key:d,ref:u!==void 0?u:null,props:c}}return di.Fragment=r,di.jsx=l,di.jsxs=l,di}var uy;function wS(){return uy||(uy=1,nd.exports=MS()),nd.exports}var b=wS(),ad={exports:{}},He={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cy;function AS(){if(cy)return He;cy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),S=Symbol.iterator;function O(A){return A===null||typeof A!="object"?null:(A=S&&A[S]||A["@@iterator"],typeof A=="function"?A:null)}var w={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,E={};function x(A,U,J){this.props=A,this.context=U,this.refs=E,this.updater=J||w}x.prototype.isReactComponent={},x.prototype.setState=function(A,U){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,U,"setState")},x.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function $(){}$.prototype=x.prototype;function B(A,U,J){this.props=A,this.context=U,this.refs=E,this.updater=J||w}var D=B.prototype=new $;D.constructor=B,R(D,x.prototype),D.isPureReactComponent=!0;var k=Array.isArray,z={H:null,A:null,T:null,S:null,V:null},N=Object.prototype.hasOwnProperty;function q(A,U,J,ne,fe,ue){return J=ue.ref,{$$typeof:n,type:A,key:U,ref:J!==void 0?J:null,props:ue}}function G(A,U){return q(A.type,U,void 0,void 0,void 0,A.props)}function K(A){return typeof A=="object"&&A!==null&&A.$$typeof===n}function v(A){var U={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function(J){return U[J]})}var _=/\/+/g;function V(A,U){return typeof A=="object"&&A!==null&&A.key!=null?v(""+A.key):U.toString(36)}function ae(){}function ee(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then(ae,ae):(A.status="pending",A.then(function(U){A.status==="pending"&&(A.status="fulfilled",A.value=U)},function(U){A.status==="pending"&&(A.status="rejected",A.reason=U)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function L(A,U,J,ne,fe){var ue=typeof A;(ue==="undefined"||ue==="boolean")&&(A=null);var le=!1;if(A===null)le=!0;else switch(ue){case"bigint":case"string":case"number":le=!0;break;case"object":switch(A.$$typeof){case n:case r:le=!0;break;case y:return le=A._init,L(le(A._payload),U,J,ne,fe)}}if(le)return fe=fe(A),le=ne===""?"."+V(A,0):ne,k(fe)?(J="",le!=null&&(J=le.replace(_,"$&/")+"/"),L(fe,U,J,"",function(be){return be})):fe!=null&&(K(fe)&&(fe=G(fe,J+(fe.key==null||A&&A.key===fe.key?"":(""+fe.key).replace(_,"$&/")+"/")+le)),U.push(fe)),1;le=0;var ve=ne===""?".":ne+":";if(k(A))for(var xe=0;xe<A.length;xe++)ne=A[xe],ue=ve+V(ne,xe),le+=L(ne,U,J,ue,fe);else if(xe=O(A),typeof xe=="function")for(A=xe.call(A),xe=0;!(ne=A.next()).done;)ne=ne.value,ue=ve+V(ne,xe++),le+=L(ne,U,J,ue,fe);else if(ue==="object"){if(typeof A.then=="function")return L(ee(A),U,J,ne,fe);throw U=String(A),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return le}function T(A,U,J){if(A==null)return A;var ne=[],fe=0;return L(A,ne,"","",function(ue){return U.call(J,ue,fe++)}),ne}function H(A){if(A._status===-1){var U=A._result;U=U(),U.then(function(J){(A._status===0||A._status===-1)&&(A._status=1,A._result=J)},function(J){(A._status===0||A._status===-1)&&(A._status=2,A._result=J)}),A._status===-1&&(A._status=0,A._result=U)}if(A._status===1)return A._result.default;throw A._result}var Y=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function X(){}return He.Children={map:T,forEach:function(A,U,J){T(A,function(){U.apply(this,arguments)},J)},count:function(A){var U=0;return T(A,function(){U++}),U},toArray:function(A){return T(A,function(U){return U})||[]},only:function(A){if(!K(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},He.Component=x,He.Fragment=l,He.Profiler=u,He.PureComponent=B,He.StrictMode=i,He.Suspense=m,He.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=z,He.__COMPILER_RUNTIME={__proto__:null,c:function(A){return z.H.useMemoCache(A)}},He.cache=function(A){return function(){return A.apply(null,arguments)}},He.cloneElement=function(A,U,J){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var ne=R({},A.props),fe=A.key,ue=void 0;if(U!=null)for(le in U.ref!==void 0&&(ue=void 0),U.key!==void 0&&(fe=""+U.key),U)!N.call(U,le)||le==="key"||le==="__self"||le==="__source"||le==="ref"&&U.ref===void 0||(ne[le]=U[le]);var le=arguments.length-2;if(le===1)ne.children=J;else if(1<le){for(var ve=Array(le),xe=0;xe<le;xe++)ve[xe]=arguments[xe+2];ne.children=ve}return q(A.type,fe,void 0,void 0,ue,ne)},He.createContext=function(A){return A={$$typeof:d,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:c,_context:A},A},He.createElement=function(A,U,J){var ne,fe={},ue=null;if(U!=null)for(ne in U.key!==void 0&&(ue=""+U.key),U)N.call(U,ne)&&ne!=="key"&&ne!=="__self"&&ne!=="__source"&&(fe[ne]=U[ne]);var le=arguments.length-2;if(le===1)fe.children=J;else if(1<le){for(var ve=Array(le),xe=0;xe<le;xe++)ve[xe]=arguments[xe+2];fe.children=ve}if(A&&A.defaultProps)for(ne in le=A.defaultProps,le)fe[ne]===void 0&&(fe[ne]=le[ne]);return q(A,ue,void 0,void 0,null,fe)},He.createRef=function(){return{current:null}},He.forwardRef=function(A){return{$$typeof:p,render:A}},He.isValidElement=K,He.lazy=function(A){return{$$typeof:y,_payload:{_status:-1,_result:A},_init:H}},He.memo=function(A,U){return{$$typeof:h,type:A,compare:U===void 0?null:U}},He.startTransition=function(A){var U=z.T,J={};z.T=J;try{var ne=A(),fe=z.S;fe!==null&&fe(J,ne),typeof ne=="object"&&ne!==null&&typeof ne.then=="function"&&ne.then(X,Y)}catch(ue){Y(ue)}finally{z.T=U}},He.unstable_useCacheRefresh=function(){return z.H.useCacheRefresh()},He.use=function(A){return z.H.use(A)},He.useActionState=function(A,U,J){return z.H.useActionState(A,U,J)},He.useCallback=function(A,U){return z.H.useCallback(A,U)},He.useContext=function(A){return z.H.useContext(A)},He.useDebugValue=function(){},He.useDeferredValue=function(A,U){return z.H.useDeferredValue(A,U)},He.useEffect=function(A,U,J){var ne=z.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return ne.useEffect(A,U)},He.useId=function(){return z.H.useId()},He.useImperativeHandle=function(A,U,J){return z.H.useImperativeHandle(A,U,J)},He.useInsertionEffect=function(A,U){return z.H.useInsertionEffect(A,U)},He.useLayoutEffect=function(A,U){return z.H.useLayoutEffect(A,U)},He.useMemo=function(A,U){return z.H.useMemo(A,U)},He.useOptimistic=function(A,U){return z.H.useOptimistic(A,U)},He.useReducer=function(A,U,J){return z.H.useReducer(A,U,J)},He.useRef=function(A){return z.H.useRef(A)},He.useState=function(A){return z.H.useState(A)},He.useSyncExternalStore=function(A,U,J){return z.H.useSyncExternalStore(A,U,J)},He.useTransition=function(){return z.H.useTransition()},He.version="19.1.0",He}var fy;function Yd(){return fy||(fy=1,ad.exports=AS()),ad.exports}var M=Yd();const Ba=Vd(M),vu=OS({__proto__:null,default:Ba},[M]);var rd={exports:{}},pi={},od={exports:{}},ld={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dy;function zS(){return dy||(dy=1,function(n){function r(T,H){var Y=T.length;T.push(H);e:for(;0<Y;){var X=Y-1>>>1,A=T[X];if(0<u(A,H))T[X]=H,T[Y]=A,Y=X;else break e}}function l(T){return T.length===0?null:T[0]}function i(T){if(T.length===0)return null;var H=T[0],Y=T.pop();if(Y!==H){T[0]=Y;e:for(var X=0,A=T.length,U=A>>>1;X<U;){var J=2*(X+1)-1,ne=T[J],fe=J+1,ue=T[fe];if(0>u(ne,Y))fe<A&&0>u(ue,ne)?(T[X]=ue,T[fe]=Y,X=fe):(T[X]=ne,T[J]=Y,X=J);else if(fe<A&&0>u(ue,Y))T[X]=ue,T[fe]=Y,X=fe;else break e}}return H}function u(T,H){var Y=T.sortIndex-H.sortIndex;return Y!==0?Y:T.id-H.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;n.unstable_now=function(){return c.now()}}else{var d=Date,p=d.now();n.unstable_now=function(){return d.now()-p}}var m=[],h=[],y=1,S=null,O=3,w=!1,R=!1,E=!1,x=!1,$=typeof setTimeout=="function"?setTimeout:null,B=typeof clearTimeout=="function"?clearTimeout:null,D=typeof setImmediate<"u"?setImmediate:null;function k(T){for(var H=l(h);H!==null;){if(H.callback===null)i(h);else if(H.startTime<=T)i(h),H.sortIndex=H.expirationTime,r(m,H);else break;H=l(h)}}function z(T){if(E=!1,k(T),!R)if(l(m)!==null)R=!0,N||(N=!0,V());else{var H=l(h);H!==null&&L(z,H.startTime-T)}}var N=!1,q=-1,G=5,K=-1;function v(){return x?!0:!(n.unstable_now()-K<G)}function _(){if(x=!1,N){var T=n.unstable_now();K=T;var H=!0;try{e:{R=!1,E&&(E=!1,B(q),q=-1),w=!0;var Y=O;try{t:{for(k(T),S=l(m);S!==null&&!(S.expirationTime>T&&v());){var X=S.callback;if(typeof X=="function"){S.callback=null,O=S.priorityLevel;var A=X(S.expirationTime<=T);if(T=n.unstable_now(),typeof A=="function"){S.callback=A,k(T),H=!0;break t}S===l(m)&&i(m),k(T)}else i(m);S=l(m)}if(S!==null)H=!0;else{var U=l(h);U!==null&&L(z,U.startTime-T),H=!1}}break e}finally{S=null,O=Y,w=!1}H=void 0}}finally{H?V():N=!1}}}var V;if(typeof D=="function")V=function(){D(_)};else if(typeof MessageChannel<"u"){var ae=new MessageChannel,ee=ae.port2;ae.port1.onmessage=_,V=function(){ee.postMessage(null)}}else V=function(){$(_,0)};function L(T,H){q=$(function(){T(n.unstable_now())},H)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(T){T.callback=null},n.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<T?Math.floor(1e3/T):5},n.unstable_getCurrentPriorityLevel=function(){return O},n.unstable_next=function(T){switch(O){case 1:case 2:case 3:var H=3;break;default:H=O}var Y=O;O=H;try{return T()}finally{O=Y}},n.unstable_requestPaint=function(){x=!0},n.unstable_runWithPriority=function(T,H){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var Y=O;O=T;try{return H()}finally{O=Y}},n.unstable_scheduleCallback=function(T,H,Y){var X=n.unstable_now();switch(typeof Y=="object"&&Y!==null?(Y=Y.delay,Y=typeof Y=="number"&&0<Y?X+Y:X):Y=X,T){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=Y+A,T={id:y++,callback:H,priorityLevel:T,startTime:Y,expirationTime:A,sortIndex:-1},Y>X?(T.sortIndex=Y,r(h,T),l(m)===null&&T===l(h)&&(E?(B(q),q=-1):E=!0,L(z,Y-X))):(T.sortIndex=A,r(m,T),R||w||(R=!0,N||(N=!0,V()))),T},n.unstable_shouldYield=v,n.unstable_wrapCallback=function(T){var H=O;return function(){var Y=O;O=H;try{return T.apply(this,arguments)}finally{O=Y}}}}(ld)),ld}var py;function DS(){return py||(py=1,od.exports=zS()),od.exports}var id={exports:{}},pn={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var my;function kS(){if(my)return pn;my=1;var n=Yd();function r(m){var h="https://react.dev/errors/"+m;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)h+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+m+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(){}var i={d:{f:l,r:function(){throw Error(r(522))},D:l,C:l,L:l,m:l,X:l,S:l,M:l},p:0,findDOMNode:null},u=Symbol.for("react.portal");function c(m,h,y){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:S==null?null:""+S,children:m,containerInfo:h,implementation:y}}var d=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(m,h){if(m==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return pn.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,pn.createPortal=function(m,h){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(r(299));return c(m,h,null,y)},pn.flushSync=function(m){var h=d.T,y=i.p;try{if(d.T=null,i.p=2,m)return m()}finally{d.T=h,i.p=y,i.d.f()}},pn.preconnect=function(m,h){typeof m=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,i.d.C(m,h))},pn.prefetchDNS=function(m){typeof m=="string"&&i.d.D(m)},pn.preinit=function(m,h){if(typeof m=="string"&&h&&typeof h.as=="string"){var y=h.as,S=p(y,h.crossOrigin),O=typeof h.integrity=="string"?h.integrity:void 0,w=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;y==="style"?i.d.S(m,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:S,integrity:O,fetchPriority:w}):y==="script"&&i.d.X(m,{crossOrigin:S,integrity:O,fetchPriority:w,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},pn.preinitModule=function(m,h){if(typeof m=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var y=p(h.as,h.crossOrigin);i.d.M(m,{crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&i.d.M(m)},pn.preload=function(m,h){if(typeof m=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var y=h.as,S=p(y,h.crossOrigin);i.d.L(m,y,{crossOrigin:S,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},pn.preloadModule=function(m,h){if(typeof m=="string")if(h){var y=p(h.as,h.crossOrigin);i.d.m(m,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else i.d.m(m)},pn.requestFormReset=function(m){i.d.r(m)},pn.unstable_batchedUpdates=function(m,h){return m(h)},pn.useFormState=function(m,h,y){return d.H.useFormState(m,h,y)},pn.useFormStatus=function(){return d.H.useHostTransitionStatus()},pn.version="19.1.0",pn}var hy;function Kv(){if(hy)return id.exports;hy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),id.exports=kS(),id.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gy;function $S(){if(gy)return pi;gy=1;var n=DS(),r=Yd(),l=Kv();function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function c(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function p(e){if(c(e)!==e)throw Error(i(188))}function m(e){var t=e.alternate;if(!t){if(t=c(e),t===null)throw Error(i(188));return t!==e?null:e}for(var a=e,o=t;;){var s=a.return;if(s===null)break;var f=s.alternate;if(f===null){if(o=s.return,o!==null){a=o;continue}break}if(s.child===f.child){for(f=s.child;f;){if(f===a)return p(s),e;if(f===o)return p(s),t;f=f.sibling}throw Error(i(188))}if(a.return!==o.return)a=s,o=f;else{for(var g=!1,C=s.child;C;){if(C===a){g=!0,a=s,o=f;break}if(C===o){g=!0,o=s,a=f;break}C=C.sibling}if(!g){for(C=f.child;C;){if(C===a){g=!0,a=f,o=s;break}if(C===o){g=!0,o=f,a=s;break}C=C.sibling}if(!g)throw Error(i(189))}}if(a.alternate!==o)throw Error(i(190))}if(a.tag!==3)throw Error(i(188));return a.stateNode.current===a?e:t}function h(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=h(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,S=Symbol.for("react.element"),O=Symbol.for("react.transitional.element"),w=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),x=Symbol.for("react.profiler"),$=Symbol.for("react.provider"),B=Symbol.for("react.consumer"),D=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),N=Symbol.for("react.suspense_list"),q=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),K=Symbol.for("react.activity"),v=Symbol.for("react.memo_cache_sentinel"),_=Symbol.iterator;function V(e){return e===null||typeof e!="object"?null:(e=_&&e[_]||e["@@iterator"],typeof e=="function"?e:null)}var ae=Symbol.for("react.client.reference");function ee(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ae?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case R:return"Fragment";case x:return"Profiler";case E:return"StrictMode";case z:return"Suspense";case N:return"SuspenseList";case K:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case w:return"Portal";case D:return(e.displayName||"Context")+".Provider";case B:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case q:return t=e.displayName||null,t!==null?t:ee(e.type)||"Memo";case G:t=e._payload,e=e._init;try{return ee(e(t))}catch{}}return null}var L=Array.isArray,T=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y={pending:!1,data:null,method:null,action:null},X=[],A=-1;function U(e){return{current:e}}function J(e){0>A||(e.current=X[A],X[A]=null,A--)}function ne(e,t){A++,X[A]=e.current,e.current=t}var fe=U(null),ue=U(null),le=U(null),ve=U(null);function xe(e,t){switch(ne(le,t),ne(ue,e),ne(fe,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Bg(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Bg(t),e=_g(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}J(fe),ne(fe,e)}function be(){J(fe),J(ue),J(le)}function me(e){e.memoizedState!==null&&ne(ve,e);var t=fe.current,a=_g(t,e.type);t!==a&&(ne(ue,e),ne(fe,a))}function Re(e){ue.current===e&&(J(fe),J(ue)),ve.current===e&&(J(ve),ii._currentValue=Y)}var Ne=Object.prototype.hasOwnProperty,_e=n.unstable_scheduleCallback,ke=n.unstable_cancelCallback,nt=n.unstable_shouldYield,Ve=n.unstable_requestPaint,at=n.unstable_now,he=n.unstable_getCurrentPriorityLevel,st=n.unstable_ImmediatePriority,Te=n.unstable_UserBlockingPriority,Le=n.unstable_NormalPriority,ye=n.unstable_LowPriority,It=n.unstable_IdlePriority,ut=n.log,At=n.unstable_setDisableYieldValue,rt=null,je=null;function Ye(e){if(typeof ut=="function"&&At(e),je&&typeof je.setStrictMode=="function")try{je.setStrictMode(rt,e)}catch{}}var et=Math.clz32?Math.clz32:Gt,vt=Math.log,Ae=Math.LN2;function Gt(e){return e>>>=0,e===0?32:31-(vt(e)/Ae|0)|0}var xn=256,Zt=4194304;function Ce(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Pe(e,t,a){var o=e.pendingLanes;if(o===0)return 0;var s=0,f=e.suspendedLanes,g=e.pingedLanes;e=e.warmLanes;var C=o&134217727;return C!==0?(o=C&~f,o!==0?s=Ce(o):(g&=C,g!==0?s=Ce(g):a||(a=C&~e,a!==0&&(s=Ce(a))))):(C=o&~f,C!==0?s=Ce(C):g!==0?s=Ce(g):a||(a=o&~e,a!==0&&(s=Ce(a)))),s===0?0:t!==0&&t!==s&&(t&f)===0&&(f=s&-s,a=t&-t,f>=a||f===32&&(a&4194048)!==0)?t:s}function ot(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Gn(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ha(){var e=xn;return xn<<=1,(xn&4194048)===0&&(xn=256),e}function po(){var e=Zt;return Zt<<=1,(Zt&62914560)===0&&(Zt=4194304),e}function tn(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function zn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function ga(e,t,a,o,s,f){var g=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var C=e.entanglements,j=e.expirationTimes,W=e.hiddenUpdates;for(a=g&~a;0<a;){var ie=31-et(a),ce=1<<ie;C[ie]=0,j[ie]=-1;var F=W[ie];if(F!==null)for(W[ie]=null,ie=0;ie<F.length;ie++){var Z=F[ie];Z!==null&&(Z.lane&=-536870913)}a&=~ce}o!==0&&Ua(e,o,0),f!==0&&s===0&&e.tag!==0&&(e.suspendedLanes|=f&~(g&~t))}function Ua(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var o=31-et(t);e.entangledLanes|=t,e.entanglements[o]=e.entanglements[o]|1073741824|a&4194090}function Dn(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var o=31-et(a),s=1<<o;s&t|e[o]&t&&(e[o]|=t),a&=~s}}function ur(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function cr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Ea(){var e=H.p;return e!==0?e:(e=window.event,e===void 0?32:ny(e.type))}function gl(e,t){var a=H.p;try{return H.p=e,t()}finally{H.p=a}}var Xn=Math.random().toString(36).slice(2),zt="__reactFiber$"+Xn,Xt="__reactProps$"+Xn,Kn="__reactContainer$"+Xn,Pr="__reactEvents$"+Xn,yl="__reactListeners$"+Xn,mo="__reactHandles$"+Xn,vl="__reactResources$"+Xn,Qn="__reactMarker$"+Xn;function Bt(e){delete e[zt],delete e[Xt],delete e[Pr],delete e[yl],delete e[mo]}function Mt(e){var t=e[zt];if(t)return t;for(var a=e.parentNode;a;){if(t=a[Kn]||a[zt]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=Ug(e);e!==null;){if(a=e[zt])return a;e=Ug(e)}return t}e=a,a=e.parentNode}return null}function gn(e){if(e=e[zt]||e[Kn]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ya(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(i(33))}function va(e){var t=e[vl];return t||(t=e[vl]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function te(e){e[Qn]=!0}var re=new Set,ge={};function Be(e,t){Ee(e,t),Ee(e+"Capture",t)}function Ee(e,t){for(ge[e]=t,e=0;e<t.length;e++)re.add(t[e])}var Dt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),jt={},yn={};function bl(e){return Ne.call(yn,e)?!0:Ne.call(jt,e)?!1:Dt.test(e)?yn[e]=!0:(jt[e]=!0,!1)}function ho(e,t,a){if(bl(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var o=t.toLowerCase().slice(0,5);if(o!=="data-"&&o!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Ur(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Wn(e,t,a,o){if(o===null)e.removeAttribute(a);else{switch(typeof o){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+o)}}var go,lt;function vn(e){if(go===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);go=t&&t[1]||"",lt=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+go+e+lt}var Fn=!1;function Ia(e,t){if(!e||Fn)return"";Fn=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var o={DetermineComponentFrameRoot:function(){try{if(t){var ce=function(){throw Error()};if(Object.defineProperty(ce.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(ce,[])}catch(Z){var F=Z}Reflect.construct(e,[],ce)}else{try{ce.call()}catch(Z){F=Z}e.call(ce.prototype)}}else{try{throw Error()}catch(Z){F=Z}(ce=e())&&typeof ce.catch=="function"&&ce.catch(function(){})}}catch(Z){if(Z&&F&&typeof Z.stack=="string")return[Z.stack,F.stack]}return[null,null]}};o.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(o.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(o.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=o.DetermineComponentFrameRoot(),g=f[0],C=f[1];if(g&&C){var j=g.split(`
`),W=C.split(`
`);for(s=o=0;o<j.length&&!j[o].includes("DetermineComponentFrameRoot");)o++;for(;s<W.length&&!W[s].includes("DetermineComponentFrameRoot");)s++;if(o===j.length||s===W.length)for(o=j.length-1,s=W.length-1;1<=o&&0<=s&&j[o]!==W[s];)s--;for(;1<=o&&0<=s;o--,s--)if(j[o]!==W[s]){if(o!==1||s!==1)do if(o--,s--,0>s||j[o]!==W[s]){var ie=`
`+j[o].replace(" at new "," at ");return e.displayName&&ie.includes("<anonymous>")&&(ie=ie.replace("<anonymous>",e.displayName)),ie}while(1<=o&&0<=s);break}}}finally{Fn=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?vn(a):""}function Sb(e){switch(e.tag){case 26:case 27:case 5:return vn(e.type);case 16:return vn("Lazy");case 13:return vn("Suspense");case 19:return vn("SuspenseList");case 0:case 15:return Ia(e.type,!1);case 11:return Ia(e.type.render,!1);case 1:return Ia(e.type,!0);case 31:return vn("Activity");default:return""}}function zp(e){try{var t="";do t+=Sb(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Zn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Dp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function xb(e){var t=Dp(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),o=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var s=a.get,f=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(g){o=""+g,f.call(this,g)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return o},setValue:function(g){o=""+g},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Xi(e){e._valueTracker||(e._valueTracker=xb(e))}function kp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),o="";return e&&(o=Dp(e)?e.checked?"true":"false":e.value),e=o,e!==a?(t.setValue(e),!0):!1}function Ki(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Cb=/[\n"\\]/g;function Jn(e){return e.replace(Cb,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Zu(e,t,a,o,s,f,g,C){e.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.type=g:e.removeAttribute("type"),t!=null?g==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Zn(t)):e.value!==""+Zn(t)&&(e.value=""+Zn(t)):g!=="submit"&&g!=="reset"||e.removeAttribute("value"),t!=null?Ju(e,g,Zn(t)):a!=null?Ju(e,g,Zn(a)):o!=null&&e.removeAttribute("value"),s==null&&f!=null&&(e.defaultChecked=!!f),s!=null&&(e.checked=s&&typeof s!="function"&&typeof s!="symbol"),C!=null&&typeof C!="function"&&typeof C!="symbol"&&typeof C!="boolean"?e.name=""+Zn(C):e.removeAttribute("name")}function $p(e,t,a,o,s,f,g,C){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.type=f),t!=null||a!=null){if(!(f!=="submit"&&f!=="reset"||t!=null))return;a=a!=null?""+Zn(a):"",t=t!=null?""+Zn(t):a,C||t===e.value||(e.value=t),e.defaultValue=t}o=o??s,o=typeof o!="function"&&typeof o!="symbol"&&!!o,e.checked=C?e.checked:!!o,e.defaultChecked=!!o,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(e.name=g)}function Ju(e,t,a){t==="number"&&Ki(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function yo(e,t,a,o){if(e=e.options,t){t={};for(var s=0;s<a.length;s++)t["$"+a[s]]=!0;for(a=0;a<e.length;a++)s=t.hasOwnProperty("$"+e[a].value),e[a].selected!==s&&(e[a].selected=s),s&&o&&(e[a].defaultSelected=!0)}else{for(a=""+Zn(a),t=null,s=0;s<e.length;s++){if(e[s].value===a){e[s].selected=!0,o&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function jp(e,t,a){if(t!=null&&(t=""+Zn(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Zn(a):""}function Np(e,t,a,o){if(t==null){if(o!=null){if(a!=null)throw Error(i(92));if(L(o)){if(1<o.length)throw Error(i(93));o=o[0]}a=o}a==null&&(a=""),t=a}a=Zn(t),e.defaultValue=a,o=e.textContent,o===a&&o!==""&&o!==null&&(e.value=o)}function vo(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Tb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Bp(e,t,a){var o=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?o?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":o?e.setProperty(t,a):typeof a!="number"||a===0||Tb.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function _p(e,t,a){if(t!=null&&typeof t!="object")throw Error(i(62));if(e=e.style,a!=null){for(var o in a)!a.hasOwnProperty(o)||t!=null&&t.hasOwnProperty(o)||(o.indexOf("--")===0?e.setProperty(o,""):o==="float"?e.cssFloat="":e[o]="");for(var s in t)o=t[s],t.hasOwnProperty(s)&&a[s]!==o&&Bp(e,s,o)}else for(var f in t)t.hasOwnProperty(f)&&Bp(e,f,t[f])}function ec(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Eb=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Rb=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Qi(e){return Rb.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var tc=null;function nc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bo=null,So=null;function Lp(e){var t=gn(e);if(t&&(e=t.stateNode)){var a=e[Xt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Zu(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Jn(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var o=a[t];if(o!==e&&o.form===e.form){var s=o[Xt]||null;if(!s)throw Error(i(90));Zu(o,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(t=0;t<a.length;t++)o=a[t],o.form===e.form&&kp(o)}break e;case"textarea":jp(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&yo(e,!!a.multiple,t,!1)}}}var ac=!1;function Hp(e,t,a){if(ac)return e(t,a);ac=!0;try{var o=e(t);return o}finally{if(ac=!1,(bo!==null||So!==null)&&(js(),bo&&(t=bo,e=So,So=bo=null,Lp(t),e)))for(t=0;t<e.length;t++)Lp(e[t])}}function Sl(e,t){var a=e.stateNode;if(a===null)return null;var o=a[Xt]||null;if(o===null)return null;a=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(e=e.type,o=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!o;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(i(231,t,typeof a));return a}var qa=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),rc=!1;if(qa)try{var xl={};Object.defineProperty(xl,"passive",{get:function(){rc=!0}}),window.addEventListener("test",xl,xl),window.removeEventListener("test",xl,xl)}catch{rc=!1}var fr=null,oc=null,Wi=null;function Pp(){if(Wi)return Wi;var e,t=oc,a=t.length,o,s="value"in fr?fr.value:fr.textContent,f=s.length;for(e=0;e<a&&t[e]===s[e];e++);var g=a-e;for(o=1;o<=g&&t[a-o]===s[f-o];o++);return Wi=s.slice(e,1<o?1-o:void 0)}function Fi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Zi(){return!0}function Up(){return!1}function Cn(e){function t(a,o,s,f,g){this._reactName=a,this._targetInst=s,this.type=o,this.nativeEvent=f,this.target=g,this.currentTarget=null;for(var C in e)e.hasOwnProperty(C)&&(a=e[C],this[C]=a?a(f):f[C]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?Zi:Up,this.isPropagationStopped=Up,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Zi)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Zi)},persist:function(){},isPersistent:Zi}),t}var Ir={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ji=Cn(Ir),Cl=y({},Ir,{view:0,detail:0}),Ob=Cn(Cl),lc,ic,Tl,es=y({},Cl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:uc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Tl&&(Tl&&e.type==="mousemove"?(lc=e.screenX-Tl.screenX,ic=e.screenY-Tl.screenY):ic=lc=0,Tl=e),lc)},movementY:function(e){return"movementY"in e?e.movementY:ic}}),Ip=Cn(es),Mb=y({},es,{dataTransfer:0}),wb=Cn(Mb),Ab=y({},Cl,{relatedTarget:0}),sc=Cn(Ab),zb=y({},Ir,{animationName:0,elapsedTime:0,pseudoElement:0}),Db=Cn(zb),kb=y({},Ir,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$b=Cn(kb),jb=y({},Ir,{data:0}),qp=Cn(jb),Nb={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Bb={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},_b={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Lb(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=_b[e])?!!t[e]:!1}function uc(){return Lb}var Hb=y({},Cl,{key:function(e){if(e.key){var t=Nb[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Fi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Bb[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:uc,charCode:function(e){return e.type==="keypress"?Fi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Pb=Cn(Hb),Ub=y({},es,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Vp=Cn(Ub),Ib=y({},Cl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:uc}),qb=Cn(Ib),Vb=y({},Ir,{propertyName:0,elapsedTime:0,pseudoElement:0}),Yb=Cn(Vb),Gb=y({},es,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Xb=Cn(Gb),Kb=y({},Ir,{newState:0,oldState:0}),Qb=Cn(Kb),Wb=[9,13,27,32],cc=qa&&"CompositionEvent"in window,El=null;qa&&"documentMode"in document&&(El=document.documentMode);var Fb=qa&&"TextEvent"in window&&!El,Yp=qa&&(!cc||El&&8<El&&11>=El),Gp=" ",Xp=!1;function Kp(e,t){switch(e){case"keyup":return Wb.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Qp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var xo=!1;function Zb(e,t){switch(e){case"compositionend":return Qp(t);case"keypress":return t.which!==32?null:(Xp=!0,Gp);case"textInput":return e=t.data,e===Gp&&Xp?null:e;default:return null}}function Jb(e,t){if(xo)return e==="compositionend"||!cc&&Kp(e,t)?(e=Pp(),Wi=oc=fr=null,xo=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Yp&&t.locale!=="ko"?null:t.data;default:return null}}var e1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wp(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!e1[e.type]:t==="textarea"}function Fp(e,t,a,o){bo?So?So.push(o):So=[o]:bo=o,t=Ps(t,"onChange"),0<t.length&&(a=new Ji("onChange","change",null,a,o),e.push({event:a,listeners:t}))}var Rl=null,Ol=null;function t1(e){Dg(e,0)}function ts(e){var t=ya(e);if(kp(t))return e}function Zp(e,t){if(e==="change")return t}var Jp=!1;if(qa){var fc;if(qa){var dc="oninput"in document;if(!dc){var em=document.createElement("div");em.setAttribute("oninput","return;"),dc=typeof em.oninput=="function"}fc=dc}else fc=!1;Jp=fc&&(!document.documentMode||9<document.documentMode)}function tm(){Rl&&(Rl.detachEvent("onpropertychange",nm),Ol=Rl=null)}function nm(e){if(e.propertyName==="value"&&ts(Ol)){var t=[];Fp(t,Ol,e,nc(e)),Hp(t1,t)}}function n1(e,t,a){e==="focusin"?(tm(),Rl=t,Ol=a,Rl.attachEvent("onpropertychange",nm)):e==="focusout"&&tm()}function a1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ts(Ol)}function r1(e,t){if(e==="click")return ts(t)}function o1(e,t){if(e==="input"||e==="change")return ts(t)}function l1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var kn=typeof Object.is=="function"?Object.is:l1;function Ml(e,t){if(kn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),o=Object.keys(t);if(a.length!==o.length)return!1;for(o=0;o<a.length;o++){var s=a[o];if(!Ne.call(t,s)||!kn(e[s],t[s]))return!1}return!0}function am(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function rm(e,t){var a=am(e);e=0;for(var o;a;){if(a.nodeType===3){if(o=e+a.textContent.length,e<=t&&o>=t)return{node:a,offset:t-e};e=o}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=am(a)}}function om(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?om(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function lm(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ki(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Ki(e.document)}return t}function pc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var i1=qa&&"documentMode"in document&&11>=document.documentMode,Co=null,mc=null,wl=null,hc=!1;function im(e,t,a){var o=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;hc||Co==null||Co!==Ki(o)||(o=Co,"selectionStart"in o&&pc(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),wl&&Ml(wl,o)||(wl=o,o=Ps(mc,"onSelect"),0<o.length&&(t=new Ji("onSelect","select",null,t,a),e.push({event:t,listeners:o}),t.target=Co)))}function qr(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var To={animationend:qr("Animation","AnimationEnd"),animationiteration:qr("Animation","AnimationIteration"),animationstart:qr("Animation","AnimationStart"),transitionrun:qr("Transition","TransitionRun"),transitionstart:qr("Transition","TransitionStart"),transitioncancel:qr("Transition","TransitionCancel"),transitionend:qr("Transition","TransitionEnd")},gc={},sm={};qa&&(sm=document.createElement("div").style,"AnimationEvent"in window||(delete To.animationend.animation,delete To.animationiteration.animation,delete To.animationstart.animation),"TransitionEvent"in window||delete To.transitionend.transition);function Vr(e){if(gc[e])return gc[e];if(!To[e])return e;var t=To[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in sm)return gc[e]=t[a];return e}var um=Vr("animationend"),cm=Vr("animationiteration"),fm=Vr("animationstart"),s1=Vr("transitionrun"),u1=Vr("transitionstart"),c1=Vr("transitioncancel"),dm=Vr("transitionend"),pm=new Map,yc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");yc.push("scrollEnd");function ba(e,t){pm.set(e,t),Be(t,[e])}var mm=new WeakMap;function ea(e,t){if(typeof e=="object"&&e!==null){var a=mm.get(e);return a!==void 0?a:(t={value:e,source:t,stack:zp(t)},mm.set(e,t),t)}return{value:e,source:t,stack:zp(t)}}var ta=[],Eo=0,vc=0;function ns(){for(var e=Eo,t=vc=Eo=0;t<e;){var a=ta[t];ta[t++]=null;var o=ta[t];ta[t++]=null;var s=ta[t];ta[t++]=null;var f=ta[t];if(ta[t++]=null,o!==null&&s!==null){var g=o.pending;g===null?s.next=s:(s.next=g.next,g.next=s),o.pending=s}f!==0&&hm(a,s,f)}}function as(e,t,a,o){ta[Eo++]=e,ta[Eo++]=t,ta[Eo++]=a,ta[Eo++]=o,vc|=o,e.lanes|=o,e=e.alternate,e!==null&&(e.lanes|=o)}function bc(e,t,a,o){return as(e,t,a,o),rs(e)}function Ro(e,t){return as(e,null,null,t),rs(e)}function hm(e,t,a){e.lanes|=a;var o=e.alternate;o!==null&&(o.lanes|=a);for(var s=!1,f=e.return;f!==null;)f.childLanes|=a,o=f.alternate,o!==null&&(o.childLanes|=a),f.tag===22&&(e=f.stateNode,e===null||e._visibility&1||(s=!0)),e=f,f=f.return;return e.tag===3?(f=e.stateNode,s&&t!==null&&(s=31-et(a),e=f.hiddenUpdates,o=e[s],o===null?e[s]=[t]:o.push(t),t.lane=a|536870912),f):null}function rs(e){if(50<Jl)throw Jl=0,Of=null,Error(i(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Oo={};function f1(e,t,a,o){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $n(e,t,a,o){return new f1(e,t,a,o)}function Sc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Va(e,t){var a=e.alternate;return a===null?(a=$n(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function gm(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function os(e,t,a,o,s,f){var g=0;if(o=e,typeof e=="function")Sc(e)&&(g=1);else if(typeof e=="string")g=pS(e,a,fe.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case K:return e=$n(31,a,t,s),e.elementType=K,e.lanes=f,e;case R:return Yr(a.children,s,f,t);case E:g=8,s|=24;break;case x:return e=$n(12,a,t,s|2),e.elementType=x,e.lanes=f,e;case z:return e=$n(13,a,t,s),e.elementType=z,e.lanes=f,e;case N:return e=$n(19,a,t,s),e.elementType=N,e.lanes=f,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case $:case D:g=10;break e;case B:g=9;break e;case k:g=11;break e;case q:g=14;break e;case G:g=16,o=null;break e}g=29,a=Error(i(130,e===null?"null":typeof e,"")),o=null}return t=$n(g,a,t,s),t.elementType=e,t.type=o,t.lanes=f,t}function Yr(e,t,a,o){return e=$n(7,e,o,t),e.lanes=a,e}function xc(e,t,a){return e=$n(6,e,null,t),e.lanes=a,e}function Cc(e,t,a){return t=$n(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Mo=[],wo=0,ls=null,is=0,na=[],aa=0,Gr=null,Ya=1,Ga="";function Xr(e,t){Mo[wo++]=is,Mo[wo++]=ls,ls=e,is=t}function ym(e,t,a){na[aa++]=Ya,na[aa++]=Ga,na[aa++]=Gr,Gr=e;var o=Ya;e=Ga;var s=32-et(o)-1;o&=~(1<<s),a+=1;var f=32-et(t)+s;if(30<f){var g=s-s%5;f=(o&(1<<g)-1).toString(32),o>>=g,s-=g,Ya=1<<32-et(t)+s|a<<s|o,Ga=f+e}else Ya=1<<f|a<<s|o,Ga=e}function Tc(e){e.return!==null&&(Xr(e,1),ym(e,1,0))}function Ec(e){for(;e===ls;)ls=Mo[--wo],Mo[wo]=null,is=Mo[--wo],Mo[wo]=null;for(;e===Gr;)Gr=na[--aa],na[aa]=null,Ga=na[--aa],na[aa]=null,Ya=na[--aa],na[aa]=null}var bn=null,_t=null,ft=!1,Kr=null,Ra=!1,Rc=Error(i(519));function Qr(e){var t=Error(i(418,""));throw Dl(ea(t,e)),Rc}function vm(e){var t=e.stateNode,a=e.type,o=e.memoizedProps;switch(t[zt]=e,t[Xt]=o,a){case"dialog":Fe("cancel",t),Fe("close",t);break;case"iframe":case"object":case"embed":Fe("load",t);break;case"video":case"audio":for(a=0;a<ti.length;a++)Fe(ti[a],t);break;case"source":Fe("error",t);break;case"img":case"image":case"link":Fe("error",t),Fe("load",t);break;case"details":Fe("toggle",t);break;case"input":Fe("invalid",t),$p(t,o.value,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name,!0),Xi(t);break;case"select":Fe("invalid",t);break;case"textarea":Fe("invalid",t),Np(t,o.value,o.defaultValue,o.children),Xi(t)}a=o.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||o.suppressHydrationWarning===!0||Ng(t.textContent,a)?(o.popover!=null&&(Fe("beforetoggle",t),Fe("toggle",t)),o.onScroll!=null&&Fe("scroll",t),o.onScrollEnd!=null&&Fe("scrollend",t),o.onClick!=null&&(t.onclick=Us),t=!0):t=!1,t||Qr(e)}function bm(e){for(bn=e.return;bn;)switch(bn.tag){case 5:case 13:Ra=!1;return;case 27:case 3:Ra=!0;return;default:bn=bn.return}}function Al(e){if(e!==bn)return!1;if(!ft)return bm(e),ft=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||If(e.type,e.memoizedProps)),a=!a),a&&_t&&Qr(e),bm(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){_t=xa(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}_t=null}}else t===27?(t=_t,Mr(e.type)?(e=Gf,Gf=null,_t=e):_t=t):_t=bn?xa(e.stateNode.nextSibling):null;return!0}function zl(){_t=bn=null,ft=!1}function Sm(){var e=Kr;return e!==null&&(Rn===null?Rn=e:Rn.push.apply(Rn,e),Kr=null),e}function Dl(e){Kr===null?Kr=[e]:Kr.push(e)}var Oc=U(null),Wr=null,Xa=null;function dr(e,t,a){ne(Oc,t._currentValue),t._currentValue=a}function Ka(e){e._currentValue=Oc.current,J(Oc)}function Mc(e,t,a){for(;e!==null;){var o=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,o!==null&&(o.childLanes|=t)):o!==null&&(o.childLanes&t)!==t&&(o.childLanes|=t),e===a)break;e=e.return}}function wc(e,t,a,o){var s=e.child;for(s!==null&&(s.return=e);s!==null;){var f=s.dependencies;if(f!==null){var g=s.child;f=f.firstContext;e:for(;f!==null;){var C=f;f=s;for(var j=0;j<t.length;j++)if(C.context===t[j]){f.lanes|=a,C=f.alternate,C!==null&&(C.lanes|=a),Mc(f.return,a,e),o||(g=null);break e}f=C.next}}else if(s.tag===18){if(g=s.return,g===null)throw Error(i(341));g.lanes|=a,f=g.alternate,f!==null&&(f.lanes|=a),Mc(g,a,e),g=null}else g=s.child;if(g!==null)g.return=s;else for(g=s;g!==null;){if(g===e){g=null;break}if(s=g.sibling,s!==null){s.return=g.return,g=s;break}g=g.return}s=g}}function kl(e,t,a,o){e=null;for(var s=t,f=!1;s!==null;){if(!f){if((s.flags&524288)!==0)f=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var g=s.alternate;if(g===null)throw Error(i(387));if(g=g.memoizedProps,g!==null){var C=s.type;kn(s.pendingProps.value,g.value)||(e!==null?e.push(C):e=[C])}}else if(s===ve.current){if(g=s.alternate,g===null)throw Error(i(387));g.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(e!==null?e.push(ii):e=[ii])}s=s.return}e!==null&&wc(t,e,a,o),t.flags|=262144}function ss(e){for(e=e.firstContext;e!==null;){if(!kn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Fr(e){Wr=e,Xa=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function dn(e){return xm(Wr,e)}function us(e,t){return Wr===null&&Fr(e),xm(e,t)}function xm(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Xa===null){if(e===null)throw Error(i(308));Xa=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Xa=Xa.next=t;return a}var d1=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,o){e.push(o)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},p1=n.unstable_scheduleCallback,m1=n.unstable_NormalPriority,Jt={$$typeof:D,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ac(){return{controller:new d1,data:new Map,refCount:0}}function $l(e){e.refCount--,e.refCount===0&&p1(m1,function(){e.controller.abort()})}var jl=null,zc=0,Ao=0,zo=null;function h1(e,t){if(jl===null){var a=jl=[];zc=0,Ao=$f(),zo={status:"pending",value:void 0,then:function(o){a.push(o)}}}return zc++,t.then(Cm,Cm),t}function Cm(){if(--zc===0&&jl!==null){zo!==null&&(zo.status="fulfilled");var e=jl;jl=null,Ao=0,zo=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function g1(e,t){var a=[],o={status:"pending",value:null,reason:null,then:function(s){a.push(s)}};return e.then(function(){o.status="fulfilled",o.value=t;for(var s=0;s<a.length;s++)(0,a[s])(t)},function(s){for(o.status="rejected",o.reason=s,s=0;s<a.length;s++)(0,a[s])(void 0)}),o}var Tm=T.S;T.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&h1(e,t),Tm!==null&&Tm(e,t)};var Zr=U(null);function Dc(){var e=Zr.current;return e!==null?e:Ot.pooledCache}function cs(e,t){t===null?ne(Zr,Zr.current):ne(Zr,t.pool)}function Em(){var e=Dc();return e===null?null:{parent:Jt._currentValue,pool:e}}var Nl=Error(i(460)),Rm=Error(i(474)),fs=Error(i(542)),kc={then:function(){}};function Om(e){return e=e.status,e==="fulfilled"||e==="rejected"}function ds(){}function Mm(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(ds,ds),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Am(e),e;default:if(typeof t.status=="string")t.then(ds,ds);else{if(e=Ot,e!==null&&100<e.shellSuspendCounter)throw Error(i(482));e=t,e.status="pending",e.then(function(o){if(t.status==="pending"){var s=t;s.status="fulfilled",s.value=o}},function(o){if(t.status==="pending"){var s=t;s.status="rejected",s.reason=o}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Am(e),e}throw Bl=t,Nl}}var Bl=null;function wm(){if(Bl===null)throw Error(i(459));var e=Bl;return Bl=null,e}function Am(e){if(e===Nl||e===fs)throw Error(i(483))}var pr=!1;function $c(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function jc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function mr(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function hr(e,t,a){var o=e.updateQueue;if(o===null)return null;if(o=o.shared,(ht&2)!==0){var s=o.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),o.pending=t,t=rs(e),hm(e,null,a),t}return as(e,o,t,a),rs(e)}function _l(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var o=t.lanes;o&=e.pendingLanes,a|=o,t.lanes=a,Dn(e,a)}}function Nc(e,t){var a=e.updateQueue,o=e.alternate;if(o!==null&&(o=o.updateQueue,a===o)){var s=null,f=null;if(a=a.firstBaseUpdate,a!==null){do{var g={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};f===null?s=f=g:f=f.next=g,a=a.next}while(a!==null);f===null?s=f=t:f=f.next=t}else s=f=t;a={baseState:o.baseState,firstBaseUpdate:s,lastBaseUpdate:f,shared:o.shared,callbacks:o.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Bc=!1;function Ll(){if(Bc){var e=zo;if(e!==null)throw e}}function Hl(e,t,a,o){Bc=!1;var s=e.updateQueue;pr=!1;var f=s.firstBaseUpdate,g=s.lastBaseUpdate,C=s.shared.pending;if(C!==null){s.shared.pending=null;var j=C,W=j.next;j.next=null,g===null?f=W:g.next=W,g=j;var ie=e.alternate;ie!==null&&(ie=ie.updateQueue,C=ie.lastBaseUpdate,C!==g&&(C===null?ie.firstBaseUpdate=W:C.next=W,ie.lastBaseUpdate=j))}if(f!==null){var ce=s.baseState;g=0,ie=W=j=null,C=f;do{var F=C.lane&-536870913,Z=F!==C.lane;if(Z?(it&F)===F:(o&F)===F){F!==0&&F===Ao&&(Bc=!0),ie!==null&&(ie=ie.next={lane:0,tag:C.tag,payload:C.payload,callback:null,next:null});e:{var $e=e,ze=C;F=t;var xt=a;switch(ze.tag){case 1:if($e=ze.payload,typeof $e=="function"){ce=$e.call(xt,ce,F);break e}ce=$e;break e;case 3:$e.flags=$e.flags&-65537|128;case 0:if($e=ze.payload,F=typeof $e=="function"?$e.call(xt,ce,F):$e,F==null)break e;ce=y({},ce,F);break e;case 2:pr=!0}}F=C.callback,F!==null&&(e.flags|=64,Z&&(e.flags|=8192),Z=s.callbacks,Z===null?s.callbacks=[F]:Z.push(F))}else Z={lane:F,tag:C.tag,payload:C.payload,callback:C.callback,next:null},ie===null?(W=ie=Z,j=ce):ie=ie.next=Z,g|=F;if(C=C.next,C===null){if(C=s.shared.pending,C===null)break;Z=C,C=Z.next,Z.next=null,s.lastBaseUpdate=Z,s.shared.pending=null}}while(!0);ie===null&&(j=ce),s.baseState=j,s.firstBaseUpdate=W,s.lastBaseUpdate=ie,f===null&&(s.shared.lanes=0),Tr|=g,e.lanes=g,e.memoizedState=ce}}function zm(e,t){if(typeof e!="function")throw Error(i(191,e));e.call(t)}function Dm(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)zm(a[e],t)}var Do=U(null),ps=U(0);function km(e,t){e=tr,ne(ps,e),ne(Do,t),tr=e|t.baseLanes}function _c(){ne(ps,tr),ne(Do,Do.current)}function Lc(){tr=ps.current,J(Do),J(ps)}var gr=0,Ie=null,bt=null,Kt=null,ms=!1,ko=!1,Jr=!1,hs=0,Pl=0,$o=null,y1=0;function qt(){throw Error(i(321))}function Hc(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!kn(e[a],t[a]))return!1;return!0}function Pc(e,t,a,o,s,f){return gr=f,Ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,T.H=e===null||e.memoizedState===null?hh:gh,Jr=!1,f=a(o,s),Jr=!1,ko&&(f=jm(t,a,o,s)),$m(e),f}function $m(e){T.H=xs;var t=bt!==null&&bt.next!==null;if(gr=0,Kt=bt=Ie=null,ms=!1,Pl=0,$o=null,t)throw Error(i(300));e===null||nn||(e=e.dependencies,e!==null&&ss(e)&&(nn=!0))}function jm(e,t,a,o){Ie=e;var s=0;do{if(ko&&($o=null),Pl=0,ko=!1,25<=s)throw Error(i(301));if(s+=1,Kt=bt=null,e.updateQueue!=null){var f=e.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}T.H=E1,f=t(a,o)}while(ko);return f}function v1(){var e=T.H,t=e.useState()[0];return t=typeof t.then=="function"?Ul(t):t,e=e.useState()[0],(bt!==null?bt.memoizedState:null)!==e&&(Ie.flags|=1024),t}function Uc(){var e=hs!==0;return hs=0,e}function Ic(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function qc(e){if(ms){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}ms=!1}gr=0,Kt=bt=Ie=null,ko=!1,Pl=hs=0,$o=null}function Tn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Kt===null?Ie.memoizedState=Kt=e:Kt=Kt.next=e,Kt}function Qt(){if(bt===null){var e=Ie.alternate;e=e!==null?e.memoizedState:null}else e=bt.next;var t=Kt===null?Ie.memoizedState:Kt.next;if(t!==null)Kt=t,bt=e;else{if(e===null)throw Ie.alternate===null?Error(i(467)):Error(i(310));bt=e,e={memoizedState:bt.memoizedState,baseState:bt.baseState,baseQueue:bt.baseQueue,queue:bt.queue,next:null},Kt===null?Ie.memoizedState=Kt=e:Kt=Kt.next=e}return Kt}function Vc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ul(e){var t=Pl;return Pl+=1,$o===null&&($o=[]),e=Mm($o,e,t),t=Ie,(Kt===null?t.memoizedState:Kt.next)===null&&(t=t.alternate,T.H=t===null||t.memoizedState===null?hh:gh),e}function gs(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ul(e);if(e.$$typeof===D)return dn(e)}throw Error(i(438,String(e)))}function Yc(e){var t=null,a=Ie.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var o=Ie.alternate;o!==null&&(o=o.updateQueue,o!==null&&(o=o.memoCache,o!=null&&(t={data:o.data.map(function(s){return s.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Vc(),Ie.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),o=0;o<e;o++)a[o]=v;return t.index++,a}function Qa(e,t){return typeof t=="function"?t(e):t}function ys(e){var t=Qt();return Gc(t,bt,e)}function Gc(e,t,a){var o=e.queue;if(o===null)throw Error(i(311));o.lastRenderedReducer=a;var s=e.baseQueue,f=o.pending;if(f!==null){if(s!==null){var g=s.next;s.next=f.next,f.next=g}t.baseQueue=s=f,o.pending=null}if(f=e.baseState,s===null)e.memoizedState=f;else{t=s.next;var C=g=null,j=null,W=t,ie=!1;do{var ce=W.lane&-536870913;if(ce!==W.lane?(it&ce)===ce:(gr&ce)===ce){var F=W.revertLane;if(F===0)j!==null&&(j=j.next={lane:0,revertLane:0,action:W.action,hasEagerState:W.hasEagerState,eagerState:W.eagerState,next:null}),ce===Ao&&(ie=!0);else if((gr&F)===F){W=W.next,F===Ao&&(ie=!0);continue}else ce={lane:0,revertLane:W.revertLane,action:W.action,hasEagerState:W.hasEagerState,eagerState:W.eagerState,next:null},j===null?(C=j=ce,g=f):j=j.next=ce,Ie.lanes|=F,Tr|=F;ce=W.action,Jr&&a(f,ce),f=W.hasEagerState?W.eagerState:a(f,ce)}else F={lane:ce,revertLane:W.revertLane,action:W.action,hasEagerState:W.hasEagerState,eagerState:W.eagerState,next:null},j===null?(C=j=F,g=f):j=j.next=F,Ie.lanes|=ce,Tr|=ce;W=W.next}while(W!==null&&W!==t);if(j===null?g=f:j.next=C,!kn(f,e.memoizedState)&&(nn=!0,ie&&(a=zo,a!==null)))throw a;e.memoizedState=f,e.baseState=g,e.baseQueue=j,o.lastRenderedState=f}return s===null&&(o.lanes=0),[e.memoizedState,o.dispatch]}function Xc(e){var t=Qt(),a=t.queue;if(a===null)throw Error(i(311));a.lastRenderedReducer=e;var o=a.dispatch,s=a.pending,f=t.memoizedState;if(s!==null){a.pending=null;var g=s=s.next;do f=e(f,g.action),g=g.next;while(g!==s);kn(f,t.memoizedState)||(nn=!0),t.memoizedState=f,t.baseQueue===null&&(t.baseState=f),a.lastRenderedState=f}return[f,o]}function Nm(e,t,a){var o=Ie,s=Qt(),f=ft;if(f){if(a===void 0)throw Error(i(407));a=a()}else a=t();var g=!kn((bt||s).memoizedState,a);g&&(s.memoizedState=a,nn=!0),s=s.queue;var C=Lm.bind(null,o,s,e);if(Il(2048,8,C,[e]),s.getSnapshot!==t||g||Kt!==null&&Kt.memoizedState.tag&1){if(o.flags|=2048,jo(9,vs(),_m.bind(null,o,s,a,t),null),Ot===null)throw Error(i(349));f||(gr&124)!==0||Bm(o,t,a)}return a}function Bm(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=Ie.updateQueue,t===null?(t=Vc(),Ie.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function _m(e,t,a,o){t.value=a,t.getSnapshot=o,Hm(t)&&Pm(e)}function Lm(e,t,a){return a(function(){Hm(t)&&Pm(e)})}function Hm(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!kn(e,a)}catch{return!0}}function Pm(e){var t=Ro(e,2);t!==null&&Ln(t,e,2)}function Kc(e){var t=Tn();if(typeof e=="function"){var a=e;if(e=a(),Jr){Ye(!0);try{a()}finally{Ye(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qa,lastRenderedState:e},t}function Um(e,t,a,o){return e.baseState=a,Gc(e,bt,typeof o=="function"?o:Qa)}function b1(e,t,a,o,s){if(Ss(e))throw Error(i(485));if(e=t.action,e!==null){var f={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){f.listeners.push(g)}};T.T!==null?a(!0):f.isTransition=!1,o(f),a=t.pending,a===null?(f.next=t.pending=f,Im(t,f)):(f.next=a.next,t.pending=a.next=f)}}function Im(e,t){var a=t.action,o=t.payload,s=e.state;if(t.isTransition){var f=T.T,g={};T.T=g;try{var C=a(s,o),j=T.S;j!==null&&j(g,C),qm(e,t,C)}catch(W){Qc(e,t,W)}finally{T.T=f}}else try{f=a(s,o),qm(e,t,f)}catch(W){Qc(e,t,W)}}function qm(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(o){Vm(e,t,o)},function(o){return Qc(e,t,o)}):Vm(e,t,a)}function Vm(e,t,a){t.status="fulfilled",t.value=a,Ym(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Im(e,a)))}function Qc(e,t,a){var o=e.pending;if(e.pending=null,o!==null){o=o.next;do t.status="rejected",t.reason=a,Ym(t),t=t.next;while(t!==o)}e.action=null}function Ym(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Gm(e,t){return t}function Xm(e,t){if(ft){var a=Ot.formState;if(a!==null){e:{var o=Ie;if(ft){if(_t){t:{for(var s=_t,f=Ra;s.nodeType!==8;){if(!f){s=null;break t}if(s=xa(s.nextSibling),s===null){s=null;break t}}f=s.data,s=f==="F!"||f==="F"?s:null}if(s){_t=xa(s.nextSibling),o=s.data==="F!";break e}}Qr(o)}o=!1}o&&(t=a[0])}}return a=Tn(),a.memoizedState=a.baseState=t,o={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Gm,lastRenderedState:t},a.queue=o,a=dh.bind(null,Ie,o),o.dispatch=a,o=Kc(!1),f=ef.bind(null,Ie,!1,o.queue),o=Tn(),s={state:t,dispatch:null,action:e,pending:null},o.queue=s,a=b1.bind(null,Ie,s,f,a),s.dispatch=a,o.memoizedState=e,[t,a,!1]}function Km(e){var t=Qt();return Qm(t,bt,e)}function Qm(e,t,a){if(t=Gc(e,t,Gm)[0],e=ys(Qa)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var o=Ul(t)}catch(g){throw g===Nl?fs:g}else o=t;t=Qt();var s=t.queue,f=s.dispatch;return a!==t.memoizedState&&(Ie.flags|=2048,jo(9,vs(),S1.bind(null,s,a),null)),[o,f,e]}function S1(e,t){e.action=t}function Wm(e){var t=Qt(),a=bt;if(a!==null)return Qm(t,a,e);Qt(),t=t.memoizedState,a=Qt();var o=a.queue.dispatch;return a.memoizedState=e,[t,o,!1]}function jo(e,t,a,o){return e={tag:e,create:a,deps:o,inst:t,next:null},t=Ie.updateQueue,t===null&&(t=Vc(),Ie.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(o=a.next,a.next=e,e.next=o,t.lastEffect=e),e}function vs(){return{destroy:void 0,resource:void 0}}function Fm(){return Qt().memoizedState}function bs(e,t,a,o){var s=Tn();o=o===void 0?null:o,Ie.flags|=e,s.memoizedState=jo(1|t,vs(),a,o)}function Il(e,t,a,o){var s=Qt();o=o===void 0?null:o;var f=s.memoizedState.inst;bt!==null&&o!==null&&Hc(o,bt.memoizedState.deps)?s.memoizedState=jo(t,f,a,o):(Ie.flags|=e,s.memoizedState=jo(1|t,f,a,o))}function Zm(e,t){bs(8390656,8,e,t)}function Jm(e,t){Il(2048,8,e,t)}function eh(e,t){return Il(4,2,e,t)}function th(e,t){return Il(4,4,e,t)}function nh(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ah(e,t,a){a=a!=null?a.concat([e]):null,Il(4,4,nh.bind(null,t,e),a)}function Wc(){}function rh(e,t){var a=Qt();t=t===void 0?null:t;var o=a.memoizedState;return t!==null&&Hc(t,o[1])?o[0]:(a.memoizedState=[e,t],e)}function oh(e,t){var a=Qt();t=t===void 0?null:t;var o=a.memoizedState;if(t!==null&&Hc(t,o[1]))return o[0];if(o=e(),Jr){Ye(!0);try{e()}finally{Ye(!1)}}return a.memoizedState=[o,t],o}function Fc(e,t,a){return a===void 0||(gr&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=sg(),Ie.lanes|=e,Tr|=e,a)}function lh(e,t,a,o){return kn(a,t)?a:Do.current!==null?(e=Fc(e,a,o),kn(e,t)||(nn=!0),e):(gr&42)===0?(nn=!0,e.memoizedState=a):(e=sg(),Ie.lanes|=e,Tr|=e,t)}function ih(e,t,a,o,s){var f=H.p;H.p=f!==0&&8>f?f:8;var g=T.T,C={};T.T=C,ef(e,!1,t,a);try{var j=s(),W=T.S;if(W!==null&&W(C,j),j!==null&&typeof j=="object"&&typeof j.then=="function"){var ie=g1(j,o);ql(e,t,ie,_n(e))}else ql(e,t,o,_n(e))}catch(ce){ql(e,t,{then:function(){},status:"rejected",reason:ce},_n())}finally{H.p=f,T.T=g}}function x1(){}function Zc(e,t,a,o){if(e.tag!==5)throw Error(i(476));var s=sh(e).queue;ih(e,s,t,Y,a===null?x1:function(){return uh(e),a(o)})}function sh(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:Y,baseState:Y,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qa,lastRenderedState:Y},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qa,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function uh(e){var t=sh(e).next.queue;ql(e,t,{},_n())}function Jc(){return dn(ii)}function ch(){return Qt().memoizedState}function fh(){return Qt().memoizedState}function C1(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=_n();e=mr(a);var o=hr(t,e,a);o!==null&&(Ln(o,t,a),_l(o,t,a)),t={cache:Ac()},e.payload=t;return}t=t.return}}function T1(e,t,a){var o=_n();a={lane:o,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Ss(e)?ph(t,a):(a=bc(e,t,a,o),a!==null&&(Ln(a,e,o),mh(a,t,o)))}function dh(e,t,a){var o=_n();ql(e,t,a,o)}function ql(e,t,a,o){var s={lane:o,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Ss(e))ph(t,s);else{var f=e.alternate;if(e.lanes===0&&(f===null||f.lanes===0)&&(f=t.lastRenderedReducer,f!==null))try{var g=t.lastRenderedState,C=f(g,a);if(s.hasEagerState=!0,s.eagerState=C,kn(C,g))return as(e,t,s,0),Ot===null&&ns(),!1}catch{}finally{}if(a=bc(e,t,s,o),a!==null)return Ln(a,e,o),mh(a,t,o),!0}return!1}function ef(e,t,a,o){if(o={lane:2,revertLane:$f(),action:o,hasEagerState:!1,eagerState:null,next:null},Ss(e)){if(t)throw Error(i(479))}else t=bc(e,a,o,2),t!==null&&Ln(t,e,2)}function Ss(e){var t=e.alternate;return e===Ie||t!==null&&t===Ie}function ph(e,t){ko=ms=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function mh(e,t,a){if((a&4194048)!==0){var o=t.lanes;o&=e.pendingLanes,a|=o,t.lanes=a,Dn(e,a)}}var xs={readContext:dn,use:gs,useCallback:qt,useContext:qt,useEffect:qt,useImperativeHandle:qt,useLayoutEffect:qt,useInsertionEffect:qt,useMemo:qt,useReducer:qt,useRef:qt,useState:qt,useDebugValue:qt,useDeferredValue:qt,useTransition:qt,useSyncExternalStore:qt,useId:qt,useHostTransitionStatus:qt,useFormState:qt,useActionState:qt,useOptimistic:qt,useMemoCache:qt,useCacheRefresh:qt},hh={readContext:dn,use:gs,useCallback:function(e,t){return Tn().memoizedState=[e,t===void 0?null:t],e},useContext:dn,useEffect:Zm,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,bs(4194308,4,nh.bind(null,t,e),a)},useLayoutEffect:function(e,t){return bs(4194308,4,e,t)},useInsertionEffect:function(e,t){bs(4,2,e,t)},useMemo:function(e,t){var a=Tn();t=t===void 0?null:t;var o=e();if(Jr){Ye(!0);try{e()}finally{Ye(!1)}}return a.memoizedState=[o,t],o},useReducer:function(e,t,a){var o=Tn();if(a!==void 0){var s=a(t);if(Jr){Ye(!0);try{a(t)}finally{Ye(!1)}}}else s=t;return o.memoizedState=o.baseState=s,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:s},o.queue=e,e=e.dispatch=T1.bind(null,Ie,e),[o.memoizedState,e]},useRef:function(e){var t=Tn();return e={current:e},t.memoizedState=e},useState:function(e){e=Kc(e);var t=e.queue,a=dh.bind(null,Ie,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Wc,useDeferredValue:function(e,t){var a=Tn();return Fc(a,e,t)},useTransition:function(){var e=Kc(!1);return e=ih.bind(null,Ie,e.queue,!0,!1),Tn().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var o=Ie,s=Tn();if(ft){if(a===void 0)throw Error(i(407));a=a()}else{if(a=t(),Ot===null)throw Error(i(349));(it&124)!==0||Bm(o,t,a)}s.memoizedState=a;var f={value:a,getSnapshot:t};return s.queue=f,Zm(Lm.bind(null,o,f,e),[e]),o.flags|=2048,jo(9,vs(),_m.bind(null,o,f,a,t),null),a},useId:function(){var e=Tn(),t=Ot.identifierPrefix;if(ft){var a=Ga,o=Ya;a=(o&~(1<<32-et(o)-1)).toString(32)+a,t="«"+t+"R"+a,a=hs++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=y1++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Jc,useFormState:Xm,useActionState:Xm,useOptimistic:function(e){var t=Tn();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=ef.bind(null,Ie,!0,a),a.dispatch=t,[e,t]},useMemoCache:Yc,useCacheRefresh:function(){return Tn().memoizedState=C1.bind(null,Ie)}},gh={readContext:dn,use:gs,useCallback:rh,useContext:dn,useEffect:Jm,useImperativeHandle:ah,useInsertionEffect:eh,useLayoutEffect:th,useMemo:oh,useReducer:ys,useRef:Fm,useState:function(){return ys(Qa)},useDebugValue:Wc,useDeferredValue:function(e,t){var a=Qt();return lh(a,bt.memoizedState,e,t)},useTransition:function(){var e=ys(Qa)[0],t=Qt().memoizedState;return[typeof e=="boolean"?e:Ul(e),t]},useSyncExternalStore:Nm,useId:ch,useHostTransitionStatus:Jc,useFormState:Km,useActionState:Km,useOptimistic:function(e,t){var a=Qt();return Um(a,bt,e,t)},useMemoCache:Yc,useCacheRefresh:fh},E1={readContext:dn,use:gs,useCallback:rh,useContext:dn,useEffect:Jm,useImperativeHandle:ah,useInsertionEffect:eh,useLayoutEffect:th,useMemo:oh,useReducer:Xc,useRef:Fm,useState:function(){return Xc(Qa)},useDebugValue:Wc,useDeferredValue:function(e,t){var a=Qt();return bt===null?Fc(a,e,t):lh(a,bt.memoizedState,e,t)},useTransition:function(){var e=Xc(Qa)[0],t=Qt().memoizedState;return[typeof e=="boolean"?e:Ul(e),t]},useSyncExternalStore:Nm,useId:ch,useHostTransitionStatus:Jc,useFormState:Wm,useActionState:Wm,useOptimistic:function(e,t){var a=Qt();return bt!==null?Um(a,bt,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Yc,useCacheRefresh:fh},No=null,Vl=0;function Cs(e){var t=Vl;return Vl+=1,No===null&&(No=[]),Mm(No,e,t)}function Yl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Ts(e,t){throw t.$$typeof===S?Error(i(525)):(e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function yh(e){var t=e._init;return t(e._payload)}function vh(e){function t(I,P){if(e){var Q=I.deletions;Q===null?(I.deletions=[P],I.flags|=16):Q.push(P)}}function a(I,P){if(!e)return null;for(;P!==null;)t(I,P),P=P.sibling;return null}function o(I){for(var P=new Map;I!==null;)I.key!==null?P.set(I.key,I):P.set(I.index,I),I=I.sibling;return P}function s(I,P){return I=Va(I,P),I.index=0,I.sibling=null,I}function f(I,P,Q){return I.index=Q,e?(Q=I.alternate,Q!==null?(Q=Q.index,Q<P?(I.flags|=67108866,P):Q):(I.flags|=67108866,P)):(I.flags|=1048576,P)}function g(I){return e&&I.alternate===null&&(I.flags|=67108866),I}function C(I,P,Q,se){return P===null||P.tag!==6?(P=xc(Q,I.mode,se),P.return=I,P):(P=s(P,Q),P.return=I,P)}function j(I,P,Q,se){var Se=Q.type;return Se===R?ie(I,P,Q.props.children,se,Q.key):P!==null&&(P.elementType===Se||typeof Se=="object"&&Se!==null&&Se.$$typeof===G&&yh(Se)===P.type)?(P=s(P,Q.props),Yl(P,Q),P.return=I,P):(P=os(Q.type,Q.key,Q.props,null,I.mode,se),Yl(P,Q),P.return=I,P)}function W(I,P,Q,se){return P===null||P.tag!==4||P.stateNode.containerInfo!==Q.containerInfo||P.stateNode.implementation!==Q.implementation?(P=Cc(Q,I.mode,se),P.return=I,P):(P=s(P,Q.children||[]),P.return=I,P)}function ie(I,P,Q,se,Se){return P===null||P.tag!==7?(P=Yr(Q,I.mode,se,Se),P.return=I,P):(P=s(P,Q),P.return=I,P)}function ce(I,P,Q){if(typeof P=="string"&&P!==""||typeof P=="number"||typeof P=="bigint")return P=xc(""+P,I.mode,Q),P.return=I,P;if(typeof P=="object"&&P!==null){switch(P.$$typeof){case O:return Q=os(P.type,P.key,P.props,null,I.mode,Q),Yl(Q,P),Q.return=I,Q;case w:return P=Cc(P,I.mode,Q),P.return=I,P;case G:var se=P._init;return P=se(P._payload),ce(I,P,Q)}if(L(P)||V(P))return P=Yr(P,I.mode,Q,null),P.return=I,P;if(typeof P.then=="function")return ce(I,Cs(P),Q);if(P.$$typeof===D)return ce(I,us(I,P),Q);Ts(I,P)}return null}function F(I,P,Q,se){var Se=P!==null?P.key:null;if(typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint")return Se!==null?null:C(I,P,""+Q,se);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case O:return Q.key===Se?j(I,P,Q,se):null;case w:return Q.key===Se?W(I,P,Q,se):null;case G:return Se=Q._init,Q=Se(Q._payload),F(I,P,Q,se)}if(L(Q)||V(Q))return Se!==null?null:ie(I,P,Q,se,null);if(typeof Q.then=="function")return F(I,P,Cs(Q),se);if(Q.$$typeof===D)return F(I,P,us(I,Q),se);Ts(I,Q)}return null}function Z(I,P,Q,se,Se){if(typeof se=="string"&&se!==""||typeof se=="number"||typeof se=="bigint")return I=I.get(Q)||null,C(P,I,""+se,Se);if(typeof se=="object"&&se!==null){switch(se.$$typeof){case O:return I=I.get(se.key===null?Q:se.key)||null,j(P,I,se,Se);case w:return I=I.get(se.key===null?Q:se.key)||null,W(P,I,se,Se);case G:var Ge=se._init;return se=Ge(se._payload),Z(I,P,Q,se,Se)}if(L(se)||V(se))return I=I.get(Q)||null,ie(P,I,se,Se,null);if(typeof se.then=="function")return Z(I,P,Q,Cs(se),Se);if(se.$$typeof===D)return Z(I,P,Q,us(P,se),Se);Ts(P,se)}return null}function $e(I,P,Q,se){for(var Se=null,Ge=null,Oe=P,De=P=0,rn=null;Oe!==null&&De<Q.length;De++){Oe.index>De?(rn=Oe,Oe=null):rn=Oe.sibling;var ct=F(I,Oe,Q[De],se);if(ct===null){Oe===null&&(Oe=rn);break}e&&Oe&&ct.alternate===null&&t(I,Oe),P=f(ct,P,De),Ge===null?Se=ct:Ge.sibling=ct,Ge=ct,Oe=rn}if(De===Q.length)return a(I,Oe),ft&&Xr(I,De),Se;if(Oe===null){for(;De<Q.length;De++)Oe=ce(I,Q[De],se),Oe!==null&&(P=f(Oe,P,De),Ge===null?Se=Oe:Ge.sibling=Oe,Ge=Oe);return ft&&Xr(I,De),Se}for(Oe=o(Oe);De<Q.length;De++)rn=Z(Oe,I,De,Q[De],se),rn!==null&&(e&&rn.alternate!==null&&Oe.delete(rn.key===null?De:rn.key),P=f(rn,P,De),Ge===null?Se=rn:Ge.sibling=rn,Ge=rn);return e&&Oe.forEach(function(kr){return t(I,kr)}),ft&&Xr(I,De),Se}function ze(I,P,Q,se){if(Q==null)throw Error(i(151));for(var Se=null,Ge=null,Oe=P,De=P=0,rn=null,ct=Q.next();Oe!==null&&!ct.done;De++,ct=Q.next()){Oe.index>De?(rn=Oe,Oe=null):rn=Oe.sibling;var kr=F(I,Oe,ct.value,se);if(kr===null){Oe===null&&(Oe=rn);break}e&&Oe&&kr.alternate===null&&t(I,Oe),P=f(kr,P,De),Ge===null?Se=kr:Ge.sibling=kr,Ge=kr,Oe=rn}if(ct.done)return a(I,Oe),ft&&Xr(I,De),Se;if(Oe===null){for(;!ct.done;De++,ct=Q.next())ct=ce(I,ct.value,se),ct!==null&&(P=f(ct,P,De),Ge===null?Se=ct:Ge.sibling=ct,Ge=ct);return ft&&Xr(I,De),Se}for(Oe=o(Oe);!ct.done;De++,ct=Q.next())ct=Z(Oe,I,De,ct.value,se),ct!==null&&(e&&ct.alternate!==null&&Oe.delete(ct.key===null?De:ct.key),P=f(ct,P,De),Ge===null?Se=ct:Ge.sibling=ct,Ge=ct);return e&&Oe.forEach(function(RS){return t(I,RS)}),ft&&Xr(I,De),Se}function xt(I,P,Q,se){if(typeof Q=="object"&&Q!==null&&Q.type===R&&Q.key===null&&(Q=Q.props.children),typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case O:e:{for(var Se=Q.key;P!==null;){if(P.key===Se){if(Se=Q.type,Se===R){if(P.tag===7){a(I,P.sibling),se=s(P,Q.props.children),se.return=I,I=se;break e}}else if(P.elementType===Se||typeof Se=="object"&&Se!==null&&Se.$$typeof===G&&yh(Se)===P.type){a(I,P.sibling),se=s(P,Q.props),Yl(se,Q),se.return=I,I=se;break e}a(I,P);break}else t(I,P);P=P.sibling}Q.type===R?(se=Yr(Q.props.children,I.mode,se,Q.key),se.return=I,I=se):(se=os(Q.type,Q.key,Q.props,null,I.mode,se),Yl(se,Q),se.return=I,I=se)}return g(I);case w:e:{for(Se=Q.key;P!==null;){if(P.key===Se)if(P.tag===4&&P.stateNode.containerInfo===Q.containerInfo&&P.stateNode.implementation===Q.implementation){a(I,P.sibling),se=s(P,Q.children||[]),se.return=I,I=se;break e}else{a(I,P);break}else t(I,P);P=P.sibling}se=Cc(Q,I.mode,se),se.return=I,I=se}return g(I);case G:return Se=Q._init,Q=Se(Q._payload),xt(I,P,Q,se)}if(L(Q))return $e(I,P,Q,se);if(V(Q)){if(Se=V(Q),typeof Se!="function")throw Error(i(150));return Q=Se.call(Q),ze(I,P,Q,se)}if(typeof Q.then=="function")return xt(I,P,Cs(Q),se);if(Q.$$typeof===D)return xt(I,P,us(I,Q),se);Ts(I,Q)}return typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint"?(Q=""+Q,P!==null&&P.tag===6?(a(I,P.sibling),se=s(P,Q),se.return=I,I=se):(a(I,P),se=xc(Q,I.mode,se),se.return=I,I=se),g(I)):a(I,P)}return function(I,P,Q,se){try{Vl=0;var Se=xt(I,P,Q,se);return No=null,Se}catch(Oe){if(Oe===Nl||Oe===fs)throw Oe;var Ge=$n(29,Oe,null,I.mode);return Ge.lanes=se,Ge.return=I,Ge}finally{}}}var Bo=vh(!0),bh=vh(!1),ra=U(null),Oa=null;function yr(e){var t=e.alternate;ne(en,en.current&1),ne(ra,e),Oa===null&&(t===null||Do.current!==null||t.memoizedState!==null)&&(Oa=e)}function Sh(e){if(e.tag===22){if(ne(en,en.current),ne(ra,e),Oa===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Oa=e)}}else vr()}function vr(){ne(en,en.current),ne(ra,ra.current)}function Wa(e){J(ra),Oa===e&&(Oa=null),J(en)}var en=U(0);function Es(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Yf(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function tf(e,t,a,o){t=e.memoizedState,a=a(o,t),a=a==null?t:y({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var nf={enqueueSetState:function(e,t,a){e=e._reactInternals;var o=_n(),s=mr(o);s.payload=t,a!=null&&(s.callback=a),t=hr(e,s,o),t!==null&&(Ln(t,e,o),_l(t,e,o))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var o=_n(),s=mr(o);s.tag=1,s.payload=t,a!=null&&(s.callback=a),t=hr(e,s,o),t!==null&&(Ln(t,e,o),_l(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=_n(),o=mr(a);o.tag=2,t!=null&&(o.callback=t),t=hr(e,o,a),t!==null&&(Ln(t,e,a),_l(t,e,a))}};function xh(e,t,a,o,s,f,g){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(o,f,g):t.prototype&&t.prototype.isPureReactComponent?!Ml(a,o)||!Ml(s,f):!0}function Ch(e,t,a,o){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,o),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,o),t.state!==e&&nf.enqueueReplaceState(t,t.state,null)}function eo(e,t){var a=t;if("ref"in t){a={};for(var o in t)o!=="ref"&&(a[o]=t[o])}if(e=e.defaultProps){a===t&&(a=y({},a));for(var s in e)a[s]===void 0&&(a[s]=e[s])}return a}var Rs=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Th(e){Rs(e)}function Eh(e){console.error(e)}function Rh(e){Rs(e)}function Os(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(o){setTimeout(function(){throw o})}}function Oh(e,t,a){try{var o=e.onCaughtError;o(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function af(e,t,a){return a=mr(a),a.tag=3,a.payload={element:null},a.callback=function(){Os(e,t)},a}function Mh(e){return e=mr(e),e.tag=3,e}function wh(e,t,a,o){var s=a.type.getDerivedStateFromError;if(typeof s=="function"){var f=o.value;e.payload=function(){return s(f)},e.callback=function(){Oh(t,a,o)}}var g=a.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(e.callback=function(){Oh(t,a,o),typeof s!="function"&&(Er===null?Er=new Set([this]):Er.add(this));var C=o.stack;this.componentDidCatch(o.value,{componentStack:C!==null?C:""})})}function R1(e,t,a,o,s){if(a.flags|=32768,o!==null&&typeof o=="object"&&typeof o.then=="function"){if(t=a.alternate,t!==null&&kl(t,a,s,!0),a=ra.current,a!==null){switch(a.tag){case 13:return Oa===null?wf():a.alternate===null&&Lt===0&&(Lt=3),a.flags&=-257,a.flags|=65536,a.lanes=s,o===kc?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([o]):t.add(o),zf(e,o,s)),!1;case 22:return a.flags|=65536,o===kc?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([o])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([o]):a.add(o)),zf(e,o,s)),!1}throw Error(i(435,a.tag))}return zf(e,o,s),wf(),!1}if(ft)return t=ra.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=s,o!==Rc&&(e=Error(i(422),{cause:o}),Dl(ea(e,a)))):(o!==Rc&&(t=Error(i(423),{cause:o}),Dl(ea(t,a))),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,o=ea(o,a),s=af(e.stateNode,o,s),Nc(e,s),Lt!==4&&(Lt=2)),!1;var f=Error(i(520),{cause:o});if(f=ea(f,a),Zl===null?Zl=[f]:Zl.push(f),Lt!==4&&(Lt=2),t===null)return!0;o=ea(o,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=s&-s,a.lanes|=e,e=af(a.stateNode,o,e),Nc(a,e),!1;case 1:if(t=a.type,f=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Er===null||!Er.has(f))))return a.flags|=65536,s&=-s,a.lanes|=s,s=Mh(s),wh(s,e,a,o),Nc(a,s),!1}a=a.return}while(a!==null);return!1}var Ah=Error(i(461)),nn=!1;function on(e,t,a,o){t.child=e===null?bh(t,null,a,o):Bo(t,e.child,a,o)}function zh(e,t,a,o,s){a=a.render;var f=t.ref;if("ref"in o){var g={};for(var C in o)C!=="ref"&&(g[C]=o[C])}else g=o;return Fr(t),o=Pc(e,t,a,g,f,s),C=Uc(),e!==null&&!nn?(Ic(e,t,s),Fa(e,t,s)):(ft&&C&&Tc(t),t.flags|=1,on(e,t,o,s),t.child)}function Dh(e,t,a,o,s){if(e===null){var f=a.type;return typeof f=="function"&&!Sc(f)&&f.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=f,kh(e,t,f,o,s)):(e=os(a.type,null,o,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(f=e.child,!df(e,s)){var g=f.memoizedProps;if(a=a.compare,a=a!==null?a:Ml,a(g,o)&&e.ref===t.ref)return Fa(e,t,s)}return t.flags|=1,e=Va(f,o),e.ref=t.ref,e.return=t,t.child=e}function kh(e,t,a,o,s){if(e!==null){var f=e.memoizedProps;if(Ml(f,o)&&e.ref===t.ref)if(nn=!1,t.pendingProps=o=f,df(e,s))(e.flags&131072)!==0&&(nn=!0);else return t.lanes=e.lanes,Fa(e,t,s)}return rf(e,t,a,o,s)}function $h(e,t,a){var o=t.pendingProps,s=o.children,f=e!==null?e.memoizedState:null;if(o.mode==="hidden"){if((t.flags&128)!==0){if(o=f!==null?f.baseLanes|a:a,e!==null){for(s=t.child=e.child,f=0;s!==null;)f=f|s.lanes|s.childLanes,s=s.sibling;t.childLanes=f&~o}else t.childLanes=0,t.child=null;return jh(e,t,o,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&cs(t,f!==null?f.cachePool:null),f!==null?km(t,f):_c(),Sh(t);else return t.lanes=t.childLanes=536870912,jh(e,t,f!==null?f.baseLanes|a:a,a)}else f!==null?(cs(t,f.cachePool),km(t,f),vr(),t.memoizedState=null):(e!==null&&cs(t,null),_c(),vr());return on(e,t,s,a),t.child}function jh(e,t,a,o){var s=Dc();return s=s===null?null:{parent:Jt._currentValue,pool:s},t.memoizedState={baseLanes:a,cachePool:s},e!==null&&cs(t,null),_c(),Sh(t),e!==null&&kl(e,t,o,!0),null}function Ms(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(i(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function rf(e,t,a,o,s){return Fr(t),a=Pc(e,t,a,o,void 0,s),o=Uc(),e!==null&&!nn?(Ic(e,t,s),Fa(e,t,s)):(ft&&o&&Tc(t),t.flags|=1,on(e,t,a,s),t.child)}function Nh(e,t,a,o,s,f){return Fr(t),t.updateQueue=null,a=jm(t,o,a,s),$m(e),o=Uc(),e!==null&&!nn?(Ic(e,t,f),Fa(e,t,f)):(ft&&o&&Tc(t),t.flags|=1,on(e,t,a,f),t.child)}function Bh(e,t,a,o,s){if(Fr(t),t.stateNode===null){var f=Oo,g=a.contextType;typeof g=="object"&&g!==null&&(f=dn(g)),f=new a(o,f),t.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=nf,t.stateNode=f,f._reactInternals=t,f=t.stateNode,f.props=o,f.state=t.memoizedState,f.refs={},$c(t),g=a.contextType,f.context=typeof g=="object"&&g!==null?dn(g):Oo,f.state=t.memoizedState,g=a.getDerivedStateFromProps,typeof g=="function"&&(tf(t,a,g,o),f.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(g=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),g!==f.state&&nf.enqueueReplaceState(f,f.state,null),Hl(t,o,f,s),Ll(),f.state=t.memoizedState),typeof f.componentDidMount=="function"&&(t.flags|=4194308),o=!0}else if(e===null){f=t.stateNode;var C=t.memoizedProps,j=eo(a,C);f.props=j;var W=f.context,ie=a.contextType;g=Oo,typeof ie=="object"&&ie!==null&&(g=dn(ie));var ce=a.getDerivedStateFromProps;ie=typeof ce=="function"||typeof f.getSnapshotBeforeUpdate=="function",C=t.pendingProps!==C,ie||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(C||W!==g)&&Ch(t,f,o,g),pr=!1;var F=t.memoizedState;f.state=F,Hl(t,o,f,s),Ll(),W=t.memoizedState,C||F!==W||pr?(typeof ce=="function"&&(tf(t,a,ce,o),W=t.memoizedState),(j=pr||xh(t,a,j,o,F,W,g))?(ie||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(t.flags|=4194308)):(typeof f.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=o,t.memoizedState=W),f.props=o,f.state=W,f.context=g,o=j):(typeof f.componentDidMount=="function"&&(t.flags|=4194308),o=!1)}else{f=t.stateNode,jc(e,t),g=t.memoizedProps,ie=eo(a,g),f.props=ie,ce=t.pendingProps,F=f.context,W=a.contextType,j=Oo,typeof W=="object"&&W!==null&&(j=dn(W)),C=a.getDerivedStateFromProps,(W=typeof C=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==ce||F!==j)&&Ch(t,f,o,j),pr=!1,F=t.memoizedState,f.state=F,Hl(t,o,f,s),Ll();var Z=t.memoizedState;g!==ce||F!==Z||pr||e!==null&&e.dependencies!==null&&ss(e.dependencies)?(typeof C=="function"&&(tf(t,a,C,o),Z=t.memoizedState),(ie=pr||xh(t,a,ie,o,F,Z,j)||e!==null&&e.dependencies!==null&&ss(e.dependencies))?(W||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(o,Z,j),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(o,Z,j)),typeof f.componentDidUpdate=="function"&&(t.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&F===e.memoizedState||(t.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&F===e.memoizedState||(t.flags|=1024),t.memoizedProps=o,t.memoizedState=Z),f.props=o,f.state=Z,f.context=j,o=ie):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&F===e.memoizedState||(t.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&F===e.memoizedState||(t.flags|=1024),o=!1)}return f=o,Ms(e,t),o=(t.flags&128)!==0,f||o?(f=t.stateNode,a=o&&typeof a.getDerivedStateFromError!="function"?null:f.render(),t.flags|=1,e!==null&&o?(t.child=Bo(t,e.child,null,s),t.child=Bo(t,null,a,s)):on(e,t,a,s),t.memoizedState=f.state,e=t.child):e=Fa(e,t,s),e}function _h(e,t,a,o){return zl(),t.flags|=256,on(e,t,a,o),t.child}var of={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function lf(e){return{baseLanes:e,cachePool:Em()}}function sf(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=oa),e}function Lh(e,t,a){var o=t.pendingProps,s=!1,f=(t.flags&128)!==0,g;if((g=f)||(g=e!==null&&e.memoizedState===null?!1:(en.current&2)!==0),g&&(s=!0,t.flags&=-129),g=(t.flags&32)!==0,t.flags&=-33,e===null){if(ft){if(s?yr(t):vr(),ft){var C=_t,j;if(j=C){e:{for(j=C,C=Ra;j.nodeType!==8;){if(!C){C=null;break e}if(j=xa(j.nextSibling),j===null){C=null;break e}}C=j}C!==null?(t.memoizedState={dehydrated:C,treeContext:Gr!==null?{id:Ya,overflow:Ga}:null,retryLane:536870912,hydrationErrors:null},j=$n(18,null,null,0),j.stateNode=C,j.return=t,t.child=j,bn=t,_t=null,j=!0):j=!1}j||Qr(t)}if(C=t.memoizedState,C!==null&&(C=C.dehydrated,C!==null))return Yf(C)?t.lanes=32:t.lanes=536870912,null;Wa(t)}return C=o.children,o=o.fallback,s?(vr(),s=t.mode,C=ws({mode:"hidden",children:C},s),o=Yr(o,s,a,null),C.return=t,o.return=t,C.sibling=o,t.child=C,s=t.child,s.memoizedState=lf(a),s.childLanes=sf(e,g,a),t.memoizedState=of,o):(yr(t),uf(t,C))}if(j=e.memoizedState,j!==null&&(C=j.dehydrated,C!==null)){if(f)t.flags&256?(yr(t),t.flags&=-257,t=cf(e,t,a)):t.memoizedState!==null?(vr(),t.child=e.child,t.flags|=128,t=null):(vr(),s=o.fallback,C=t.mode,o=ws({mode:"visible",children:o.children},C),s=Yr(s,C,a,null),s.flags|=2,o.return=t,s.return=t,o.sibling=s,t.child=o,Bo(t,e.child,null,a),o=t.child,o.memoizedState=lf(a),o.childLanes=sf(e,g,a),t.memoizedState=of,t=s);else if(yr(t),Yf(C)){if(g=C.nextSibling&&C.nextSibling.dataset,g)var W=g.dgst;g=W,o=Error(i(419)),o.stack="",o.digest=g,Dl({value:o,source:null,stack:null}),t=cf(e,t,a)}else if(nn||kl(e,t,a,!1),g=(a&e.childLanes)!==0,nn||g){if(g=Ot,g!==null&&(o=a&-a,o=(o&42)!==0?1:ur(o),o=(o&(g.suspendedLanes|a))!==0?0:o,o!==0&&o!==j.retryLane))throw j.retryLane=o,Ro(e,o),Ln(g,e,o),Ah;C.data==="$?"||wf(),t=cf(e,t,a)}else C.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=j.treeContext,_t=xa(C.nextSibling),bn=t,ft=!0,Kr=null,Ra=!1,e!==null&&(na[aa++]=Ya,na[aa++]=Ga,na[aa++]=Gr,Ya=e.id,Ga=e.overflow,Gr=t),t=uf(t,o.children),t.flags|=4096);return t}return s?(vr(),s=o.fallback,C=t.mode,j=e.child,W=j.sibling,o=Va(j,{mode:"hidden",children:o.children}),o.subtreeFlags=j.subtreeFlags&65011712,W!==null?s=Va(W,s):(s=Yr(s,C,a,null),s.flags|=2),s.return=t,o.return=t,o.sibling=s,t.child=o,o=s,s=t.child,C=e.child.memoizedState,C===null?C=lf(a):(j=C.cachePool,j!==null?(W=Jt._currentValue,j=j.parent!==W?{parent:W,pool:W}:j):j=Em(),C={baseLanes:C.baseLanes|a,cachePool:j}),s.memoizedState=C,s.childLanes=sf(e,g,a),t.memoizedState=of,o):(yr(t),a=e.child,e=a.sibling,a=Va(a,{mode:"visible",children:o.children}),a.return=t,a.sibling=null,e!==null&&(g=t.deletions,g===null?(t.deletions=[e],t.flags|=16):g.push(e)),t.child=a,t.memoizedState=null,a)}function uf(e,t){return t=ws({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ws(e,t){return e=$n(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function cf(e,t,a){return Bo(t,e.child,null,a),e=uf(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Hh(e,t,a){e.lanes|=t;var o=e.alternate;o!==null&&(o.lanes|=t),Mc(e.return,t,a)}function ff(e,t,a,o,s){var f=e.memoizedState;f===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:o,tail:a,tailMode:s}:(f.isBackwards=t,f.rendering=null,f.renderingStartTime=0,f.last=o,f.tail=a,f.tailMode=s)}function Ph(e,t,a){var o=t.pendingProps,s=o.revealOrder,f=o.tail;if(on(e,t,o.children,a),o=en.current,(o&2)!==0)o=o&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Hh(e,a,t);else if(e.tag===19)Hh(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}switch(ne(en,o),s){case"forwards":for(a=t.child,s=null;a!==null;)e=a.alternate,e!==null&&Es(e)===null&&(s=a),a=a.sibling;a=s,a===null?(s=t.child,t.child=null):(s=a.sibling,a.sibling=null),ff(t,!1,s,a,f);break;case"backwards":for(a=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Es(e)===null){t.child=s;break}e=s.sibling,s.sibling=a,a=s,s=e}ff(t,!0,a,null,f);break;case"together":ff(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Fa(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Tr|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(kl(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,a=Va(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Va(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function df(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ss(e)))}function O1(e,t,a){switch(t.tag){case 3:xe(t,t.stateNode.containerInfo),dr(t,Jt,e.memoizedState.cache),zl();break;case 27:case 5:me(t);break;case 4:xe(t,t.stateNode.containerInfo);break;case 10:dr(t,t.type,t.memoizedProps.value);break;case 13:var o=t.memoizedState;if(o!==null)return o.dehydrated!==null?(yr(t),t.flags|=128,null):(a&t.child.childLanes)!==0?Lh(e,t,a):(yr(t),e=Fa(e,t,a),e!==null?e.sibling:null);yr(t);break;case 19:var s=(e.flags&128)!==0;if(o=(a&t.childLanes)!==0,o||(kl(e,t,a,!1),o=(a&t.childLanes)!==0),s){if(o)return Ph(e,t,a);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),ne(en,en.current),o)break;return null;case 22:case 23:return t.lanes=0,$h(e,t,a);case 24:dr(t,Jt,e.memoizedState.cache)}return Fa(e,t,a)}function Uh(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)nn=!0;else{if(!df(e,a)&&(t.flags&128)===0)return nn=!1,O1(e,t,a);nn=(e.flags&131072)!==0}else nn=!1,ft&&(t.flags&1048576)!==0&&ym(t,is,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var o=t.elementType,s=o._init;if(o=s(o._payload),t.type=o,typeof o=="function")Sc(o)?(e=eo(o,e),t.tag=1,t=Bh(null,t,o,e,a)):(t.tag=0,t=rf(null,t,o,e,a));else{if(o!=null){if(s=o.$$typeof,s===k){t.tag=11,t=zh(null,t,o,e,a);break e}else if(s===q){t.tag=14,t=Dh(null,t,o,e,a);break e}}throw t=ee(o)||o,Error(i(306,t,""))}}return t;case 0:return rf(e,t,t.type,t.pendingProps,a);case 1:return o=t.type,s=eo(o,t.pendingProps),Bh(e,t,o,s,a);case 3:e:{if(xe(t,t.stateNode.containerInfo),e===null)throw Error(i(387));o=t.pendingProps;var f=t.memoizedState;s=f.element,jc(e,t),Hl(t,o,null,a);var g=t.memoizedState;if(o=g.cache,dr(t,Jt,o),o!==f.cache&&wc(t,[Jt],a,!0),Ll(),o=g.element,f.isDehydrated)if(f={element:o,isDehydrated:!1,cache:g.cache},t.updateQueue.baseState=f,t.memoizedState=f,t.flags&256){t=_h(e,t,o,a);break e}else if(o!==s){s=ea(Error(i(424)),t),Dl(s),t=_h(e,t,o,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(_t=xa(e.firstChild),bn=t,ft=!0,Kr=null,Ra=!0,a=bh(t,null,o,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(zl(),o===s){t=Fa(e,t,a);break e}on(e,t,o,a)}t=t.child}return t;case 26:return Ms(e,t),e===null?(a=Yg(t.type,null,t.pendingProps,null))?t.memoizedState=a:ft||(a=t.type,e=t.pendingProps,o=Is(le.current).createElement(a),o[zt]=t,o[Xt]=e,sn(o,a,e),te(o),t.stateNode=o):t.memoizedState=Yg(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return me(t),e===null&&ft&&(o=t.stateNode=Ig(t.type,t.pendingProps,le.current),bn=t,Ra=!0,s=_t,Mr(t.type)?(Gf=s,_t=xa(o.firstChild)):_t=s),on(e,t,t.pendingProps.children,a),Ms(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&ft&&((s=o=_t)&&(o=eS(o,t.type,t.pendingProps,Ra),o!==null?(t.stateNode=o,bn=t,_t=xa(o.firstChild),Ra=!1,s=!0):s=!1),s||Qr(t)),me(t),s=t.type,f=t.pendingProps,g=e!==null?e.memoizedProps:null,o=f.children,If(s,f)?o=null:g!==null&&If(s,g)&&(t.flags|=32),t.memoizedState!==null&&(s=Pc(e,t,v1,null,null,a),ii._currentValue=s),Ms(e,t),on(e,t,o,a),t.child;case 6:return e===null&&ft&&((e=a=_t)&&(a=tS(a,t.pendingProps,Ra),a!==null?(t.stateNode=a,bn=t,_t=null,e=!0):e=!1),e||Qr(t)),null;case 13:return Lh(e,t,a);case 4:return xe(t,t.stateNode.containerInfo),o=t.pendingProps,e===null?t.child=Bo(t,null,o,a):on(e,t,o,a),t.child;case 11:return zh(e,t,t.type,t.pendingProps,a);case 7:return on(e,t,t.pendingProps,a),t.child;case 8:return on(e,t,t.pendingProps.children,a),t.child;case 12:return on(e,t,t.pendingProps.children,a),t.child;case 10:return o=t.pendingProps,dr(t,t.type,o.value),on(e,t,o.children,a),t.child;case 9:return s=t.type._context,o=t.pendingProps.children,Fr(t),s=dn(s),o=o(s),t.flags|=1,on(e,t,o,a),t.child;case 14:return Dh(e,t,t.type,t.pendingProps,a);case 15:return kh(e,t,t.type,t.pendingProps,a);case 19:return Ph(e,t,a);case 31:return o=t.pendingProps,a=t.mode,o={mode:o.mode,children:o.children},e===null?(a=ws(o,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Va(e.child,o),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return $h(e,t,a);case 24:return Fr(t),o=dn(Jt),e===null?(s=Dc(),s===null&&(s=Ot,f=Ac(),s.pooledCache=f,f.refCount++,f!==null&&(s.pooledCacheLanes|=a),s=f),t.memoizedState={parent:o,cache:s},$c(t),dr(t,Jt,s)):((e.lanes&a)!==0&&(jc(e,t),Hl(t,null,null,a),Ll()),s=e.memoizedState,f=t.memoizedState,s.parent!==o?(s={parent:o,cache:o},t.memoizedState=s,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=s),dr(t,Jt,o)):(o=f.cache,dr(t,Jt,o),o!==s.cache&&wc(t,[Jt],a,!0))),on(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function Za(e){e.flags|=4}function Ih(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Wg(t)){if(t=ra.current,t!==null&&((it&4194048)===it?Oa!==null:(it&62914560)!==it&&(it&536870912)===0||t!==Oa))throw Bl=kc,Rm;e.flags|=8192}}function As(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?po():536870912,e.lanes|=t,Po|=t)}function Gl(e,t){if(!ft)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var o=null;a!==null;)a.alternate!==null&&(o=a),a=a.sibling;o===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:o.sibling=null}}function Nt(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,o=0;if(t)for(var s=e.child;s!==null;)a|=s.lanes|s.childLanes,o|=s.subtreeFlags&65011712,o|=s.flags&65011712,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)a|=s.lanes|s.childLanes,o|=s.subtreeFlags,o|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=o,e.childLanes=a,t}function M1(e,t,a){var o=t.pendingProps;switch(Ec(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Nt(t),null;case 1:return Nt(t),null;case 3:return a=t.stateNode,o=null,e!==null&&(o=e.memoizedState.cache),t.memoizedState.cache!==o&&(t.flags|=2048),Ka(Jt),be(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Al(t)?Za(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Sm())),Nt(t),null;case 26:return a=t.memoizedState,e===null?(Za(t),a!==null?(Nt(t),Ih(t,a)):(Nt(t),t.flags&=-16777217)):a?a!==e.memoizedState?(Za(t),Nt(t),Ih(t,a)):(Nt(t),t.flags&=-16777217):(e.memoizedProps!==o&&Za(t),Nt(t),t.flags&=-16777217),null;case 27:Re(t),a=le.current;var s=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==o&&Za(t);else{if(!o){if(t.stateNode===null)throw Error(i(166));return Nt(t),null}e=fe.current,Al(t)?vm(t):(e=Ig(s,o,a),t.stateNode=e,Za(t))}return Nt(t),null;case 5:if(Re(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==o&&Za(t);else{if(!o){if(t.stateNode===null)throw Error(i(166));return Nt(t),null}if(e=fe.current,Al(t))vm(t);else{switch(s=Is(le.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof o.is=="string"?s.createElement("select",{is:o.is}):s.createElement("select"),o.multiple?e.multiple=!0:o.size&&(e.size=o.size);break;default:e=typeof o.is=="string"?s.createElement(a,{is:o.is}):s.createElement(a)}}e[zt]=t,e[Xt]=o;e:for(s=t.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===t)break e;for(;s.sibling===null;){if(s.return===null||s.return===t)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}t.stateNode=e;e:switch(sn(e,a,o),a){case"button":case"input":case"select":case"textarea":e=!!o.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Za(t)}}return Nt(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==o&&Za(t);else{if(typeof o!="string"&&t.stateNode===null)throw Error(i(166));if(e=le.current,Al(t)){if(e=t.stateNode,a=t.memoizedProps,o=null,s=bn,s!==null)switch(s.tag){case 27:case 5:o=s.memoizedProps}e[zt]=t,e=!!(e.nodeValue===a||o!==null&&o.suppressHydrationWarning===!0||Ng(e.nodeValue,a)),e||Qr(t)}else e=Is(e).createTextNode(o),e[zt]=t,t.stateNode=e}return Nt(t),null;case 13:if(o=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(s=Al(t),o!==null&&o.dehydrated!==null){if(e===null){if(!s)throw Error(i(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(i(317));s[zt]=t}else zl(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Nt(t),s=!1}else s=Sm(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return t.flags&256?(Wa(t),t):(Wa(t),null)}if(Wa(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=o!==null,e=e!==null&&e.memoizedState!==null,a){o=t.child,s=null,o.alternate!==null&&o.alternate.memoizedState!==null&&o.alternate.memoizedState.cachePool!==null&&(s=o.alternate.memoizedState.cachePool.pool);var f=null;o.memoizedState!==null&&o.memoizedState.cachePool!==null&&(f=o.memoizedState.cachePool.pool),f!==s&&(o.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),As(t,t.updateQueue),Nt(t),null;case 4:return be(),e===null&&_f(t.stateNode.containerInfo),Nt(t),null;case 10:return Ka(t.type),Nt(t),null;case 19:if(J(en),s=t.memoizedState,s===null)return Nt(t),null;if(o=(t.flags&128)!==0,f=s.rendering,f===null)if(o)Gl(s,!1);else{if(Lt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(f=Es(e),f!==null){for(t.flags|=128,Gl(s,!1),e=f.updateQueue,t.updateQueue=e,As(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)gm(a,e),a=a.sibling;return ne(en,en.current&1|2),t.child}e=e.sibling}s.tail!==null&&at()>ks&&(t.flags|=128,o=!0,Gl(s,!1),t.lanes=4194304)}else{if(!o)if(e=Es(f),e!==null){if(t.flags|=128,o=!0,e=e.updateQueue,t.updateQueue=e,As(t,e),Gl(s,!0),s.tail===null&&s.tailMode==="hidden"&&!f.alternate&&!ft)return Nt(t),null}else 2*at()-s.renderingStartTime>ks&&a!==536870912&&(t.flags|=128,o=!0,Gl(s,!1),t.lanes=4194304);s.isBackwards?(f.sibling=t.child,t.child=f):(e=s.last,e!==null?e.sibling=f:t.child=f,s.last=f)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=at(),t.sibling=null,e=en.current,ne(en,o?e&1|2:e&1),t):(Nt(t),null);case 22:case 23:return Wa(t),Lc(),o=t.memoizedState!==null,e!==null?e.memoizedState!==null!==o&&(t.flags|=8192):o&&(t.flags|=8192),o?(a&536870912)!==0&&(t.flags&128)===0&&(Nt(t),t.subtreeFlags&6&&(t.flags|=8192)):Nt(t),a=t.updateQueue,a!==null&&As(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),o=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(o=t.memoizedState.cachePool.pool),o!==a&&(t.flags|=2048),e!==null&&J(Zr),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Ka(Jt),Nt(t),null;case 25:return null;case 30:return null}throw Error(i(156,t.tag))}function w1(e,t){switch(Ec(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ka(Jt),be(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Re(t),null;case 13:if(Wa(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));zl()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(en),null;case 4:return be(),null;case 10:return Ka(t.type),null;case 22:case 23:return Wa(t),Lc(),e!==null&&J(Zr),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Ka(Jt),null;case 25:return null;default:return null}}function qh(e,t){switch(Ec(t),t.tag){case 3:Ka(Jt),be();break;case 26:case 27:case 5:Re(t);break;case 4:be();break;case 13:Wa(t);break;case 19:J(en);break;case 10:Ka(t.type);break;case 22:case 23:Wa(t),Lc(),e!==null&&J(Zr);break;case 24:Ka(Jt)}}function Xl(e,t){try{var a=t.updateQueue,o=a!==null?a.lastEffect:null;if(o!==null){var s=o.next;a=s;do{if((a.tag&e)===e){o=void 0;var f=a.create,g=a.inst;o=f(),g.destroy=o}a=a.next}while(a!==s)}}catch(C){Tt(t,t.return,C)}}function br(e,t,a){try{var o=t.updateQueue,s=o!==null?o.lastEffect:null;if(s!==null){var f=s.next;o=f;do{if((o.tag&e)===e){var g=o.inst,C=g.destroy;if(C!==void 0){g.destroy=void 0,s=t;var j=a,W=C;try{W()}catch(ie){Tt(s,j,ie)}}}o=o.next}while(o!==f)}}catch(ie){Tt(t,t.return,ie)}}function Vh(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Dm(t,a)}catch(o){Tt(e,e.return,o)}}}function Yh(e,t,a){a.props=eo(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(o){Tt(e,t,o)}}function Kl(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var o=e.stateNode;break;case 30:o=e.stateNode;break;default:o=e.stateNode}typeof a=="function"?e.refCleanup=a(o):a.current=o}}catch(s){Tt(e,t,s)}}function Ma(e,t){var a=e.ref,o=e.refCleanup;if(a!==null)if(typeof o=="function")try{o()}catch(s){Tt(e,t,s)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(s){Tt(e,t,s)}else a.current=null}function Gh(e){var t=e.type,a=e.memoizedProps,o=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&o.focus();break e;case"img":a.src?o.src=a.src:a.srcSet&&(o.srcset=a.srcSet)}}catch(s){Tt(e,e.return,s)}}function pf(e,t,a){try{var o=e.stateNode;Q1(o,e.type,a,t),o[Xt]=t}catch(s){Tt(e,e.return,s)}}function Xh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Mr(e.type)||e.tag===4}function mf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Xh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Mr(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hf(e,t,a){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Us));else if(o!==4&&(o===27&&Mr(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(hf(e,t,a),e=e.sibling;e!==null;)hf(e,t,a),e=e.sibling}function zs(e,t,a){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(o!==4&&(o===27&&Mr(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(zs(e,t,a),e=e.sibling;e!==null;)zs(e,t,a),e=e.sibling}function Kh(e){var t=e.stateNode,a=e.memoizedProps;try{for(var o=e.type,s=t.attributes;s.length;)t.removeAttributeNode(s[0]);sn(t,o,a),t[zt]=e,t[Xt]=a}catch(f){Tt(e,e.return,f)}}var Ja=!1,Vt=!1,gf=!1,Qh=typeof WeakSet=="function"?WeakSet:Set,an=null;function A1(e,t){if(e=e.containerInfo,Pf=Ks,e=lm(e),pc(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var o=a.getSelection&&a.getSelection();if(o&&o.rangeCount!==0){a=o.anchorNode;var s=o.anchorOffset,f=o.focusNode;o=o.focusOffset;try{a.nodeType,f.nodeType}catch{a=null;break e}var g=0,C=-1,j=-1,W=0,ie=0,ce=e,F=null;t:for(;;){for(var Z;ce!==a||s!==0&&ce.nodeType!==3||(C=g+s),ce!==f||o!==0&&ce.nodeType!==3||(j=g+o),ce.nodeType===3&&(g+=ce.nodeValue.length),(Z=ce.firstChild)!==null;)F=ce,ce=Z;for(;;){if(ce===e)break t;if(F===a&&++W===s&&(C=g),F===f&&++ie===o&&(j=g),(Z=ce.nextSibling)!==null)break;ce=F,F=ce.parentNode}ce=Z}a=C===-1||j===-1?null:{start:C,end:j}}else a=null}a=a||{start:0,end:0}}else a=null;for(Uf={focusedElem:e,selectionRange:a},Ks=!1,an=t;an!==null;)if(t=an,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,an=e;else for(;an!==null;){switch(t=an,f=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&f!==null){e=void 0,a=t,s=f.memoizedProps,f=f.memoizedState,o=a.stateNode;try{var $e=eo(a.type,s,a.elementType===a.type);e=o.getSnapshotBeforeUpdate($e,f),o.__reactInternalSnapshotBeforeUpdate=e}catch(ze){Tt(a,a.return,ze)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Vf(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Vf(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(i(163))}if(e=t.sibling,e!==null){e.return=t.return,an=e;break}an=t.return}}function Wh(e,t,a){var o=a.flags;switch(a.tag){case 0:case 11:case 15:Sr(e,a),o&4&&Xl(5,a);break;case 1:if(Sr(e,a),o&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(g){Tt(a,a.return,g)}else{var s=eo(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(s,t,e.__reactInternalSnapshotBeforeUpdate)}catch(g){Tt(a,a.return,g)}}o&64&&Vh(a),o&512&&Kl(a,a.return);break;case 3:if(Sr(e,a),o&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Dm(e,t)}catch(g){Tt(a,a.return,g)}}break;case 27:t===null&&o&4&&Kh(a);case 26:case 5:Sr(e,a),t===null&&o&4&&Gh(a),o&512&&Kl(a,a.return);break;case 12:Sr(e,a);break;case 13:Sr(e,a),o&4&&Jh(e,a),o&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=L1.bind(null,a),nS(e,a))));break;case 22:if(o=a.memoizedState!==null||Ja,!o){t=t!==null&&t.memoizedState!==null||Vt,s=Ja;var f=Vt;Ja=o,(Vt=t)&&!f?xr(e,a,(a.subtreeFlags&8772)!==0):Sr(e,a),Ja=s,Vt=f}break;case 30:break;default:Sr(e,a)}}function Fh(e){var t=e.alternate;t!==null&&(e.alternate=null,Fh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Bt(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var kt=null,En=!1;function er(e,t,a){for(a=a.child;a!==null;)Zh(e,t,a),a=a.sibling}function Zh(e,t,a){if(je&&typeof je.onCommitFiberUnmount=="function")try{je.onCommitFiberUnmount(rt,a)}catch{}switch(a.tag){case 26:Vt||Ma(a,t),er(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Vt||Ma(a,t);var o=kt,s=En;Mr(a.type)&&(kt=a.stateNode,En=!1),er(e,t,a),ai(a.stateNode),kt=o,En=s;break;case 5:Vt||Ma(a,t);case 6:if(o=kt,s=En,kt=null,er(e,t,a),kt=o,En=s,kt!==null)if(En)try{(kt.nodeType===9?kt.body:kt.nodeName==="HTML"?kt.ownerDocument.body:kt).removeChild(a.stateNode)}catch(f){Tt(a,t,f)}else try{kt.removeChild(a.stateNode)}catch(f){Tt(a,t,f)}break;case 18:kt!==null&&(En?(e=kt,Pg(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),fi(e)):Pg(kt,a.stateNode));break;case 4:o=kt,s=En,kt=a.stateNode.containerInfo,En=!0,er(e,t,a),kt=o,En=s;break;case 0:case 11:case 14:case 15:Vt||br(2,a,t),Vt||br(4,a,t),er(e,t,a);break;case 1:Vt||(Ma(a,t),o=a.stateNode,typeof o.componentWillUnmount=="function"&&Yh(a,t,o)),er(e,t,a);break;case 21:er(e,t,a);break;case 22:Vt=(o=Vt)||a.memoizedState!==null,er(e,t,a),Vt=o;break;default:er(e,t,a)}}function Jh(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{fi(e)}catch(a){Tt(t,t.return,a)}}function z1(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Qh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Qh),t;default:throw Error(i(435,e.tag))}}function yf(e,t){var a=z1(e);t.forEach(function(o){var s=H1.bind(null,e,o);a.has(o)||(a.add(o),o.then(s,s))})}function jn(e,t){var a=t.deletions;if(a!==null)for(var o=0;o<a.length;o++){var s=a[o],f=e,g=t,C=g;e:for(;C!==null;){switch(C.tag){case 27:if(Mr(C.type)){kt=C.stateNode,En=!1;break e}break;case 5:kt=C.stateNode,En=!1;break e;case 3:case 4:kt=C.stateNode.containerInfo,En=!0;break e}C=C.return}if(kt===null)throw Error(i(160));Zh(f,g,s),kt=null,En=!1,f=s.alternate,f!==null&&(f.return=null),s.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)eg(t,e),t=t.sibling}var Sa=null;function eg(e,t){var a=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:jn(t,e),Nn(e),o&4&&(br(3,e,e.return),Xl(3,e),br(5,e,e.return));break;case 1:jn(t,e),Nn(e),o&512&&(Vt||a===null||Ma(a,a.return)),o&64&&Ja&&(e=e.updateQueue,e!==null&&(o=e.callbacks,o!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?o:a.concat(o))));break;case 26:var s=Sa;if(jn(t,e),Nn(e),o&512&&(Vt||a===null||Ma(a,a.return)),o&4){var f=a!==null?a.memoizedState:null;if(o=e.memoizedState,a===null)if(o===null)if(e.stateNode===null){e:{o=e.type,a=e.memoizedProps,s=s.ownerDocument||s;t:switch(o){case"title":f=s.getElementsByTagName("title")[0],(!f||f[Qn]||f[zt]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=s.createElement(o),s.head.insertBefore(f,s.querySelector("head > title"))),sn(f,o,a),f[zt]=e,te(f),o=f;break e;case"link":var g=Kg("link","href",s).get(o+(a.href||""));if(g){for(var C=0;C<g.length;C++)if(f=g[C],f.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&f.getAttribute("rel")===(a.rel==null?null:a.rel)&&f.getAttribute("title")===(a.title==null?null:a.title)&&f.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){g.splice(C,1);break t}}f=s.createElement(o),sn(f,o,a),s.head.appendChild(f);break;case"meta":if(g=Kg("meta","content",s).get(o+(a.content||""))){for(C=0;C<g.length;C++)if(f=g[C],f.getAttribute("content")===(a.content==null?null:""+a.content)&&f.getAttribute("name")===(a.name==null?null:a.name)&&f.getAttribute("property")===(a.property==null?null:a.property)&&f.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&f.getAttribute("charset")===(a.charSet==null?null:a.charSet)){g.splice(C,1);break t}}f=s.createElement(o),sn(f,o,a),s.head.appendChild(f);break;default:throw Error(i(468,o))}f[zt]=e,te(f),o=f}e.stateNode=o}else Qg(s,e.type,e.stateNode);else e.stateNode=Xg(s,o,e.memoizedProps);else f!==o?(f===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):f.count--,o===null?Qg(s,e.type,e.stateNode):Xg(s,o,e.memoizedProps)):o===null&&e.stateNode!==null&&pf(e,e.memoizedProps,a.memoizedProps)}break;case 27:jn(t,e),Nn(e),o&512&&(Vt||a===null||Ma(a,a.return)),a!==null&&o&4&&pf(e,e.memoizedProps,a.memoizedProps);break;case 5:if(jn(t,e),Nn(e),o&512&&(Vt||a===null||Ma(a,a.return)),e.flags&32){s=e.stateNode;try{vo(s,"")}catch(Z){Tt(e,e.return,Z)}}o&4&&e.stateNode!=null&&(s=e.memoizedProps,pf(e,s,a!==null?a.memoizedProps:s)),o&1024&&(gf=!0);break;case 6:if(jn(t,e),Nn(e),o&4){if(e.stateNode===null)throw Error(i(162));o=e.memoizedProps,a=e.stateNode;try{a.nodeValue=o}catch(Z){Tt(e,e.return,Z)}}break;case 3:if(Ys=null,s=Sa,Sa=qs(t.containerInfo),jn(t,e),Sa=s,Nn(e),o&4&&a!==null&&a.memoizedState.isDehydrated)try{fi(t.containerInfo)}catch(Z){Tt(e,e.return,Z)}gf&&(gf=!1,tg(e));break;case 4:o=Sa,Sa=qs(e.stateNode.containerInfo),jn(t,e),Nn(e),Sa=o;break;case 12:jn(t,e),Nn(e);break;case 13:jn(t,e),Nn(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Tf=at()),o&4&&(o=e.updateQueue,o!==null&&(e.updateQueue=null,yf(e,o)));break;case 22:s=e.memoizedState!==null;var j=a!==null&&a.memoizedState!==null,W=Ja,ie=Vt;if(Ja=W||s,Vt=ie||j,jn(t,e),Vt=ie,Ja=W,Nn(e),o&8192)e:for(t=e.stateNode,t._visibility=s?t._visibility&-2:t._visibility|1,s&&(a===null||j||Ja||Vt||to(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){j=a=t;try{if(f=j.stateNode,s)g=f.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{C=j.stateNode;var ce=j.memoizedProps.style,F=ce!=null&&ce.hasOwnProperty("display")?ce.display:null;C.style.display=F==null||typeof F=="boolean"?"":(""+F).trim()}}catch(Z){Tt(j,j.return,Z)}}}else if(t.tag===6){if(a===null){j=t;try{j.stateNode.nodeValue=s?"":j.memoizedProps}catch(Z){Tt(j,j.return,Z)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}o&4&&(o=e.updateQueue,o!==null&&(a=o.retryQueue,a!==null&&(o.retryQueue=null,yf(e,a))));break;case 19:jn(t,e),Nn(e),o&4&&(o=e.updateQueue,o!==null&&(e.updateQueue=null,yf(e,o)));break;case 30:break;case 21:break;default:jn(t,e),Nn(e)}}function Nn(e){var t=e.flags;if(t&2){try{for(var a,o=e.return;o!==null;){if(Xh(o)){a=o;break}o=o.return}if(a==null)throw Error(i(160));switch(a.tag){case 27:var s=a.stateNode,f=mf(e);zs(e,f,s);break;case 5:var g=a.stateNode;a.flags&32&&(vo(g,""),a.flags&=-33);var C=mf(e);zs(e,C,g);break;case 3:case 4:var j=a.stateNode.containerInfo,W=mf(e);hf(e,W,j);break;default:throw Error(i(161))}}catch(ie){Tt(e,e.return,ie)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function tg(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;tg(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Sr(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Wh(e,t.alternate,t),t=t.sibling}function to(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:br(4,t,t.return),to(t);break;case 1:Ma(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Yh(t,t.return,a),to(t);break;case 27:ai(t.stateNode);case 26:case 5:Ma(t,t.return),to(t);break;case 22:t.memoizedState===null&&to(t);break;case 30:to(t);break;default:to(t)}e=e.sibling}}function xr(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var o=t.alternate,s=e,f=t,g=f.flags;switch(f.tag){case 0:case 11:case 15:xr(s,f,a),Xl(4,f);break;case 1:if(xr(s,f,a),o=f,s=o.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(W){Tt(o,o.return,W)}if(o=f,s=o.updateQueue,s!==null){var C=o.stateNode;try{var j=s.shared.hiddenCallbacks;if(j!==null)for(s.shared.hiddenCallbacks=null,s=0;s<j.length;s++)zm(j[s],C)}catch(W){Tt(o,o.return,W)}}a&&g&64&&Vh(f),Kl(f,f.return);break;case 27:Kh(f);case 26:case 5:xr(s,f,a),a&&o===null&&g&4&&Gh(f),Kl(f,f.return);break;case 12:xr(s,f,a);break;case 13:xr(s,f,a),a&&g&4&&Jh(s,f);break;case 22:f.memoizedState===null&&xr(s,f,a),Kl(f,f.return);break;case 30:break;default:xr(s,f,a)}t=t.sibling}}function vf(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&$l(a))}function bf(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&$l(e))}function wa(e,t,a,o){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ng(e,t,a,o),t=t.sibling}function ng(e,t,a,o){var s=t.flags;switch(t.tag){case 0:case 11:case 15:wa(e,t,a,o),s&2048&&Xl(9,t);break;case 1:wa(e,t,a,o);break;case 3:wa(e,t,a,o),s&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&$l(e)));break;case 12:if(s&2048){wa(e,t,a,o),e=t.stateNode;try{var f=t.memoizedProps,g=f.id,C=f.onPostCommit;typeof C=="function"&&C(g,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(j){Tt(t,t.return,j)}}else wa(e,t,a,o);break;case 13:wa(e,t,a,o);break;case 23:break;case 22:f=t.stateNode,g=t.alternate,t.memoizedState!==null?f._visibility&2?wa(e,t,a,o):Ql(e,t):f._visibility&2?wa(e,t,a,o):(f._visibility|=2,_o(e,t,a,o,(t.subtreeFlags&10256)!==0)),s&2048&&vf(g,t);break;case 24:wa(e,t,a,o),s&2048&&bf(t.alternate,t);break;default:wa(e,t,a,o)}}function _o(e,t,a,o,s){for(s=s&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var f=e,g=t,C=a,j=o,W=g.flags;switch(g.tag){case 0:case 11:case 15:_o(f,g,C,j,s),Xl(8,g);break;case 23:break;case 22:var ie=g.stateNode;g.memoizedState!==null?ie._visibility&2?_o(f,g,C,j,s):Ql(f,g):(ie._visibility|=2,_o(f,g,C,j,s)),s&&W&2048&&vf(g.alternate,g);break;case 24:_o(f,g,C,j,s),s&&W&2048&&bf(g.alternate,g);break;default:_o(f,g,C,j,s)}t=t.sibling}}function Ql(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,o=t,s=o.flags;switch(o.tag){case 22:Ql(a,o),s&2048&&vf(o.alternate,o);break;case 24:Ql(a,o),s&2048&&bf(o.alternate,o);break;default:Ql(a,o)}t=t.sibling}}var Wl=8192;function Lo(e){if(e.subtreeFlags&Wl)for(e=e.child;e!==null;)ag(e),e=e.sibling}function ag(e){switch(e.tag){case 26:Lo(e),e.flags&Wl&&e.memoizedState!==null&&hS(Sa,e.memoizedState,e.memoizedProps);break;case 5:Lo(e);break;case 3:case 4:var t=Sa;Sa=qs(e.stateNode.containerInfo),Lo(e),Sa=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Wl,Wl=16777216,Lo(e),Wl=t):Lo(e));break;default:Lo(e)}}function rg(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Fl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var o=t[a];an=o,lg(o,e)}rg(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)og(e),e=e.sibling}function og(e){switch(e.tag){case 0:case 11:case 15:Fl(e),e.flags&2048&&br(9,e,e.return);break;case 3:Fl(e);break;case 12:Fl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Ds(e)):Fl(e);break;default:Fl(e)}}function Ds(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var o=t[a];an=o,lg(o,e)}rg(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:br(8,t,t.return),Ds(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Ds(t));break;default:Ds(t)}e=e.sibling}}function lg(e,t){for(;an!==null;){var a=an;switch(a.tag){case 0:case 11:case 15:br(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var o=a.memoizedState.cachePool.pool;o!=null&&o.refCount++}break;case 24:$l(a.memoizedState.cache)}if(o=a.child,o!==null)o.return=a,an=o;else e:for(a=e;an!==null;){o=an;var s=o.sibling,f=o.return;if(Fh(o),o===a){an=null;break e}if(s!==null){s.return=f,an=s;break e}an=f}}}var D1={getCacheForType:function(e){var t=dn(Jt),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},k1=typeof WeakMap=="function"?WeakMap:Map,ht=0,Ot=null,We=null,it=0,gt=0,Bn=null,Cr=!1,Ho=!1,Sf=!1,tr=0,Lt=0,Tr=0,no=0,xf=0,oa=0,Po=0,Zl=null,Rn=null,Cf=!1,Tf=0,ks=1/0,$s=null,Er=null,ln=0,Rr=null,Uo=null,Io=0,Ef=0,Rf=null,ig=null,Jl=0,Of=null;function _n(){if((ht&2)!==0&&it!==0)return it&-it;if(T.T!==null){var e=Ao;return e!==0?e:$f()}return Ea()}function sg(){oa===0&&(oa=(it&536870912)===0||ft?ha():536870912);var e=ra.current;return e!==null&&(e.flags|=32),oa}function Ln(e,t,a){(e===Ot&&(gt===2||gt===9)||e.cancelPendingCommit!==null)&&(qo(e,0),Or(e,it,oa,!1)),zn(e,a),((ht&2)===0||e!==Ot)&&(e===Ot&&((ht&2)===0&&(no|=a),Lt===4&&Or(e,it,oa,!1)),Aa(e))}function ug(e,t,a){if((ht&6)!==0)throw Error(i(327));var o=!a&&(t&124)===0&&(t&e.expiredLanes)===0||ot(e,t),s=o?N1(e,t):Af(e,t,!0),f=o;do{if(s===0){Ho&&!o&&Or(e,t,0,!1);break}else{if(a=e.current.alternate,f&&!$1(a)){s=Af(e,t,!1),f=!1;continue}if(s===2){if(f=t,e.errorRecoveryDisabledLanes&f)var g=0;else g=e.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){t=g;e:{var C=e;s=Zl;var j=C.current.memoizedState.isDehydrated;if(j&&(qo(C,g).flags|=256),g=Af(C,g,!1),g!==2){if(Sf&&!j){C.errorRecoveryDisabledLanes|=f,no|=f,s=4;break e}f=Rn,Rn=s,f!==null&&(Rn===null?Rn=f:Rn.push.apply(Rn,f))}s=g}if(f=!1,s!==2)continue}}if(s===1){qo(e,0),Or(e,t,0,!0);break}e:{switch(o=e,f=s,f){case 0:case 1:throw Error(i(345));case 4:if((t&4194048)!==t)break;case 6:Or(o,t,oa,!Cr);break e;case 2:Rn=null;break;case 3:case 5:break;default:throw Error(i(329))}if((t&62914560)===t&&(s=Tf+300-at(),10<s)){if(Or(o,t,oa,!Cr),Pe(o,0,!0)!==0)break e;o.timeoutHandle=Lg(cg.bind(null,o,a,Rn,$s,Cf,t,oa,no,Po,Cr,f,2,-0,0),s);break e}cg(o,a,Rn,$s,Cf,t,oa,no,Po,Cr,f,0,-0,0)}}break}while(!0);Aa(e)}function cg(e,t,a,o,s,f,g,C,j,W,ie,ce,F,Z){if(e.timeoutHandle=-1,ce=t.subtreeFlags,(ce&8192||(ce&16785408)===16785408)&&(li={stylesheets:null,count:0,unsuspend:mS},ag(t),ce=gS(),ce!==null)){e.cancelPendingCommit=ce(yg.bind(null,e,t,f,a,o,s,g,C,j,ie,1,F,Z)),Or(e,f,g,!W);return}yg(e,t,f,a,o,s,g,C,j)}function $1(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var o=0;o<a.length;o++){var s=a[o],f=s.getSnapshot;s=s.value;try{if(!kn(f(),s))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Or(e,t,a,o){t&=~xf,t&=~no,e.suspendedLanes|=t,e.pingedLanes&=~t,o&&(e.warmLanes|=t),o=e.expirationTimes;for(var s=t;0<s;){var f=31-et(s),g=1<<f;o[f]=-1,s&=~g}a!==0&&Ua(e,a,t)}function js(){return(ht&6)===0?(ei(0),!1):!0}function Mf(){if(We!==null){if(gt===0)var e=We.return;else e=We,Xa=Wr=null,qc(e),No=null,Vl=0,e=We;for(;e!==null;)qh(e.alternate,e),e=e.return;We=null}}function qo(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,F1(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Mf(),Ot=e,We=a=Va(e.current,null),it=t,gt=0,Bn=null,Cr=!1,Ho=ot(e,t),Sf=!1,Po=oa=xf=no=Tr=Lt=0,Rn=Zl=null,Cf=!1,(t&8)!==0&&(t|=t&32);var o=e.entangledLanes;if(o!==0)for(e=e.entanglements,o&=t;0<o;){var s=31-et(o),f=1<<s;t|=e[s],o&=~f}return tr=t,ns(),a}function fg(e,t){Ie=null,T.H=xs,t===Nl||t===fs?(t=wm(),gt=3):t===Rm?(t=wm(),gt=4):gt=t===Ah?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Bn=t,We===null&&(Lt=1,Os(e,ea(t,e.current)))}function dg(){var e=T.H;return T.H=xs,e===null?xs:e}function pg(){var e=T.A;return T.A=D1,e}function wf(){Lt=4,Cr||(it&4194048)!==it&&ra.current!==null||(Ho=!0),(Tr&134217727)===0&&(no&134217727)===0||Ot===null||Or(Ot,it,oa,!1)}function Af(e,t,a){var o=ht;ht|=2;var s=dg(),f=pg();(Ot!==e||it!==t)&&($s=null,qo(e,t)),t=!1;var g=Lt;e:do try{if(gt!==0&&We!==null){var C=We,j=Bn;switch(gt){case 8:Mf(),g=6;break e;case 3:case 2:case 9:case 6:ra.current===null&&(t=!0);var W=gt;if(gt=0,Bn=null,Vo(e,C,j,W),a&&Ho){g=0;break e}break;default:W=gt,gt=0,Bn=null,Vo(e,C,j,W)}}j1(),g=Lt;break}catch(ie){fg(e,ie)}while(!0);return t&&e.shellSuspendCounter++,Xa=Wr=null,ht=o,T.H=s,T.A=f,We===null&&(Ot=null,it=0,ns()),g}function j1(){for(;We!==null;)mg(We)}function N1(e,t){var a=ht;ht|=2;var o=dg(),s=pg();Ot!==e||it!==t?($s=null,ks=at()+500,qo(e,t)):Ho=ot(e,t);e:do try{if(gt!==0&&We!==null){t=We;var f=Bn;t:switch(gt){case 1:gt=0,Bn=null,Vo(e,t,f,1);break;case 2:case 9:if(Om(f)){gt=0,Bn=null,hg(t);break}t=function(){gt!==2&&gt!==9||Ot!==e||(gt=7),Aa(e)},f.then(t,t);break e;case 3:gt=7;break e;case 4:gt=5;break e;case 7:Om(f)?(gt=0,Bn=null,hg(t)):(gt=0,Bn=null,Vo(e,t,f,7));break;case 5:var g=null;switch(We.tag){case 26:g=We.memoizedState;case 5:case 27:var C=We;if(!g||Wg(g)){gt=0,Bn=null;var j=C.sibling;if(j!==null)We=j;else{var W=C.return;W!==null?(We=W,Ns(W)):We=null}break t}}gt=0,Bn=null,Vo(e,t,f,5);break;case 6:gt=0,Bn=null,Vo(e,t,f,6);break;case 8:Mf(),Lt=6;break e;default:throw Error(i(462))}}B1();break}catch(ie){fg(e,ie)}while(!0);return Xa=Wr=null,T.H=o,T.A=s,ht=a,We!==null?0:(Ot=null,it=0,ns(),Lt)}function B1(){for(;We!==null&&!nt();)mg(We)}function mg(e){var t=Uh(e.alternate,e,tr);e.memoizedProps=e.pendingProps,t===null?Ns(e):We=t}function hg(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=Nh(a,t,t.pendingProps,t.type,void 0,it);break;case 11:t=Nh(a,t,t.pendingProps,t.type.render,t.ref,it);break;case 5:qc(t);default:qh(a,t),t=We=gm(t,tr),t=Uh(a,t,tr)}e.memoizedProps=e.pendingProps,t===null?Ns(e):We=t}function Vo(e,t,a,o){Xa=Wr=null,qc(t),No=null,Vl=0;var s=t.return;try{if(R1(e,s,t,a,it)){Lt=1,Os(e,ea(a,e.current)),We=null;return}}catch(f){if(s!==null)throw We=s,f;Lt=1,Os(e,ea(a,e.current)),We=null;return}t.flags&32768?(ft||o===1?e=!0:Ho||(it&536870912)!==0?e=!1:(Cr=e=!0,(o===2||o===9||o===3||o===6)&&(o=ra.current,o!==null&&o.tag===13&&(o.flags|=16384))),gg(t,e)):Ns(t)}function Ns(e){var t=e;do{if((t.flags&32768)!==0){gg(t,Cr);return}e=t.return;var a=M1(t.alternate,t,tr);if(a!==null){We=a;return}if(t=t.sibling,t!==null){We=t;return}We=t=e}while(t!==null);Lt===0&&(Lt=5)}function gg(e,t){do{var a=w1(e.alternate,e);if(a!==null){a.flags&=32767,We=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){We=e;return}We=e=a}while(e!==null);Lt=6,We=null}function yg(e,t,a,o,s,f,g,C,j){e.cancelPendingCommit=null;do Bs();while(ln!==0);if((ht&6)!==0)throw Error(i(327));if(t!==null){if(t===e.current)throw Error(i(177));if(f=t.lanes|t.childLanes,f|=vc,ga(e,a,f,g,C,j),e===Ot&&(We=Ot=null,it=0),Uo=t,Rr=e,Io=a,Ef=f,Rf=s,ig=o,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,P1(Le,function(){return Cg(),null})):(e.callbackNode=null,e.callbackPriority=0),o=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||o){o=T.T,T.T=null,s=H.p,H.p=2,g=ht,ht|=4;try{A1(e,t,a)}finally{ht=g,H.p=s,T.T=o}}ln=1,vg(),bg(),Sg()}}function vg(){if(ln===1){ln=0;var e=Rr,t=Uo,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=T.T,T.T=null;var o=H.p;H.p=2;var s=ht;ht|=4;try{eg(t,e);var f=Uf,g=lm(e.containerInfo),C=f.focusedElem,j=f.selectionRange;if(g!==C&&C&&C.ownerDocument&&om(C.ownerDocument.documentElement,C)){if(j!==null&&pc(C)){var W=j.start,ie=j.end;if(ie===void 0&&(ie=W),"selectionStart"in C)C.selectionStart=W,C.selectionEnd=Math.min(ie,C.value.length);else{var ce=C.ownerDocument||document,F=ce&&ce.defaultView||window;if(F.getSelection){var Z=F.getSelection(),$e=C.textContent.length,ze=Math.min(j.start,$e),xt=j.end===void 0?ze:Math.min(j.end,$e);!Z.extend&&ze>xt&&(g=xt,xt=ze,ze=g);var I=rm(C,ze),P=rm(C,xt);if(I&&P&&(Z.rangeCount!==1||Z.anchorNode!==I.node||Z.anchorOffset!==I.offset||Z.focusNode!==P.node||Z.focusOffset!==P.offset)){var Q=ce.createRange();Q.setStart(I.node,I.offset),Z.removeAllRanges(),ze>xt?(Z.addRange(Q),Z.extend(P.node,P.offset)):(Q.setEnd(P.node,P.offset),Z.addRange(Q))}}}}for(ce=[],Z=C;Z=Z.parentNode;)Z.nodeType===1&&ce.push({element:Z,left:Z.scrollLeft,top:Z.scrollTop});for(typeof C.focus=="function"&&C.focus(),C=0;C<ce.length;C++){var se=ce[C];se.element.scrollLeft=se.left,se.element.scrollTop=se.top}}Ks=!!Pf,Uf=Pf=null}finally{ht=s,H.p=o,T.T=a}}e.current=t,ln=2}}function bg(){if(ln===2){ln=0;var e=Rr,t=Uo,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=T.T,T.T=null;var o=H.p;H.p=2;var s=ht;ht|=4;try{Wh(e,t.alternate,t)}finally{ht=s,H.p=o,T.T=a}}ln=3}}function Sg(){if(ln===4||ln===3){ln=0,Ve();var e=Rr,t=Uo,a=Io,o=ig;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ln=5:(ln=0,Uo=Rr=null,xg(e,e.pendingLanes));var s=e.pendingLanes;if(s===0&&(Er=null),cr(a),t=t.stateNode,je&&typeof je.onCommitFiberRoot=="function")try{je.onCommitFiberRoot(rt,t,void 0,(t.current.flags&128)===128)}catch{}if(o!==null){t=T.T,s=H.p,H.p=2,T.T=null;try{for(var f=e.onRecoverableError,g=0;g<o.length;g++){var C=o[g];f(C.value,{componentStack:C.stack})}}finally{T.T=t,H.p=s}}(Io&3)!==0&&Bs(),Aa(e),s=e.pendingLanes,(a&4194090)!==0&&(s&42)!==0?e===Of?Jl++:(Jl=0,Of=e):Jl=0,ei(0)}}function xg(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,$l(t)))}function Bs(e){return vg(),bg(),Sg(),Cg()}function Cg(){if(ln!==5)return!1;var e=Rr,t=Ef;Ef=0;var a=cr(Io),o=T.T,s=H.p;try{H.p=32>a?32:a,T.T=null,a=Rf,Rf=null;var f=Rr,g=Io;if(ln=0,Uo=Rr=null,Io=0,(ht&6)!==0)throw Error(i(331));var C=ht;if(ht|=4,og(f.current),ng(f,f.current,g,a),ht=C,ei(0,!1),je&&typeof je.onPostCommitFiberRoot=="function")try{je.onPostCommitFiberRoot(rt,f)}catch{}return!0}finally{H.p=s,T.T=o,xg(e,t)}}function Tg(e,t,a){t=ea(a,t),t=af(e.stateNode,t,2),e=hr(e,t,2),e!==null&&(zn(e,2),Aa(e))}function Tt(e,t,a){if(e.tag===3)Tg(e,e,a);else for(;t!==null;){if(t.tag===3){Tg(t,e,a);break}else if(t.tag===1){var o=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(Er===null||!Er.has(o))){e=ea(a,e),a=Mh(2),o=hr(t,a,2),o!==null&&(wh(a,o,t,e),zn(o,2),Aa(o));break}}t=t.return}}function zf(e,t,a){var o=e.pingCache;if(o===null){o=e.pingCache=new k1;var s=new Set;o.set(t,s)}else s=o.get(t),s===void 0&&(s=new Set,o.set(t,s));s.has(a)||(Sf=!0,s.add(a),e=_1.bind(null,e,t,a),t.then(e,e))}function _1(e,t,a){var o=e.pingCache;o!==null&&o.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Ot===e&&(it&a)===a&&(Lt===4||Lt===3&&(it&62914560)===it&&300>at()-Tf?(ht&2)===0&&qo(e,0):xf|=a,Po===it&&(Po=0)),Aa(e)}function Eg(e,t){t===0&&(t=po()),e=Ro(e,t),e!==null&&(zn(e,t),Aa(e))}function L1(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Eg(e,a)}function H1(e,t){var a=0;switch(e.tag){case 13:var o=e.stateNode,s=e.memoizedState;s!==null&&(a=s.retryLane);break;case 19:o=e.stateNode;break;case 22:o=e.stateNode._retryCache;break;default:throw Error(i(314))}o!==null&&o.delete(t),Eg(e,a)}function P1(e,t){return _e(e,t)}var _s=null,Yo=null,Df=!1,Ls=!1,kf=!1,ao=0;function Aa(e){e!==Yo&&e.next===null&&(Yo===null?_s=Yo=e:Yo=Yo.next=e),Ls=!0,Df||(Df=!0,I1())}function ei(e,t){if(!kf&&Ls){kf=!0;do for(var a=!1,o=_s;o!==null;){if(e!==0){var s=o.pendingLanes;if(s===0)var f=0;else{var g=o.suspendedLanes,C=o.pingedLanes;f=(1<<31-et(42|e)+1)-1,f&=s&~(g&~C),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(a=!0,wg(o,f))}else f=it,f=Pe(o,o===Ot?f:0,o.cancelPendingCommit!==null||o.timeoutHandle!==-1),(f&3)===0||ot(o,f)||(a=!0,wg(o,f));o=o.next}while(a);kf=!1}}function U1(){Rg()}function Rg(){Ls=Df=!1;var e=0;ao!==0&&(W1()&&(e=ao),ao=0);for(var t=at(),a=null,o=_s;o!==null;){var s=o.next,f=Og(o,t);f===0?(o.next=null,a===null?_s=s:a.next=s,s===null&&(Yo=a)):(a=o,(e!==0||(f&3)!==0)&&(Ls=!0)),o=s}ei(e)}function Og(e,t){for(var a=e.suspendedLanes,o=e.pingedLanes,s=e.expirationTimes,f=e.pendingLanes&-62914561;0<f;){var g=31-et(f),C=1<<g,j=s[g];j===-1?((C&a)===0||(C&o)!==0)&&(s[g]=Gn(C,t)):j<=t&&(e.expiredLanes|=C),f&=~C}if(t=Ot,a=it,a=Pe(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),o=e.callbackNode,a===0||e===t&&(gt===2||gt===9)||e.cancelPendingCommit!==null)return o!==null&&o!==null&&ke(o),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||ot(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(o!==null&&ke(o),cr(a)){case 2:case 8:a=Te;break;case 32:a=Le;break;case 268435456:a=It;break;default:a=Le}return o=Mg.bind(null,e),a=_e(a,o),e.callbackPriority=t,e.callbackNode=a,t}return o!==null&&o!==null&&ke(o),e.callbackPriority=2,e.callbackNode=null,2}function Mg(e,t){if(ln!==0&&ln!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(Bs()&&e.callbackNode!==a)return null;var o=it;return o=Pe(e,e===Ot?o:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),o===0?null:(ug(e,o,t),Og(e,at()),e.callbackNode!=null&&e.callbackNode===a?Mg.bind(null,e):null)}function wg(e,t){if(Bs())return null;ug(e,t,!0)}function I1(){Z1(function(){(ht&6)!==0?_e(st,U1):Rg()})}function $f(){return ao===0&&(ao=ha()),ao}function Ag(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Qi(""+e)}function zg(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function q1(e,t,a,o,s){if(t==="submit"&&a&&a.stateNode===s){var f=Ag((s[Xt]||null).action),g=o.submitter;g&&(t=(t=g[Xt]||null)?Ag(t.formAction):g.getAttribute("formAction"),t!==null&&(f=t,g=null));var C=new Ji("action","action",null,o,s);e.push({event:C,listeners:[{instance:null,listener:function(){if(o.defaultPrevented){if(ao!==0){var j=g?zg(s,g):new FormData(s);Zc(a,{pending:!0,data:j,method:s.method,action:f},null,j)}}else typeof f=="function"&&(C.preventDefault(),j=g?zg(s,g):new FormData(s),Zc(a,{pending:!0,data:j,method:s.method,action:f},f,j))},currentTarget:s}]})}}for(var jf=0;jf<yc.length;jf++){var Nf=yc[jf],V1=Nf.toLowerCase(),Y1=Nf[0].toUpperCase()+Nf.slice(1);ba(V1,"on"+Y1)}ba(um,"onAnimationEnd"),ba(cm,"onAnimationIteration"),ba(fm,"onAnimationStart"),ba("dblclick","onDoubleClick"),ba("focusin","onFocus"),ba("focusout","onBlur"),ba(s1,"onTransitionRun"),ba(u1,"onTransitionStart"),ba(c1,"onTransitionCancel"),ba(dm,"onTransitionEnd"),Ee("onMouseEnter",["mouseout","mouseover"]),Ee("onMouseLeave",["mouseout","mouseover"]),Ee("onPointerEnter",["pointerout","pointerover"]),Ee("onPointerLeave",["pointerout","pointerover"]),Be("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Be("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Be("onBeforeInput",["compositionend","keypress","textInput","paste"]),Be("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Be("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Be("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ti="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),G1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ti));function Dg(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var o=e[a],s=o.event;o=o.listeners;e:{var f=void 0;if(t)for(var g=o.length-1;0<=g;g--){var C=o[g],j=C.instance,W=C.currentTarget;if(C=C.listener,j!==f&&s.isPropagationStopped())break e;f=C,s.currentTarget=W;try{f(s)}catch(ie){Rs(ie)}s.currentTarget=null,f=j}else for(g=0;g<o.length;g++){if(C=o[g],j=C.instance,W=C.currentTarget,C=C.listener,j!==f&&s.isPropagationStopped())break e;f=C,s.currentTarget=W;try{f(s)}catch(ie){Rs(ie)}s.currentTarget=null,f=j}}}}function Fe(e,t){var a=t[Pr];a===void 0&&(a=t[Pr]=new Set);var o=e+"__bubble";a.has(o)||(kg(t,e,2,!1),a.add(o))}function Bf(e,t,a){var o=0;t&&(o|=4),kg(a,e,o,t)}var Hs="_reactListening"+Math.random().toString(36).slice(2);function _f(e){if(!e[Hs]){e[Hs]=!0,re.forEach(function(a){a!=="selectionchange"&&(G1.has(a)||Bf(a,!1,e),Bf(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Hs]||(t[Hs]=!0,Bf("selectionchange",!1,t))}}function kg(e,t,a,o){switch(ny(t)){case 2:var s=bS;break;case 8:s=SS;break;default:s=Ff}a=s.bind(null,t,a,e),s=void 0,!rc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),o?s!==void 0?e.addEventListener(t,a,{capture:!0,passive:s}):e.addEventListener(t,a,!0):s!==void 0?e.addEventListener(t,a,{passive:s}):e.addEventListener(t,a,!1)}function Lf(e,t,a,o,s){var f=o;if((t&1)===0&&(t&2)===0&&o!==null)e:for(;;){if(o===null)return;var g=o.tag;if(g===3||g===4){var C=o.stateNode.containerInfo;if(C===s)break;if(g===4)for(g=o.return;g!==null;){var j=g.tag;if((j===3||j===4)&&g.stateNode.containerInfo===s)return;g=g.return}for(;C!==null;){if(g=Mt(C),g===null)return;if(j=g.tag,j===5||j===6||j===26||j===27){o=f=g;continue e}C=C.parentNode}}o=o.return}Hp(function(){var W=f,ie=nc(a),ce=[];e:{var F=pm.get(e);if(F!==void 0){var Z=Ji,$e=e;switch(e){case"keypress":if(Fi(a)===0)break e;case"keydown":case"keyup":Z=Pb;break;case"focusin":$e="focus",Z=sc;break;case"focusout":$e="blur",Z=sc;break;case"beforeblur":case"afterblur":Z=sc;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":Z=Ip;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":Z=wb;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":Z=qb;break;case um:case cm:case fm:Z=Db;break;case dm:Z=Yb;break;case"scroll":case"scrollend":Z=Ob;break;case"wheel":Z=Xb;break;case"copy":case"cut":case"paste":Z=$b;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":Z=Vp;break;case"toggle":case"beforetoggle":Z=Qb}var ze=(t&4)!==0,xt=!ze&&(e==="scroll"||e==="scrollend"),I=ze?F!==null?F+"Capture":null:F;ze=[];for(var P=W,Q;P!==null;){var se=P;if(Q=se.stateNode,se=se.tag,se!==5&&se!==26&&se!==27||Q===null||I===null||(se=Sl(P,I),se!=null&&ze.push(ni(P,se,Q))),xt)break;P=P.return}0<ze.length&&(F=new Z(F,$e,null,a,ie),ce.push({event:F,listeners:ze}))}}if((t&7)===0){e:{if(F=e==="mouseover"||e==="pointerover",Z=e==="mouseout"||e==="pointerout",F&&a!==tc&&($e=a.relatedTarget||a.fromElement)&&(Mt($e)||$e[Kn]))break e;if((Z||F)&&(F=ie.window===ie?ie:(F=ie.ownerDocument)?F.defaultView||F.parentWindow:window,Z?($e=a.relatedTarget||a.toElement,Z=W,$e=$e?Mt($e):null,$e!==null&&(xt=c($e),ze=$e.tag,$e!==xt||ze!==5&&ze!==27&&ze!==6)&&($e=null)):(Z=null,$e=W),Z!==$e)){if(ze=Ip,se="onMouseLeave",I="onMouseEnter",P="mouse",(e==="pointerout"||e==="pointerover")&&(ze=Vp,se="onPointerLeave",I="onPointerEnter",P="pointer"),xt=Z==null?F:ya(Z),Q=$e==null?F:ya($e),F=new ze(se,P+"leave",Z,a,ie),F.target=xt,F.relatedTarget=Q,se=null,Mt(ie)===W&&(ze=new ze(I,P+"enter",$e,a,ie),ze.target=Q,ze.relatedTarget=xt,se=ze),xt=se,Z&&$e)t:{for(ze=Z,I=$e,P=0,Q=ze;Q;Q=Go(Q))P++;for(Q=0,se=I;se;se=Go(se))Q++;for(;0<P-Q;)ze=Go(ze),P--;for(;0<Q-P;)I=Go(I),Q--;for(;P--;){if(ze===I||I!==null&&ze===I.alternate)break t;ze=Go(ze),I=Go(I)}ze=null}else ze=null;Z!==null&&$g(ce,F,Z,ze,!1),$e!==null&&xt!==null&&$g(ce,xt,$e,ze,!0)}}e:{if(F=W?ya(W):window,Z=F.nodeName&&F.nodeName.toLowerCase(),Z==="select"||Z==="input"&&F.type==="file")var Se=Zp;else if(Wp(F))if(Jp)Se=o1;else{Se=a1;var Ge=n1}else Z=F.nodeName,!Z||Z.toLowerCase()!=="input"||F.type!=="checkbox"&&F.type!=="radio"?W&&ec(W.elementType)&&(Se=Zp):Se=r1;if(Se&&(Se=Se(e,W))){Fp(ce,Se,a,ie);break e}Ge&&Ge(e,F,W),e==="focusout"&&W&&F.type==="number"&&W.memoizedProps.value!=null&&Ju(F,"number",F.value)}switch(Ge=W?ya(W):window,e){case"focusin":(Wp(Ge)||Ge.contentEditable==="true")&&(Co=Ge,mc=W,wl=null);break;case"focusout":wl=mc=Co=null;break;case"mousedown":hc=!0;break;case"contextmenu":case"mouseup":case"dragend":hc=!1,im(ce,a,ie);break;case"selectionchange":if(i1)break;case"keydown":case"keyup":im(ce,a,ie)}var Oe;if(cc)e:{switch(e){case"compositionstart":var De="onCompositionStart";break e;case"compositionend":De="onCompositionEnd";break e;case"compositionupdate":De="onCompositionUpdate";break e}De=void 0}else xo?Kp(e,a)&&(De="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(De="onCompositionStart");De&&(Yp&&a.locale!=="ko"&&(xo||De!=="onCompositionStart"?De==="onCompositionEnd"&&xo&&(Oe=Pp()):(fr=ie,oc="value"in fr?fr.value:fr.textContent,xo=!0)),Ge=Ps(W,De),0<Ge.length&&(De=new qp(De,e,null,a,ie),ce.push({event:De,listeners:Ge}),Oe?De.data=Oe:(Oe=Qp(a),Oe!==null&&(De.data=Oe)))),(Oe=Fb?Zb(e,a):Jb(e,a))&&(De=Ps(W,"onBeforeInput"),0<De.length&&(Ge=new qp("onBeforeInput","beforeinput",null,a,ie),ce.push({event:Ge,listeners:De}),Ge.data=Oe)),q1(ce,e,W,a,ie)}Dg(ce,t)})}function ni(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Ps(e,t){for(var a=t+"Capture",o=[];e!==null;){var s=e,f=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||f===null||(s=Sl(e,a),s!=null&&o.unshift(ni(e,s,f)),s=Sl(e,t),s!=null&&o.push(ni(e,s,f))),e.tag===3)return o;e=e.return}return[]}function Go(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function $g(e,t,a,o,s){for(var f=t._reactName,g=[];a!==null&&a!==o;){var C=a,j=C.alternate,W=C.stateNode;if(C=C.tag,j!==null&&j===o)break;C!==5&&C!==26&&C!==27||W===null||(j=W,s?(W=Sl(a,f),W!=null&&g.unshift(ni(a,W,j))):s||(W=Sl(a,f),W!=null&&g.push(ni(a,W,j)))),a=a.return}g.length!==0&&e.push({event:t,listeners:g})}var X1=/\r\n?/g,K1=/\u0000|\uFFFD/g;function jg(e){return(typeof e=="string"?e:""+e).replace(X1,`
`).replace(K1,"")}function Ng(e,t){return t=jg(t),jg(e)===t}function Us(){}function St(e,t,a,o,s,f){switch(a){case"children":typeof o=="string"?t==="body"||t==="textarea"&&o===""||vo(e,o):(typeof o=="number"||typeof o=="bigint")&&t!=="body"&&vo(e,""+o);break;case"className":Ur(e,"class",o);break;case"tabIndex":Ur(e,"tabindex",o);break;case"dir":case"role":case"viewBox":case"width":case"height":Ur(e,a,o);break;case"style":_p(e,o,f);break;case"data":if(t!=="object"){Ur(e,"data",o);break}case"src":case"href":if(o===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(o==null||typeof o=="function"||typeof o=="symbol"||typeof o=="boolean"){e.removeAttribute(a);break}o=Qi(""+o),e.setAttribute(a,o);break;case"action":case"formAction":if(typeof o=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(a==="formAction"?(t!=="input"&&St(e,t,"name",s.name,s,null),St(e,t,"formEncType",s.formEncType,s,null),St(e,t,"formMethod",s.formMethod,s,null),St(e,t,"formTarget",s.formTarget,s,null)):(St(e,t,"encType",s.encType,s,null),St(e,t,"method",s.method,s,null),St(e,t,"target",s.target,s,null)));if(o==null||typeof o=="symbol"||typeof o=="boolean"){e.removeAttribute(a);break}o=Qi(""+o),e.setAttribute(a,o);break;case"onClick":o!=null&&(e.onclick=Us);break;case"onScroll":o!=null&&Fe("scroll",e);break;case"onScrollEnd":o!=null&&Fe("scrollend",e);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(i(61));if(a=o.__html,a!=null){if(s.children!=null)throw Error(i(60));e.innerHTML=a}}break;case"multiple":e.multiple=o&&typeof o!="function"&&typeof o!="symbol";break;case"muted":e.muted=o&&typeof o!="function"&&typeof o!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(o==null||typeof o=="function"||typeof o=="boolean"||typeof o=="symbol"){e.removeAttribute("xlink:href");break}a=Qi(""+o),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":o!=null&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(a,""+o):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":o&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":o===!0?e.setAttribute(a,""):o!==!1&&o!=null&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(a,o):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":o!=null&&typeof o!="function"&&typeof o!="symbol"&&!isNaN(o)&&1<=o?e.setAttribute(a,o):e.removeAttribute(a);break;case"rowSpan":case"start":o==null||typeof o=="function"||typeof o=="symbol"||isNaN(o)?e.removeAttribute(a):e.setAttribute(a,o);break;case"popover":Fe("beforetoggle",e),Fe("toggle",e),ho(e,"popover",o);break;case"xlinkActuate":Wn(e,"http://www.w3.org/1999/xlink","xlink:actuate",o);break;case"xlinkArcrole":Wn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",o);break;case"xlinkRole":Wn(e,"http://www.w3.org/1999/xlink","xlink:role",o);break;case"xlinkShow":Wn(e,"http://www.w3.org/1999/xlink","xlink:show",o);break;case"xlinkTitle":Wn(e,"http://www.w3.org/1999/xlink","xlink:title",o);break;case"xlinkType":Wn(e,"http://www.w3.org/1999/xlink","xlink:type",o);break;case"xmlBase":Wn(e,"http://www.w3.org/XML/1998/namespace","xml:base",o);break;case"xmlLang":Wn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",o);break;case"xmlSpace":Wn(e,"http://www.w3.org/XML/1998/namespace","xml:space",o);break;case"is":ho(e,"is",o);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Eb.get(a)||a,ho(e,a,o))}}function Hf(e,t,a,o,s,f){switch(a){case"style":_p(e,o,f);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(i(61));if(a=o.__html,a!=null){if(s.children!=null)throw Error(i(60));e.innerHTML=a}}break;case"children":typeof o=="string"?vo(e,o):(typeof o=="number"||typeof o=="bigint")&&vo(e,""+o);break;case"onScroll":o!=null&&Fe("scroll",e);break;case"onScrollEnd":o!=null&&Fe("scrollend",e);break;case"onClick":o!=null&&(e.onclick=Us);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ge.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(s=a.endsWith("Capture"),t=a.slice(2,s?a.length-7:void 0),f=e[Xt]||null,f=f!=null?f[a]:null,typeof f=="function"&&e.removeEventListener(t,f,s),typeof o=="function")){typeof f!="function"&&f!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,o,s);break e}a in e?e[a]=o:o===!0?e.setAttribute(a,""):ho(e,a,o)}}}function sn(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Fe("error",e),Fe("load",e);var o=!1,s=!1,f;for(f in a)if(a.hasOwnProperty(f)){var g=a[f];if(g!=null)switch(f){case"src":o=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:St(e,t,f,g,a,null)}}s&&St(e,t,"srcSet",a.srcSet,a,null),o&&St(e,t,"src",a.src,a,null);return;case"input":Fe("invalid",e);var C=f=g=s=null,j=null,W=null;for(o in a)if(a.hasOwnProperty(o)){var ie=a[o];if(ie!=null)switch(o){case"name":s=ie;break;case"type":g=ie;break;case"checked":j=ie;break;case"defaultChecked":W=ie;break;case"value":f=ie;break;case"defaultValue":C=ie;break;case"children":case"dangerouslySetInnerHTML":if(ie!=null)throw Error(i(137,t));break;default:St(e,t,o,ie,a,null)}}$p(e,f,C,j,W,g,s,!1),Xi(e);return;case"select":Fe("invalid",e),o=g=f=null;for(s in a)if(a.hasOwnProperty(s)&&(C=a[s],C!=null))switch(s){case"value":f=C;break;case"defaultValue":g=C;break;case"multiple":o=C;default:St(e,t,s,C,a,null)}t=f,a=g,e.multiple=!!o,t!=null?yo(e,!!o,t,!1):a!=null&&yo(e,!!o,a,!0);return;case"textarea":Fe("invalid",e),f=s=o=null;for(g in a)if(a.hasOwnProperty(g)&&(C=a[g],C!=null))switch(g){case"value":o=C;break;case"defaultValue":s=C;break;case"children":f=C;break;case"dangerouslySetInnerHTML":if(C!=null)throw Error(i(91));break;default:St(e,t,g,C,a,null)}Np(e,o,s,f),Xi(e);return;case"option":for(j in a)if(a.hasOwnProperty(j)&&(o=a[j],o!=null))switch(j){case"selected":e.selected=o&&typeof o!="function"&&typeof o!="symbol";break;default:St(e,t,j,o,a,null)}return;case"dialog":Fe("beforetoggle",e),Fe("toggle",e),Fe("cancel",e),Fe("close",e);break;case"iframe":case"object":Fe("load",e);break;case"video":case"audio":for(o=0;o<ti.length;o++)Fe(ti[o],e);break;case"image":Fe("error",e),Fe("load",e);break;case"details":Fe("toggle",e);break;case"embed":case"source":case"link":Fe("error",e),Fe("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(W in a)if(a.hasOwnProperty(W)&&(o=a[W],o!=null))switch(W){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:St(e,t,W,o,a,null)}return;default:if(ec(t)){for(ie in a)a.hasOwnProperty(ie)&&(o=a[ie],o!==void 0&&Hf(e,t,ie,o,a,void 0));return}}for(C in a)a.hasOwnProperty(C)&&(o=a[C],o!=null&&St(e,t,C,o,a,null))}function Q1(e,t,a,o){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,f=null,g=null,C=null,j=null,W=null,ie=null;for(Z in a){var ce=a[Z];if(a.hasOwnProperty(Z)&&ce!=null)switch(Z){case"checked":break;case"value":break;case"defaultValue":j=ce;default:o.hasOwnProperty(Z)||St(e,t,Z,null,o,ce)}}for(var F in o){var Z=o[F];if(ce=a[F],o.hasOwnProperty(F)&&(Z!=null||ce!=null))switch(F){case"type":f=Z;break;case"name":s=Z;break;case"checked":W=Z;break;case"defaultChecked":ie=Z;break;case"value":g=Z;break;case"defaultValue":C=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(i(137,t));break;default:Z!==ce&&St(e,t,F,Z,o,ce)}}Zu(e,g,C,j,W,ie,f,s);return;case"select":Z=g=C=F=null;for(f in a)if(j=a[f],a.hasOwnProperty(f)&&j!=null)switch(f){case"value":break;case"multiple":Z=j;default:o.hasOwnProperty(f)||St(e,t,f,null,o,j)}for(s in o)if(f=o[s],j=a[s],o.hasOwnProperty(s)&&(f!=null||j!=null))switch(s){case"value":F=f;break;case"defaultValue":C=f;break;case"multiple":g=f;default:f!==j&&St(e,t,s,f,o,j)}t=C,a=g,o=Z,F!=null?yo(e,!!a,F,!1):!!o!=!!a&&(t!=null?yo(e,!!a,t,!0):yo(e,!!a,a?[]:"",!1));return;case"textarea":Z=F=null;for(C in a)if(s=a[C],a.hasOwnProperty(C)&&s!=null&&!o.hasOwnProperty(C))switch(C){case"value":break;case"children":break;default:St(e,t,C,null,o,s)}for(g in o)if(s=o[g],f=a[g],o.hasOwnProperty(g)&&(s!=null||f!=null))switch(g){case"value":F=s;break;case"defaultValue":Z=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(i(91));break;default:s!==f&&St(e,t,g,s,o,f)}jp(e,F,Z);return;case"option":for(var $e in a)if(F=a[$e],a.hasOwnProperty($e)&&F!=null&&!o.hasOwnProperty($e))switch($e){case"selected":e.selected=!1;break;default:St(e,t,$e,null,o,F)}for(j in o)if(F=o[j],Z=a[j],o.hasOwnProperty(j)&&F!==Z&&(F!=null||Z!=null))switch(j){case"selected":e.selected=F&&typeof F!="function"&&typeof F!="symbol";break;default:St(e,t,j,F,o,Z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ze in a)F=a[ze],a.hasOwnProperty(ze)&&F!=null&&!o.hasOwnProperty(ze)&&St(e,t,ze,null,o,F);for(W in o)if(F=o[W],Z=a[W],o.hasOwnProperty(W)&&F!==Z&&(F!=null||Z!=null))switch(W){case"children":case"dangerouslySetInnerHTML":if(F!=null)throw Error(i(137,t));break;default:St(e,t,W,F,o,Z)}return;default:if(ec(t)){for(var xt in a)F=a[xt],a.hasOwnProperty(xt)&&F!==void 0&&!o.hasOwnProperty(xt)&&Hf(e,t,xt,void 0,o,F);for(ie in o)F=o[ie],Z=a[ie],!o.hasOwnProperty(ie)||F===Z||F===void 0&&Z===void 0||Hf(e,t,ie,F,o,Z);return}}for(var I in a)F=a[I],a.hasOwnProperty(I)&&F!=null&&!o.hasOwnProperty(I)&&St(e,t,I,null,o,F);for(ce in o)F=o[ce],Z=a[ce],!o.hasOwnProperty(ce)||F===Z||F==null&&Z==null||St(e,t,ce,F,o,Z)}var Pf=null,Uf=null;function Is(e){return e.nodeType===9?e:e.ownerDocument}function Bg(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function _g(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function If(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var qf=null;function W1(){var e=window.event;return e&&e.type==="popstate"?e===qf?!1:(qf=e,!0):(qf=null,!1)}var Lg=typeof setTimeout=="function"?setTimeout:void 0,F1=typeof clearTimeout=="function"?clearTimeout:void 0,Hg=typeof Promise=="function"?Promise:void 0,Z1=typeof queueMicrotask=="function"?queueMicrotask:typeof Hg<"u"?function(e){return Hg.resolve(null).then(e).catch(J1)}:Lg;function J1(e){setTimeout(function(){throw e})}function Mr(e){return e==="head"}function Pg(e,t){var a=t,o=0,s=0;do{var f=a.nextSibling;if(e.removeChild(a),f&&f.nodeType===8)if(a=f.data,a==="/$"){if(0<o&&8>o){a=o;var g=e.ownerDocument;if(a&1&&ai(g.documentElement),a&2&&ai(g.body),a&4)for(a=g.head,ai(a),g=a.firstChild;g;){var C=g.nextSibling,j=g.nodeName;g[Qn]||j==="SCRIPT"||j==="STYLE"||j==="LINK"&&g.rel.toLowerCase()==="stylesheet"||a.removeChild(g),g=C}}if(s===0){e.removeChild(f),fi(t);return}s--}else a==="$"||a==="$?"||a==="$!"?s++:o=a.charCodeAt(0)-48;else o=0;a=f}while(a);fi(t)}function Vf(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Vf(a),Bt(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function eS(e,t,a,o){for(;e.nodeType===1;){var s=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!o&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(o){if(!e[Qn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(f=e.getAttribute("rel"),f==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(f!==s.rel||e.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||e.getAttribute("title")!==(s.title==null?null:s.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(f=e.getAttribute("src"),(f!==(s.src==null?null:s.src)||e.getAttribute("type")!==(s.type==null?null:s.type)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&f&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var f=s.name==null?null:""+s.name;if(s.type==="hidden"&&e.getAttribute("name")===f)return e}else return e;if(e=xa(e.nextSibling),e===null)break}return null}function tS(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=xa(e.nextSibling),e===null))return null;return e}function Yf(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function nS(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var o=function(){t(),a.removeEventListener("DOMContentLoaded",o)};a.addEventListener("DOMContentLoaded",o),e._reactRetry=o}}function xa(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Gf=null;function Ug(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function Ig(e,t,a){switch(t=Is(a),e){case"html":if(e=t.documentElement,!e)throw Error(i(452));return e;case"head":if(e=t.head,!e)throw Error(i(453));return e;case"body":if(e=t.body,!e)throw Error(i(454));return e;default:throw Error(i(451))}}function ai(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Bt(e)}var la=new Map,qg=new Set;function qs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var nr=H.d;H.d={f:aS,r:rS,D:oS,C:lS,L:iS,m:sS,X:cS,S:uS,M:fS};function aS(){var e=nr.f(),t=js();return e||t}function rS(e){var t=gn(e);t!==null&&t.tag===5&&t.type==="form"?uh(t):nr.r(e)}var Xo=typeof document>"u"?null:document;function Vg(e,t,a){var o=Xo;if(o&&typeof t=="string"&&t){var s=Jn(t);s='link[rel="'+e+'"][href="'+s+'"]',typeof a=="string"&&(s+='[crossorigin="'+a+'"]'),qg.has(s)||(qg.add(s),e={rel:e,crossOrigin:a,href:t},o.querySelector(s)===null&&(t=o.createElement("link"),sn(t,"link",e),te(t),o.head.appendChild(t)))}}function oS(e){nr.D(e),Vg("dns-prefetch",e,null)}function lS(e,t){nr.C(e,t),Vg("preconnect",e,t)}function iS(e,t,a){nr.L(e,t,a);var o=Xo;if(o&&e&&t){var s='link[rel="preload"][as="'+Jn(t)+'"]';t==="image"&&a&&a.imageSrcSet?(s+='[imagesrcset="'+Jn(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(s+='[imagesizes="'+Jn(a.imageSizes)+'"]')):s+='[href="'+Jn(e)+'"]';var f=s;switch(t){case"style":f=Ko(e);break;case"script":f=Qo(e)}la.has(f)||(e=y({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),la.set(f,e),o.querySelector(s)!==null||t==="style"&&o.querySelector(ri(f))||t==="script"&&o.querySelector(oi(f))||(t=o.createElement("link"),sn(t,"link",e),te(t),o.head.appendChild(t)))}}function sS(e,t){nr.m(e,t);var a=Xo;if(a&&e){var o=t&&typeof t.as=="string"?t.as:"script",s='link[rel="modulepreload"][as="'+Jn(o)+'"][href="'+Jn(e)+'"]',f=s;switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=Qo(e)}if(!la.has(f)&&(e=y({rel:"modulepreload",href:e},t),la.set(f,e),a.querySelector(s)===null)){switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(oi(f)))return}o=a.createElement("link"),sn(o,"link",e),te(o),a.head.appendChild(o)}}}function uS(e,t,a){nr.S(e,t,a);var o=Xo;if(o&&e){var s=va(o).hoistableStyles,f=Ko(e);t=t||"default";var g=s.get(f);if(!g){var C={loading:0,preload:null};if(g=o.querySelector(ri(f)))C.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},a),(a=la.get(f))&&Xf(e,a);var j=g=o.createElement("link");te(j),sn(j,"link",e),j._p=new Promise(function(W,ie){j.onload=W,j.onerror=ie}),j.addEventListener("load",function(){C.loading|=1}),j.addEventListener("error",function(){C.loading|=2}),C.loading|=4,Vs(g,t,o)}g={type:"stylesheet",instance:g,count:1,state:C},s.set(f,g)}}}function cS(e,t){nr.X(e,t);var a=Xo;if(a&&e){var o=va(a).hoistableScripts,s=Qo(e),f=o.get(s);f||(f=a.querySelector(oi(s)),f||(e=y({src:e,async:!0},t),(t=la.get(s))&&Kf(e,t),f=a.createElement("script"),te(f),sn(f,"link",e),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},o.set(s,f))}}function fS(e,t){nr.M(e,t);var a=Xo;if(a&&e){var o=va(a).hoistableScripts,s=Qo(e),f=o.get(s);f||(f=a.querySelector(oi(s)),f||(e=y({src:e,async:!0,type:"module"},t),(t=la.get(s))&&Kf(e,t),f=a.createElement("script"),te(f),sn(f,"link",e),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},o.set(s,f))}}function Yg(e,t,a,o){var s=(s=le.current)?qs(s):null;if(!s)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Ko(a.href),a=va(s).hoistableStyles,o=a.get(t),o||(o={type:"style",instance:null,count:0,state:null},a.set(t,o)),o):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Ko(a.href);var f=va(s).hoistableStyles,g=f.get(e);if(g||(s=s.ownerDocument||s,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(e,g),(f=s.querySelector(ri(e)))&&!f._p&&(g.instance=f,g.state.loading=5),la.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},la.set(e,a),f||dS(s,e,a,g.state))),t&&o===null)throw Error(i(528,""));return g}if(t&&o!==null)throw Error(i(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Qo(a),a=va(s).hoistableScripts,o=a.get(t),o||(o={type:"script",instance:null,count:0,state:null},a.set(t,o)),o):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Ko(e){return'href="'+Jn(e)+'"'}function ri(e){return'link[rel="stylesheet"]['+e+"]"}function Gg(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function dS(e,t,a,o){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?o.loading=1:(t=e.createElement("link"),o.preload=t,t.addEventListener("load",function(){return o.loading|=1}),t.addEventListener("error",function(){return o.loading|=2}),sn(t,"link",a),te(t),e.head.appendChild(t))}function Qo(e){return'[src="'+Jn(e)+'"]'}function oi(e){return"script[async]"+e}function Xg(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var o=e.querySelector('style[data-href~="'+Jn(a.href)+'"]');if(o)return t.instance=o,te(o),o;var s=y({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return o=(e.ownerDocument||e).createElement("style"),te(o),sn(o,"style",s),Vs(o,a.precedence,e),t.instance=o;case"stylesheet":s=Ko(a.href);var f=e.querySelector(ri(s));if(f)return t.state.loading|=4,t.instance=f,te(f),f;o=Gg(a),(s=la.get(s))&&Xf(o,s),f=(e.ownerDocument||e).createElement("link"),te(f);var g=f;return g._p=new Promise(function(C,j){g.onload=C,g.onerror=j}),sn(f,"link",o),t.state.loading|=4,Vs(f,a.precedence,e),t.instance=f;case"script":return f=Qo(a.src),(s=e.querySelector(oi(f)))?(t.instance=s,te(s),s):(o=a,(s=la.get(f))&&(o=y({},a),Kf(o,s)),e=e.ownerDocument||e,s=e.createElement("script"),te(s),sn(s,"link",o),e.head.appendChild(s),t.instance=s);case"void":return null;default:throw Error(i(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(o=t.instance,t.state.loading|=4,Vs(o,a.precedence,e));return t.instance}function Vs(e,t,a){for(var o=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=o.length?o[o.length-1]:null,f=s,g=0;g<o.length;g++){var C=o[g];if(C.dataset.precedence===t)f=C;else if(f!==s)break}f?f.parentNode.insertBefore(e,f.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Xf(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Kf(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ys=null;function Kg(e,t,a){if(Ys===null){var o=new Map,s=Ys=new Map;s.set(a,o)}else s=Ys,o=s.get(a),o||(o=new Map,s.set(a,o));if(o.has(e))return o;for(o.set(e,null),a=a.getElementsByTagName(e),s=0;s<a.length;s++){var f=a[s];if(!(f[Qn]||f[zt]||e==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var g=f.getAttribute(t)||"";g=e+g;var C=o.get(g);C?C.push(f):o.set(g,[f])}}return o}function Qg(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function pS(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Wg(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var li=null;function mS(){}function hS(e,t,a){if(li===null)throw Error(i(475));var o=li;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var s=Ko(a.href),f=e.querySelector(ri(s));if(f){e=f._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(o.count++,o=Gs.bind(o),e.then(o,o)),t.state.loading|=4,t.instance=f,te(f);return}f=e.ownerDocument||e,a=Gg(a),(s=la.get(s))&&Xf(a,s),f=f.createElement("link"),te(f);var g=f;g._p=new Promise(function(C,j){g.onload=C,g.onerror=j}),sn(f,"link",a),t.instance=f}o.stylesheets===null&&(o.stylesheets=new Map),o.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(o.count++,t=Gs.bind(o),e.addEventListener("load",t),e.addEventListener("error",t))}}function gS(){if(li===null)throw Error(i(475));var e=li;return e.stylesheets&&e.count===0&&Qf(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Qf(e,e.stylesheets),e.unsuspend){var o=e.unsuspend;e.unsuspend=null,o()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Gs(){if(this.count--,this.count===0){if(this.stylesheets)Qf(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Xs=null;function Qf(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Xs=new Map,t.forEach(yS,e),Xs=null,Gs.call(e))}function yS(e,t){if(!(t.state.loading&4)){var a=Xs.get(e);if(a)var o=a.get(null);else{a=new Map,Xs.set(e,a);for(var s=e.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<s.length;f++){var g=s[f];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(a.set(g.dataset.precedence,g),o=g)}o&&a.set(null,o)}s=t.instance,g=s.getAttribute("data-precedence"),f=a.get(g)||o,f===o&&a.set(null,s),a.set(g,s),this.count++,o=Gs.bind(this),s.addEventListener("load",o),s.addEventListener("error",o),f?f.parentNode.insertBefore(s,f.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(s,e.firstChild)),t.state.loading|=4}}var ii={$$typeof:D,Provider:null,Consumer:null,_currentValue:Y,_currentValue2:Y,_threadCount:0};function vS(e,t,a,o,s,f,g,C){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=tn(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=tn(0),this.hiddenUpdates=tn(null),this.identifierPrefix=o,this.onUncaughtError=s,this.onCaughtError=f,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=C,this.incompleteTransitions=new Map}function Fg(e,t,a,o,s,f,g,C,j,W,ie,ce){return e=new vS(e,t,a,g,C,j,W,ce),t=1,f===!0&&(t|=24),f=$n(3,null,null,t),e.current=f,f.stateNode=e,t=Ac(),t.refCount++,e.pooledCache=t,t.refCount++,f.memoizedState={element:o,isDehydrated:a,cache:t},$c(f),e}function Zg(e){return e?(e=Oo,e):Oo}function Jg(e,t,a,o,s,f){s=Zg(s),o.context===null?o.context=s:o.pendingContext=s,o=mr(t),o.payload={element:a},f=f===void 0?null:f,f!==null&&(o.callback=f),a=hr(e,o,t),a!==null&&(Ln(a,e,t),_l(a,e,t))}function ey(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function Wf(e,t){ey(e,t),(e=e.alternate)&&ey(e,t)}function ty(e){if(e.tag===13){var t=Ro(e,67108864);t!==null&&Ln(t,e,67108864),Wf(e,67108864)}}var Ks=!0;function bS(e,t,a,o){var s=T.T;T.T=null;var f=H.p;try{H.p=2,Ff(e,t,a,o)}finally{H.p=f,T.T=s}}function SS(e,t,a,o){var s=T.T;T.T=null;var f=H.p;try{H.p=8,Ff(e,t,a,o)}finally{H.p=f,T.T=s}}function Ff(e,t,a,o){if(Ks){var s=Zf(o);if(s===null)Lf(e,t,o,Qs,a),ay(e,o);else if(CS(s,e,t,a,o))o.stopPropagation();else if(ay(e,o),t&4&&-1<xS.indexOf(e)){for(;s!==null;){var f=gn(s);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var g=Ce(f.pendingLanes);if(g!==0){var C=f;for(C.pendingLanes|=2,C.entangledLanes|=2;g;){var j=1<<31-et(g);C.entanglements[1]|=j,g&=~j}Aa(f),(ht&6)===0&&(ks=at()+500,ei(0))}}break;case 13:C=Ro(f,2),C!==null&&Ln(C,f,2),js(),Wf(f,2)}if(f=Zf(o),f===null&&Lf(e,t,o,Qs,a),f===s)break;s=f}s!==null&&o.stopPropagation()}else Lf(e,t,o,null,a)}}function Zf(e){return e=nc(e),Jf(e)}var Qs=null;function Jf(e){if(Qs=null,e=Mt(e),e!==null){var t=c(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=d(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Qs=e,null}function ny(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(he()){case st:return 2;case Te:return 8;case Le:case ye:return 32;case It:return 268435456;default:return 32}default:return 32}}var ed=!1,wr=null,Ar=null,zr=null,si=new Map,ui=new Map,Dr=[],xS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ay(e,t){switch(e){case"focusin":case"focusout":wr=null;break;case"dragenter":case"dragleave":Ar=null;break;case"mouseover":case"mouseout":zr=null;break;case"pointerover":case"pointerout":si.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ui.delete(t.pointerId)}}function ci(e,t,a,o,s,f){return e===null||e.nativeEvent!==f?(e={blockedOn:t,domEventName:a,eventSystemFlags:o,nativeEvent:f,targetContainers:[s]},t!==null&&(t=gn(t),t!==null&&ty(t)),e):(e.eventSystemFlags|=o,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function CS(e,t,a,o,s){switch(t){case"focusin":return wr=ci(wr,e,t,a,o,s),!0;case"dragenter":return Ar=ci(Ar,e,t,a,o,s),!0;case"mouseover":return zr=ci(zr,e,t,a,o,s),!0;case"pointerover":var f=s.pointerId;return si.set(f,ci(si.get(f)||null,e,t,a,o,s)),!0;case"gotpointercapture":return f=s.pointerId,ui.set(f,ci(ui.get(f)||null,e,t,a,o,s)),!0}return!1}function ry(e){var t=Mt(e.target);if(t!==null){var a=c(t);if(a!==null){if(t=a.tag,t===13){if(t=d(a),t!==null){e.blockedOn=t,gl(e.priority,function(){if(a.tag===13){var o=_n();o=ur(o);var s=Ro(a,o);s!==null&&Ln(s,a,o),Wf(a,o)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ws(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Zf(e.nativeEvent);if(a===null){a=e.nativeEvent;var o=new a.constructor(a.type,a);tc=o,a.target.dispatchEvent(o),tc=null}else return t=gn(a),t!==null&&ty(t),e.blockedOn=a,!1;t.shift()}return!0}function oy(e,t,a){Ws(e)&&a.delete(t)}function TS(){ed=!1,wr!==null&&Ws(wr)&&(wr=null),Ar!==null&&Ws(Ar)&&(Ar=null),zr!==null&&Ws(zr)&&(zr=null),si.forEach(oy),ui.forEach(oy)}function Fs(e,t){e.blockedOn===t&&(e.blockedOn=null,ed||(ed=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,TS)))}var Zs=null;function ly(e){Zs!==e&&(Zs=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Zs===e&&(Zs=null);for(var t=0;t<e.length;t+=3){var a=e[t],o=e[t+1],s=e[t+2];if(typeof o!="function"){if(Jf(o||a)===null)continue;break}var f=gn(a);f!==null&&(e.splice(t,3),t-=3,Zc(f,{pending:!0,data:s,method:a.method,action:o},o,s))}}))}function fi(e){function t(j){return Fs(j,e)}wr!==null&&Fs(wr,e),Ar!==null&&Fs(Ar,e),zr!==null&&Fs(zr,e),si.forEach(t),ui.forEach(t);for(var a=0;a<Dr.length;a++){var o=Dr[a];o.blockedOn===e&&(o.blockedOn=null)}for(;0<Dr.length&&(a=Dr[0],a.blockedOn===null);)ry(a),a.blockedOn===null&&Dr.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(o=0;o<a.length;o+=3){var s=a[o],f=a[o+1],g=s[Xt]||null;if(typeof f=="function")g||ly(a);else if(g){var C=null;if(f&&f.hasAttribute("formAction")){if(s=f,g=f[Xt]||null)C=g.formAction;else if(Jf(s)!==null)continue}else C=g.action;typeof C=="function"?a[o+1]=C:(a.splice(o,3),o-=3),ly(a)}}}function td(e){this._internalRoot=e}Js.prototype.render=td.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));var a=t.current,o=_n();Jg(a,o,e,t,null,null)},Js.prototype.unmount=td.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Jg(e.current,2,null,e,null,null),js(),t[Kn]=null}};function Js(e){this._internalRoot=e}Js.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ea();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Dr.length&&t!==0&&t<Dr[a].priority;a++);Dr.splice(a,0,e),a===0&&ry(e)}};var iy=r.version;if(iy!=="19.1.0")throw Error(i(527,iy,"19.1.0"));H.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=m(t),e=e!==null?h(e):null,e=e===null?null:e.stateNode,e};var ES={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:T,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var eu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!eu.isDisabled&&eu.supportsFiber)try{rt=eu.inject(ES),je=eu}catch{}}return pi.createRoot=function(e,t){if(!u(e))throw Error(i(299));var a=!1,o="",s=Th,f=Eh,g=Rh,C=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(o=t.identifierPrefix),t.onUncaughtError!==void 0&&(s=t.onUncaughtError),t.onCaughtError!==void 0&&(f=t.onCaughtError),t.onRecoverableError!==void 0&&(g=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(C=t.unstable_transitionCallbacks)),t=Fg(e,1,!1,null,null,a,o,s,f,g,C,null),e[Kn]=t.current,_f(e),new td(t)},pi.hydrateRoot=function(e,t,a){if(!u(e))throw Error(i(299));var o=!1,s="",f=Th,g=Eh,C=Rh,j=null,W=null;return a!=null&&(a.unstable_strictMode===!0&&(o=!0),a.identifierPrefix!==void 0&&(s=a.identifierPrefix),a.onUncaughtError!==void 0&&(f=a.onUncaughtError),a.onCaughtError!==void 0&&(g=a.onCaughtError),a.onRecoverableError!==void 0&&(C=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(j=a.unstable_transitionCallbacks),a.formState!==void 0&&(W=a.formState)),t=Fg(e,1,!0,t,a??null,o,s,f,g,C,j,W),t.context=Zg(null),a=t.current,o=_n(),o=ur(o),s=mr(o),s.callback=null,hr(a,s,o),a=o,t.current.lanes=a,zn(t,a),Aa(t),e[Kn]=t.current,_f(e),new Js(t)},pi.version="19.1.0",pi}var yy;function jS(){if(yy)return rd.exports;yy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),rd.exports=$S(),rd.exports}var NS=jS();const wi={black:"#000",white:"#fff"},Wo={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Fo={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},Zo={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Jo={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},el={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"},mi={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},BS={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"};function or(n,...r){const l=new URL(`https://mui.com/production-error/?code=${n}`);return r.forEach(i=>l.searchParams.append("args[]",i)),`Minified MUI error #${n}; visit ${l} for the full message.`}const Ta="$$material";function bu(){return bu=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)({}).hasOwnProperty.call(l,i)&&(n[i]=l[i])}return n},bu.apply(null,arguments)}function _S(n){if(n.sheet)return n.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===n)return document.styleSheets[r]}function LS(n){var r=document.createElement("style");return r.setAttribute("data-emotion",n.key),n.nonce!==void 0&&r.setAttribute("nonce",n.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var HS=function(){function n(l){var i=this;this._insertTag=function(u){var c;i.tags.length===0?i.insertionPoint?c=i.insertionPoint.nextSibling:i.prepend?c=i.container.firstChild:c=i.before:c=i.tags[i.tags.length-1].nextSibling,i.container.insertBefore(u,c),i.tags.push(u)},this.isSpeedy=l.speedy===void 0?!0:l.speedy,this.tags=[],this.ctr=0,this.nonce=l.nonce,this.key=l.key,this.container=l.container,this.prepend=l.prepend,this.insertionPoint=l.insertionPoint,this.before=null}var r=n.prototype;return r.hydrate=function(i){i.forEach(this._insertTag)},r.insert=function(i){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(LS(this));var u=this.tags[this.tags.length-1];if(this.isSpeedy){var c=_S(u);try{c.insertRule(i,c.cssRules.length)}catch{}}else u.appendChild(document.createTextNode(i));this.ctr++},r.flush=function(){this.tags.forEach(function(i){var u;return(u=i.parentNode)==null?void 0:u.removeChild(i)}),this.tags=[],this.ctr=0},n}(),mn="-ms-",Su="-moz-",dt="-webkit-",Qv="comm",Gd="rule",Xd="decl",PS="@import",Wv="@keyframes",US="@layer",IS=Math.abs,wu=String.fromCharCode,qS=Object.assign;function VS(n,r){return cn(n,0)^45?(((r<<2^cn(n,0))<<2^cn(n,1))<<2^cn(n,2))<<2^cn(n,3):0}function Fv(n){return n.trim()}function YS(n,r){return(n=r.exec(n))?n[0]:n}function pt(n,r,l){return n.replace(r,l)}function xd(n,r){return n.indexOf(r)}function cn(n,r){return n.charCodeAt(r)|0}function Ai(n,r,l){return n.slice(r,l)}function $a(n){return n.length}function Kd(n){return n.length}function tu(n,r){return r.push(n),n}function GS(n,r){return n.map(r).join("")}var Au=1,sl=1,Zv=0,wn=0,Ft=0,pl="";function zu(n,r,l,i,u,c,d){return{value:n,root:r,parent:l,type:i,props:u,children:c,line:Au,column:sl,length:d,return:""}}function hi(n,r){return qS(zu("",null,null,"",null,null,0),n,{length:-n.length},r)}function XS(){return Ft}function KS(){return Ft=wn>0?cn(pl,--wn):0,sl--,Ft===10&&(sl=1,Au--),Ft}function In(){return Ft=wn<Zv?cn(pl,wn++):0,sl++,Ft===10&&(sl=1,Au++),Ft}function _a(){return cn(pl,wn)}function cu(){return wn}function Bi(n,r){return Ai(pl,n,r)}function zi(n){switch(n){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Jv(n){return Au=sl=1,Zv=$a(pl=n),wn=0,[]}function e0(n){return pl="",n}function fu(n){return Fv(Bi(wn-1,Cd(n===91?n+2:n===40?n+1:n)))}function QS(n){for(;(Ft=_a())&&Ft<33;)In();return zi(n)>2||zi(Ft)>3?"":" "}function WS(n,r){for(;--r&&In()&&!(Ft<48||Ft>102||Ft>57&&Ft<65||Ft>70&&Ft<97););return Bi(n,cu()+(r<6&&_a()==32&&In()==32))}function Cd(n){for(;In();)switch(Ft){case n:return wn;case 34:case 39:n!==34&&n!==39&&Cd(Ft);break;case 40:n===41&&Cd(n);break;case 92:In();break}return wn}function FS(n,r){for(;In()&&n+Ft!==57;)if(n+Ft===84&&_a()===47)break;return"/*"+Bi(r,wn-1)+"*"+wu(n===47?n:In())}function ZS(n){for(;!zi(_a());)In();return Bi(n,wn)}function JS(n){return e0(du("",null,null,null,[""],n=Jv(n),0,[0],n))}function du(n,r,l,i,u,c,d,p,m){for(var h=0,y=0,S=d,O=0,w=0,R=0,E=1,x=1,$=1,B=0,D="",k=u,z=c,N=i,q=D;x;)switch(R=B,B=In()){case 40:if(R!=108&&cn(q,S-1)==58){xd(q+=pt(fu(B),"&","&\f"),"&\f")!=-1&&($=-1);break}case 34:case 39:case 91:q+=fu(B);break;case 9:case 10:case 13:case 32:q+=QS(R);break;case 92:q+=WS(cu()-1,7);continue;case 47:switch(_a()){case 42:case 47:tu(ex(FS(In(),cu()),r,l),m);break;default:q+="/"}break;case 123*E:p[h++]=$a(q)*$;case 125*E:case 59:case 0:switch(B){case 0:case 125:x=0;case 59+y:$==-1&&(q=pt(q,/\f/g,"")),w>0&&$a(q)-S&&tu(w>32?by(q+";",i,l,S-1):by(pt(q," ","")+";",i,l,S-2),m);break;case 59:q+=";";default:if(tu(N=vy(q,r,l,h,y,u,p,D,k=[],z=[],S),c),B===123)if(y===0)du(q,r,N,N,k,c,S,p,z);else switch(O===99&&cn(q,3)===110?100:O){case 100:case 108:case 109:case 115:du(n,N,N,i&&tu(vy(n,N,N,0,0,u,p,D,u,k=[],S),z),u,z,S,p,i?k:z);break;default:du(q,N,N,N,[""],z,0,p,z)}}h=y=w=0,E=$=1,D=q="",S=d;break;case 58:S=1+$a(q),w=R;default:if(E<1){if(B==123)--E;else if(B==125&&E++==0&&KS()==125)continue}switch(q+=wu(B),B*E){case 38:$=y>0?1:(q+="\f",-1);break;case 44:p[h++]=($a(q)-1)*$,$=1;break;case 64:_a()===45&&(q+=fu(In())),O=_a(),y=S=$a(D=q+=ZS(cu())),B++;break;case 45:R===45&&$a(q)==2&&(E=0)}}return c}function vy(n,r,l,i,u,c,d,p,m,h,y){for(var S=u-1,O=u===0?c:[""],w=Kd(O),R=0,E=0,x=0;R<i;++R)for(var $=0,B=Ai(n,S+1,S=IS(E=d[R])),D=n;$<w;++$)(D=Fv(E>0?O[$]+" "+B:pt(B,/&\f/g,O[$])))&&(m[x++]=D);return zu(n,r,l,u===0?Gd:p,m,h,y)}function ex(n,r,l){return zu(n,r,l,Qv,wu(XS()),Ai(n,2,-2),0)}function by(n,r,l,i){return zu(n,r,l,Xd,Ai(n,0,i),Ai(n,i+1,-1),i)}function rl(n,r){for(var l="",i=Kd(n),u=0;u<i;u++)l+=r(n[u],u,n,r)||"";return l}function tx(n,r,l,i){switch(n.type){case US:if(n.children.length)break;case PS:case Xd:return n.return=n.return||n.value;case Qv:return"";case Wv:return n.return=n.value+"{"+rl(n.children,i)+"}";case Gd:n.value=n.props.join(",")}return $a(l=rl(n.children,i))?n.return=n.value+"{"+l+"}":""}function nx(n){var r=Kd(n);return function(l,i,u,c){for(var d="",p=0;p<r;p++)d+=n[p](l,i,u,c)||"";return d}}function ax(n){return function(r){r.root||(r=r.return)&&n(r)}}function t0(n){var r=Object.create(null);return function(l){return r[l]===void 0&&(r[l]=n(l)),r[l]}}var rx=function(r,l,i){for(var u=0,c=0;u=c,c=_a(),u===38&&c===12&&(l[i]=1),!zi(c);)In();return Bi(r,wn)},ox=function(r,l){var i=-1,u=44;do switch(zi(u)){case 0:u===38&&_a()===12&&(l[i]=1),r[i]+=rx(wn-1,l,i);break;case 2:r[i]+=fu(u);break;case 4:if(u===44){r[++i]=_a()===58?"&\f":"",l[i]=r[i].length;break}default:r[i]+=wu(u)}while(u=In());return r},lx=function(r,l){return e0(ox(Jv(r),l))},Sy=new WeakMap,ix=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var l=r.value,i=r.parent,u=r.column===i.column&&r.line===i.line;i.type!=="rule";)if(i=i.parent,!i)return;if(!(r.props.length===1&&l.charCodeAt(0)!==58&&!Sy.get(i))&&!u){Sy.set(r,!0);for(var c=[],d=lx(l,c),p=i.props,m=0,h=0;m<d.length;m++)for(var y=0;y<p.length;y++,h++)r.props[h]=c[m]?d[m].replace(/&\f/g,p[y]):p[y]+" "+d[m]}}},sx=function(r){if(r.type==="decl"){var l=r.value;l.charCodeAt(0)===108&&l.charCodeAt(2)===98&&(r.return="",r.value="")}};function n0(n,r){switch(VS(n,r)){case 5103:return dt+"print-"+n+n;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return dt+n+n;case 5349:case 4246:case 4810:case 6968:case 2756:return dt+n+Su+n+mn+n+n;case 6828:case 4268:return dt+n+mn+n+n;case 6165:return dt+n+mn+"flex-"+n+n;case 5187:return dt+n+pt(n,/(\w+).+(:[^]+)/,dt+"box-$1$2"+mn+"flex-$1$2")+n;case 5443:return dt+n+mn+"flex-item-"+pt(n,/flex-|-self/,"")+n;case 4675:return dt+n+mn+"flex-line-pack"+pt(n,/align-content|flex-|-self/,"")+n;case 5548:return dt+n+mn+pt(n,"shrink","negative")+n;case 5292:return dt+n+mn+pt(n,"basis","preferred-size")+n;case 6060:return dt+"box-"+pt(n,"-grow","")+dt+n+mn+pt(n,"grow","positive")+n;case 4554:return dt+pt(n,/([^-])(transform)/g,"$1"+dt+"$2")+n;case 6187:return pt(pt(pt(n,/(zoom-|grab)/,dt+"$1"),/(image-set)/,dt+"$1"),n,"")+n;case 5495:case 3959:return pt(n,/(image-set\([^]*)/,dt+"$1$`$1");case 4968:return pt(pt(n,/(.+:)(flex-)?(.*)/,dt+"box-pack:$3"+mn+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+dt+n+n;case 4095:case 3583:case 4068:case 2532:return pt(n,/(.+)-inline(.+)/,dt+"$1$2")+n;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if($a(n)-1-r>6)switch(cn(n,r+1)){case 109:if(cn(n,r+4)!==45)break;case 102:return pt(n,/(.+:)(.+)-([^]+)/,"$1"+dt+"$2-$3$1"+Su+(cn(n,r+3)==108?"$3":"$2-$3"))+n;case 115:return~xd(n,"stretch")?n0(pt(n,"stretch","fill-available"),r)+n:n}break;case 4949:if(cn(n,r+1)!==115)break;case 6444:switch(cn(n,$a(n)-3-(~xd(n,"!important")&&10))){case 107:return pt(n,":",":"+dt)+n;case 101:return pt(n,/(.+:)([^;!]+)(;|!.+)?/,"$1"+dt+(cn(n,14)===45?"inline-":"")+"box$3$1"+dt+"$2$3$1"+mn+"$2box$3")+n}break;case 5936:switch(cn(n,r+11)){case 114:return dt+n+mn+pt(n,/[svh]\w+-[tblr]{2}/,"tb")+n;case 108:return dt+n+mn+pt(n,/[svh]\w+-[tblr]{2}/,"tb-rl")+n;case 45:return dt+n+mn+pt(n,/[svh]\w+-[tblr]{2}/,"lr")+n}return dt+n+mn+n+n}return n}var ux=function(r,l,i,u){if(r.length>-1&&!r.return)switch(r.type){case Xd:r.return=n0(r.value,r.length);break;case Wv:return rl([hi(r,{value:pt(r.value,"@","@"+dt)})],u);case Gd:if(r.length)return GS(r.props,function(c){switch(YS(c,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return rl([hi(r,{props:[pt(c,/:(read-\w+)/,":"+Su+"$1")]})],u);case"::placeholder":return rl([hi(r,{props:[pt(c,/:(plac\w+)/,":"+dt+"input-$1")]}),hi(r,{props:[pt(c,/:(plac\w+)/,":"+Su+"$1")]}),hi(r,{props:[pt(c,/:(plac\w+)/,mn+"input-$1")]})],u)}return""})}},cx=[ux],fx=function(r){var l=r.key;if(l==="css"){var i=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(i,function(E){var x=E.getAttribute("data-emotion");x.indexOf(" ")!==-1&&(document.head.appendChild(E),E.setAttribute("data-s",""))})}var u=r.stylisPlugins||cx,c={},d,p=[];d=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+l+' "]'),function(E){for(var x=E.getAttribute("data-emotion").split(" "),$=1;$<x.length;$++)c[x[$]]=!0;p.push(E)});var m,h=[ix,sx];{var y,S=[tx,ax(function(E){y.insert(E)})],O=nx(h.concat(u,S)),w=function(x){return rl(JS(x),O)};m=function(x,$,B,D){y=B,w(x?x+"{"+$.styles+"}":$.styles),D&&(R.inserted[$.name]=!0)}}var R={key:l,sheet:new HS({key:l,container:d,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:c,registered:{},insert:m};return R.sheet.hydrate(p),R},sd={exports:{}},mt={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xy;function dx(){if(xy)return mt;xy=1;var n=typeof Symbol=="function"&&Symbol.for,r=n?Symbol.for("react.element"):60103,l=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,u=n?Symbol.for("react.strict_mode"):60108,c=n?Symbol.for("react.profiler"):60114,d=n?Symbol.for("react.provider"):60109,p=n?Symbol.for("react.context"):60110,m=n?Symbol.for("react.async_mode"):60111,h=n?Symbol.for("react.concurrent_mode"):60111,y=n?Symbol.for("react.forward_ref"):60112,S=n?Symbol.for("react.suspense"):60113,O=n?Symbol.for("react.suspense_list"):60120,w=n?Symbol.for("react.memo"):60115,R=n?Symbol.for("react.lazy"):60116,E=n?Symbol.for("react.block"):60121,x=n?Symbol.for("react.fundamental"):60117,$=n?Symbol.for("react.responder"):60118,B=n?Symbol.for("react.scope"):60119;function D(z){if(typeof z=="object"&&z!==null){var N=z.$$typeof;switch(N){case r:switch(z=z.type,z){case m:case h:case i:case c:case u:case S:return z;default:switch(z=z&&z.$$typeof,z){case p:case y:case R:case w:case d:return z;default:return N}}case l:return N}}}function k(z){return D(z)===h}return mt.AsyncMode=m,mt.ConcurrentMode=h,mt.ContextConsumer=p,mt.ContextProvider=d,mt.Element=r,mt.ForwardRef=y,mt.Fragment=i,mt.Lazy=R,mt.Memo=w,mt.Portal=l,mt.Profiler=c,mt.StrictMode=u,mt.Suspense=S,mt.isAsyncMode=function(z){return k(z)||D(z)===m},mt.isConcurrentMode=k,mt.isContextConsumer=function(z){return D(z)===p},mt.isContextProvider=function(z){return D(z)===d},mt.isElement=function(z){return typeof z=="object"&&z!==null&&z.$$typeof===r},mt.isForwardRef=function(z){return D(z)===y},mt.isFragment=function(z){return D(z)===i},mt.isLazy=function(z){return D(z)===R},mt.isMemo=function(z){return D(z)===w},mt.isPortal=function(z){return D(z)===l},mt.isProfiler=function(z){return D(z)===c},mt.isStrictMode=function(z){return D(z)===u},mt.isSuspense=function(z){return D(z)===S},mt.isValidElementType=function(z){return typeof z=="string"||typeof z=="function"||z===i||z===h||z===c||z===u||z===S||z===O||typeof z=="object"&&z!==null&&(z.$$typeof===R||z.$$typeof===w||z.$$typeof===d||z.$$typeof===p||z.$$typeof===y||z.$$typeof===x||z.$$typeof===$||z.$$typeof===B||z.$$typeof===E)},mt.typeOf=D,mt}var Cy;function px(){return Cy||(Cy=1,sd.exports=dx()),sd.exports}var ud,Ty;function mx(){if(Ty)return ud;Ty=1;var n=px(),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},l={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},c={};c[n.ForwardRef]=i,c[n.Memo]=u;function d(R){return n.isMemo(R)?u:c[R.$$typeof]||r}var p=Object.defineProperty,m=Object.getOwnPropertyNames,h=Object.getOwnPropertySymbols,y=Object.getOwnPropertyDescriptor,S=Object.getPrototypeOf,O=Object.prototype;function w(R,E,x){if(typeof E!="string"){if(O){var $=S(E);$&&$!==O&&w(R,$,x)}var B=m(E);h&&(B=B.concat(h(E)));for(var D=d(R),k=d(E),z=0;z<B.length;++z){var N=B[z];if(!l[N]&&!(x&&x[N])&&!(k&&k[N])&&!(D&&D[N])){var q=y(E,N);try{p(R,N,q)}catch{}}}}return R}return ud=w,ud}mx();var hx=!0;function a0(n,r,l){var i="";return l.split(" ").forEach(function(u){n[u]!==void 0?r.push(n[u]+";"):u&&(i+=u+" ")}),i}var Qd=function(r,l,i){var u=r.key+"-"+l.name;(i===!1||hx===!1)&&r.registered[u]===void 0&&(r.registered[u]=l.styles)},Wd=function(r,l,i){Qd(r,l,i);var u=r.key+"-"+l.name;if(r.inserted[l.name]===void 0){var c=l;do r.insert(l===c?"."+u:"",c,r.sheet,!0),c=c.next;while(c!==void 0)}};function gx(n){for(var r=0,l,i=0,u=n.length;u>=4;++i,u-=4)l=n.charCodeAt(i)&255|(n.charCodeAt(++i)&255)<<8|(n.charCodeAt(++i)&255)<<16|(n.charCodeAt(++i)&255)<<24,l=(l&65535)*1540483477+((l>>>16)*59797<<16),l^=l>>>24,r=(l&65535)*1540483477+((l>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(u){case 3:r^=(n.charCodeAt(i+2)&255)<<16;case 2:r^=(n.charCodeAt(i+1)&255)<<8;case 1:r^=n.charCodeAt(i)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var yx={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},vx=/[A-Z]|^ms/g,bx=/_EMO_([^_]+?)_([^]*?)_EMO_/g,r0=function(r){return r.charCodeAt(1)===45},Ey=function(r){return r!=null&&typeof r!="boolean"},cd=t0(function(n){return r0(n)?n:n.replace(vx,"-$&").toLowerCase()}),Ry=function(r,l){switch(r){case"animation":case"animationName":if(typeof l=="string")return l.replace(bx,function(i,u,c){return ja={name:u,styles:c,next:ja},u})}return yx[r]!==1&&!r0(r)&&typeof l=="number"&&l!==0?l+"px":l};function Di(n,r,l){if(l==null)return"";var i=l;if(i.__emotion_styles!==void 0)return i;switch(typeof l){case"boolean":return"";case"object":{var u=l;if(u.anim===1)return ja={name:u.name,styles:u.styles,next:ja},u.name;var c=l;if(c.styles!==void 0){var d=c.next;if(d!==void 0)for(;d!==void 0;)ja={name:d.name,styles:d.styles,next:ja},d=d.next;var p=c.styles+";";return p}return Sx(n,r,l)}case"function":{if(n!==void 0){var m=ja,h=l(n);return ja=m,Di(n,r,h)}break}}var y=l;if(r==null)return y;var S=r[y];return S!==void 0?S:y}function Sx(n,r,l){var i="";if(Array.isArray(l))for(var u=0;u<l.length;u++)i+=Di(n,r,l[u])+";";else for(var c in l){var d=l[c];if(typeof d!="object"){var p=d;r!=null&&r[p]!==void 0?i+=c+"{"+r[p]+"}":Ey(p)&&(i+=cd(c)+":"+Ry(c,p)+";")}else if(Array.isArray(d)&&typeof d[0]=="string"&&(r==null||r[d[0]]===void 0))for(var m=0;m<d.length;m++)Ey(d[m])&&(i+=cd(c)+":"+Ry(c,d[m])+";");else{var h=Di(n,r,d);switch(c){case"animation":case"animationName":{i+=cd(c)+":"+h+";";break}default:i+=c+"{"+h+"}"}}}return i}var Oy=/label:\s*([^\s;{]+)\s*(;|$)/g,ja;function _i(n,r,l){if(n.length===1&&typeof n[0]=="object"&&n[0]!==null&&n[0].styles!==void 0)return n[0];var i=!0,u="";ja=void 0;var c=n[0];if(c==null||c.raw===void 0)i=!1,u+=Di(l,r,c);else{var d=c;u+=d[0]}for(var p=1;p<n.length;p++)if(u+=Di(l,r,n[p]),i){var m=c;u+=m[p]}Oy.lastIndex=0;for(var h="",y;(y=Oy.exec(u))!==null;)h+="-"+y[1];var S=gx(u)+h;return{name:S,styles:u,next:ja}}var xx=function(r){return r()},o0=vu.useInsertionEffect?vu.useInsertionEffect:!1,l0=o0||xx,My=o0||M.useLayoutEffect,i0=M.createContext(typeof HTMLElement<"u"?fx({key:"css"}):null);i0.Provider;var Fd=function(r){return M.forwardRef(function(l,i){var u=M.useContext(i0);return r(l,u,i)})},Li=M.createContext({}),Zd={}.hasOwnProperty,Td="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",Cx=function(r,l){var i={};for(var u in l)Zd.call(l,u)&&(i[u]=l[u]);return i[Td]=r,i},Tx=function(r){var l=r.cache,i=r.serialized,u=r.isStringTag;return Qd(l,i,u),l0(function(){return Wd(l,i,u)}),null},Ex=Fd(function(n,r,l){var i=n.css;typeof i=="string"&&r.registered[i]!==void 0&&(i=r.registered[i]);var u=n[Td],c=[i],d="";typeof n.className=="string"?d=a0(r.registered,c,n.className):n.className!=null&&(d=n.className+" ");var p=_i(c,void 0,M.useContext(Li));d+=r.key+"-"+p.name;var m={};for(var h in n)Zd.call(n,h)&&h!=="css"&&h!==Td&&(m[h]=n[h]);return m.className=d,l&&(m.ref=l),M.createElement(M.Fragment,null,M.createElement(Tx,{cache:r,serialized:p,isStringTag:typeof u=="string"}),M.createElement(u,m))}),Rx=Ex,wy=function(r,l){var i=arguments;if(l==null||!Zd.call(l,"css"))return M.createElement.apply(void 0,i);var u=i.length,c=new Array(u);c[0]=Rx,c[1]=Cx(r,l);for(var d=2;d<u;d++)c[d]=i[d];return M.createElement.apply(null,c)};(function(n){var r;r||(r=n.JSX||(n.JSX={}))})(wy||(wy={}));var Ox=Fd(function(n,r){var l=n.styles,i=_i([l],void 0,M.useContext(Li)),u=M.useRef();return My(function(){var c=r.key+"-global",d=new r.sheet.constructor({key:c,nonce:r.sheet.nonce,container:r.sheet.container,speedy:r.sheet.isSpeedy}),p=!1,m=document.querySelector('style[data-emotion="'+c+" "+i.name+'"]');return r.sheet.tags.length&&(d.before=r.sheet.tags[0]),m!==null&&(p=!0,m.setAttribute("data-emotion",c),d.hydrate([m])),u.current=[d,p],function(){d.flush()}},[r]),My(function(){var c=u.current,d=c[0],p=c[1];if(p){c[1]=!1;return}if(i.next!==void 0&&Wd(r,i.next,!0),d.tags.length){var m=d.tags[d.tags.length-1].nextElementSibling;d.before=m,d.flush()}r.insert("",i,d,!1)},[r,i.name]),null});function Jd(){for(var n=arguments.length,r=new Array(n),l=0;l<n;l++)r[l]=arguments[l];return _i(r)}function Hi(){var n=Jd.apply(void 0,arguments),r="animation-"+n.name;return{name:r,styles:"@keyframes "+r+"{"+n.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var Mx=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,wx=t0(function(n){return Mx.test(n)||n.charCodeAt(0)===111&&n.charCodeAt(1)===110&&n.charCodeAt(2)<91}),Ax=wx,zx=function(r){return r!=="theme"},Ay=function(r){return typeof r=="string"&&r.charCodeAt(0)>96?Ax:zx},zy=function(r,l,i){var u;if(l){var c=l.shouldForwardProp;u=r.__emotion_forwardProp&&c?function(d){return r.__emotion_forwardProp(d)&&c(d)}:c}return typeof u!="function"&&i&&(u=r.__emotion_forwardProp),u},Dx=function(r){var l=r.cache,i=r.serialized,u=r.isStringTag;return Qd(l,i,u),l0(function(){return Wd(l,i,u)}),null},kx=function n(r,l){var i=r.__emotion_real===r,u=i&&r.__emotion_base||r,c,d;l!==void 0&&(c=l.label,d=l.target);var p=zy(r,l,i),m=p||Ay(u),h=!m("as");return function(){var y=arguments,S=i&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(c!==void 0&&S.push("label:"+c+";"),y[0]==null||y[0].raw===void 0)S.push.apply(S,y);else{var O=y[0];S.push(O[0]);for(var w=y.length,R=1;R<w;R++)S.push(y[R],O[R])}var E=Fd(function(x,$,B){var D=h&&x.as||u,k="",z=[],N=x;if(x.theme==null){N={};for(var q in x)N[q]=x[q];N.theme=M.useContext(Li)}typeof x.className=="string"?k=a0($.registered,z,x.className):x.className!=null&&(k=x.className+" ");var G=_i(S.concat(z),$.registered,N);k+=$.key+"-"+G.name,d!==void 0&&(k+=" "+d);var K=h&&p===void 0?Ay(D):m,v={};for(var _ in x)h&&_==="as"||K(_)&&(v[_]=x[_]);return v.className=k,B&&(v.ref=B),M.createElement(M.Fragment,null,M.createElement(Dx,{cache:$,serialized:G,isStringTag:typeof D=="string"}),M.createElement(D,v))});return E.displayName=c!==void 0?c:"Styled("+(typeof u=="string"?u:u.displayName||u.name||"Component")+")",E.defaultProps=r.defaultProps,E.__emotion_real=E,E.__emotion_base=u,E.__emotion_styles=S,E.__emotion_forwardProp=p,Object.defineProperty(E,"toString",{value:function(){return"."+d}}),E.withComponent=function(x,$){var B=n(x,bu({},l,$,{shouldForwardProp:zy(E,$,!0)}));return B.apply(void 0,S)},E}},$x=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],Ed=kx.bind(null);$x.forEach(function(n){Ed[n]=Ed(n)});function jx(n){return n==null||Object.keys(n).length===0}function s0(n){const{styles:r,defaultTheme:l={}}=n,i=typeof r=="function"?u=>r(jx(u)?l:u):r;return b.jsx(Ox,{styles:i})}function u0(n,r){return Ed(n,r)}function Nx(n,r){Array.isArray(n.__emotion_styles)&&(n.__emotion_styles=r(n.__emotion_styles))}const Dy=[];function Nr(n){return Dy[0]=n,_i(Dy)}var fd={exports:{}},Ct={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ky;function Bx(){if(ky)return Ct;ky=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),O=Symbol.for("react.view_transition"),w=Symbol.for("react.client.reference");function R(E){if(typeof E=="object"&&E!==null){var x=E.$$typeof;switch(x){case n:switch(E=E.type,E){case l:case u:case i:case m:case h:case O:return E;default:switch(E=E&&E.$$typeof,E){case d:case p:case S:case y:return E;case c:return E;default:return x}}case r:return x}}}return Ct.ContextConsumer=c,Ct.ContextProvider=d,Ct.Element=n,Ct.ForwardRef=p,Ct.Fragment=l,Ct.Lazy=S,Ct.Memo=y,Ct.Portal=r,Ct.Profiler=u,Ct.StrictMode=i,Ct.Suspense=m,Ct.SuspenseList=h,Ct.isContextConsumer=function(E){return R(E)===c},Ct.isContextProvider=function(E){return R(E)===d},Ct.isElement=function(E){return typeof E=="object"&&E!==null&&E.$$typeof===n},Ct.isForwardRef=function(E){return R(E)===p},Ct.isFragment=function(E){return R(E)===l},Ct.isLazy=function(E){return R(E)===S},Ct.isMemo=function(E){return R(E)===y},Ct.isPortal=function(E){return R(E)===r},Ct.isProfiler=function(E){return R(E)===u},Ct.isStrictMode=function(E){return R(E)===i},Ct.isSuspense=function(E){return R(E)===m},Ct.isSuspenseList=function(E){return R(E)===h},Ct.isValidElementType=function(E){return typeof E=="string"||typeof E=="function"||E===l||E===u||E===i||E===m||E===h||typeof E=="object"&&E!==null&&(E.$$typeof===S||E.$$typeof===y||E.$$typeof===d||E.$$typeof===c||E.$$typeof===p||E.$$typeof===w||E.getModuleId!==void 0)},Ct.typeOf=R,Ct}var $y;function _x(){return $y||($y=1,fd.exports=Bx()),fd.exports}var c0=_x();function Na(n){if(typeof n!="object"||n===null)return!1;const r=Object.getPrototypeOf(n);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)}function f0(n){if(M.isValidElement(n)||c0.isValidElementType(n)||!Na(n))return n;const r={};return Object.keys(n).forEach(l=>{r[l]=f0(n[l])}),r}function hn(n,r,l={clone:!0}){const i=l.clone?{...n}:n;return Na(n)&&Na(r)&&Object.keys(r).forEach(u=>{M.isValidElement(r[u])||c0.isValidElementType(r[u])?i[u]=r[u]:Na(r[u])&&Object.prototype.hasOwnProperty.call(n,u)&&Na(n[u])?i[u]=hn(n[u],r[u],l):l.clone?i[u]=Na(r[u])?f0(r[u]):r[u]:i[u]=r[u]}),i}const Lx=n=>{const r=Object.keys(n).map(l=>({key:l,val:n[l]}))||[];return r.sort((l,i)=>l.val-i.val),r.reduce((l,i)=>({...l,[i.key]:i.val}),{})};function Hx(n){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:l="px",step:i=5,...u}=n,c=Lx(r),d=Object.keys(c);function p(O){return`@media (min-width:${typeof r[O]=="number"?r[O]:O}${l})`}function m(O){return`@media (max-width:${(typeof r[O]=="number"?r[O]:O)-i/100}${l})`}function h(O,w){const R=d.indexOf(w);return`@media (min-width:${typeof r[O]=="number"?r[O]:O}${l}) and (max-width:${(R!==-1&&typeof r[d[R]]=="number"?r[d[R]]:w)-i/100}${l})`}function y(O){return d.indexOf(O)+1<d.length?h(O,d[d.indexOf(O)+1]):p(O)}function S(O){const w=d.indexOf(O);return w===0?p(d[1]):w===d.length-1?m(d[w]):h(O,d[d.indexOf(O)+1]).replace("@media","@media not all and")}return{keys:d,values:c,up:p,down:m,between:h,only:y,not:S,unit:l,...u}}function jy(n,r){if(!n.containerQueries)return r;const l=Object.keys(r).filter(i=>i.startsWith("@container")).sort((i,u)=>{const c=/min-width:\s*([0-9.]+)/;return+(i.match(c)?.[1]||0)-+(u.match(c)?.[1]||0)});return l.length?l.reduce((i,u)=>{const c=r[u];return delete i[u],i[u]=c,i},{...r}):r}function Px(n,r){return r==="@"||r.startsWith("@")&&(n.some(l=>r.startsWith(`@${l}`))||!!r.match(/^@\d/))}function Ux(n,r){const l=r.match(/^@([^/]+)?\/?(.+)?$/);if(!l)return null;const[,i,u]=l,c=Number.isNaN(+i)?i||0:+i;return n.containerQueries(u).up(c)}function Ix(n){const r=(c,d)=>c.replace("@media",d?`@container ${d}`:"@container");function l(c,d){c.up=(...p)=>r(n.breakpoints.up(...p),d),c.down=(...p)=>r(n.breakpoints.down(...p),d),c.between=(...p)=>r(n.breakpoints.between(...p),d),c.only=(...p)=>r(n.breakpoints.only(...p),d),c.not=(...p)=>{const m=r(n.breakpoints.not(...p),d);return m.includes("not all and")?m.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):m}}const i={},u=c=>(l(i,c),i);return l(u),{...n,containerQueries:u}}const qx={borderRadius:4};function Ti(n,r){return r?hn(n,r,{clone:!1}):n}const Du={xs:0,sm:600,md:900,lg:1200,xl:1536},Ny={keys:["xs","sm","md","lg","xl"],up:n=>`@media (min-width:${Du[n]}px)`},Vx={containerQueries:n=>({up:r=>{let l=typeof r=="number"?r:Du[r]||r;return typeof l=="number"&&(l=`${l}px`),n?`@container ${n} (min-width:${l})`:`@container (min-width:${l})`}})};function lr(n,r,l){const i=n.theme||{};if(Array.isArray(r)){const c=i.breakpoints||Ny;return r.reduce((d,p,m)=>(d[c.up(c.keys[m])]=l(r[m]),d),{})}if(typeof r=="object"){const c=i.breakpoints||Ny;return Object.keys(r).reduce((d,p)=>{if(Px(c.keys,p)){const m=Ux(i.containerQueries?i:Vx,p);m&&(d[m]=l(r[p],p))}else if(Object.keys(c.values||Du).includes(p)){const m=c.up(p);d[m]=l(r[p],p)}else{const m=p;d[m]=r[m]}return d},{})}return l(r)}function Yx(n={}){return n.keys?.reduce((l,i)=>{const u=n.up(i);return l[u]={},l},{})||{}}function By(n,r){return n.reduce((l,i)=>{const u=l[i];return(!u||Object.keys(u).length===0)&&delete l[i],l},r)}function de(n){if(typeof n!="string")throw new Error(or(7));return n.charAt(0).toUpperCase()+n.slice(1)}function ku(n,r,l=!0){if(!r||typeof r!="string")return null;if(n&&n.vars&&l){const i=`vars.${r}`.split(".").reduce((u,c)=>u&&u[c]?u[c]:null,n);if(i!=null)return i}return r.split(".").reduce((i,u)=>i&&i[u]!=null?i[u]:null,n)}function xu(n,r,l,i=l){let u;return typeof n=="function"?u=n(l):Array.isArray(n)?u=n[l]||i:u=ku(n,l)||i,r&&(u=r(u,i,n)),u}function Yt(n){const{prop:r,cssProperty:l=n.prop,themeKey:i,transform:u}=n,c=d=>{if(d[r]==null)return null;const p=d[r],m=d.theme,h=ku(m,i)||{};return lr(d,p,S=>{let O=xu(h,u,S);return S===O&&typeof S=="string"&&(O=xu(h,u,`${r}${S==="default"?"":de(S)}`,S)),l===!1?O:{[l]:O}})};return c.propTypes={},c.filterProps=[r],c}function Gx(n){const r={};return l=>(r[l]===void 0&&(r[l]=n(l)),r[l])}const Xx={m:"margin",p:"padding"},Kx={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},_y={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Qx=Gx(n=>{if(n.length>2)if(_y[n])n=_y[n];else return[n];const[r,l]=n.split(""),i=Xx[r],u=Kx[l]||"";return Array.isArray(u)?u.map(c=>i+c):[i+u]}),ep=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],tp=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...ep,...tp];function Pi(n,r,l,i){const u=ku(n,r,!0)??l;return typeof u=="number"||typeof u=="string"?c=>typeof c=="string"?c:typeof u=="string"?u.startsWith("var(")&&c===0?0:u.startsWith("var(")&&c===1?u:`calc(${c} * ${u})`:u*c:Array.isArray(u)?c=>{if(typeof c=="string")return c;const d=Math.abs(c),p=u[d];return c>=0?p:typeof p=="number"?-p:typeof p=="string"&&p.startsWith("var(")?`calc(-1 * ${p})`:`-${p}`}:typeof u=="function"?u:()=>{}}function np(n){return Pi(n,"spacing",8)}function Ui(n,r){return typeof r=="string"||r==null?r:n(r)}function Wx(n,r){return l=>n.reduce((i,u)=>(i[u]=Ui(r,l),i),{})}function Fx(n,r,l,i){if(!r.includes(l))return null;const u=Qx(l),c=Wx(u,i),d=n[l];return lr(n,d,c)}function d0(n,r){const l=np(n.theme);return Object.keys(n).map(i=>Fx(n,r,i,l)).reduce(Ti,{})}function Pt(n){return d0(n,ep)}Pt.propTypes={};Pt.filterProps=ep;function Ut(n){return d0(n,tp)}Ut.propTypes={};Ut.filterProps=tp;function p0(n=8,r=np({spacing:n})){if(n.mui)return n;const l=(...i)=>(i.length===0?[1]:i).map(c=>{const d=r(c);return typeof d=="number"?`${d}px`:d}).join(" ");return l.mui=!0,l}function $u(...n){const r=n.reduce((i,u)=>(u.filterProps.forEach(c=>{i[c]=u}),i),{}),l=i=>Object.keys(i).reduce((u,c)=>r[c]?Ti(u,r[c](i)):u,{});return l.propTypes={},l.filterProps=n.reduce((i,u)=>i.concat(u.filterProps),[]),l}function ua(n){return typeof n!="number"?n:`${n}px solid`}function ma(n,r){return Yt({prop:n,themeKey:"borders",transform:r})}const Zx=ma("border",ua),Jx=ma("borderTop",ua),e2=ma("borderRight",ua),t2=ma("borderBottom",ua),n2=ma("borderLeft",ua),a2=ma("borderColor"),r2=ma("borderTopColor"),o2=ma("borderRightColor"),l2=ma("borderBottomColor"),i2=ma("borderLeftColor"),s2=ma("outline",ua),u2=ma("outlineColor"),ju=n=>{if(n.borderRadius!==void 0&&n.borderRadius!==null){const r=Pi(n.theme,"shape.borderRadius",4),l=i=>({borderRadius:Ui(r,i)});return lr(n,n.borderRadius,l)}return null};ju.propTypes={};ju.filterProps=["borderRadius"];$u(Zx,Jx,e2,t2,n2,a2,r2,o2,l2,i2,ju,s2,u2);const Nu=n=>{if(n.gap!==void 0&&n.gap!==null){const r=Pi(n.theme,"spacing",8),l=i=>({gap:Ui(r,i)});return lr(n,n.gap,l)}return null};Nu.propTypes={};Nu.filterProps=["gap"];const Bu=n=>{if(n.columnGap!==void 0&&n.columnGap!==null){const r=Pi(n.theme,"spacing",8),l=i=>({columnGap:Ui(r,i)});return lr(n,n.columnGap,l)}return null};Bu.propTypes={};Bu.filterProps=["columnGap"];const _u=n=>{if(n.rowGap!==void 0&&n.rowGap!==null){const r=Pi(n.theme,"spacing",8),l=i=>({rowGap:Ui(r,i)});return lr(n,n.rowGap,l)}return null};_u.propTypes={};_u.filterProps=["rowGap"];const c2=Yt({prop:"gridColumn"}),f2=Yt({prop:"gridRow"}),d2=Yt({prop:"gridAutoFlow"}),p2=Yt({prop:"gridAutoColumns"}),m2=Yt({prop:"gridAutoRows"}),h2=Yt({prop:"gridTemplateColumns"}),g2=Yt({prop:"gridTemplateRows"}),y2=Yt({prop:"gridTemplateAreas"}),v2=Yt({prop:"gridArea"});$u(Nu,Bu,_u,c2,f2,d2,p2,m2,h2,g2,y2,v2);function ol(n,r){return r==="grey"?r:n}const b2=Yt({prop:"color",themeKey:"palette",transform:ol}),S2=Yt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:ol}),x2=Yt({prop:"backgroundColor",themeKey:"palette",transform:ol});$u(b2,S2,x2);function Un(n){return n<=1&&n!==0?`${n*100}%`:n}const C2=Yt({prop:"width",transform:Un}),ap=n=>{if(n.maxWidth!==void 0&&n.maxWidth!==null){const r=l=>{const i=n.theme?.breakpoints?.values?.[l]||Du[l];return i?n.theme?.breakpoints?.unit!=="px"?{maxWidth:`${i}${n.theme.breakpoints.unit}`}:{maxWidth:i}:{maxWidth:Un(l)}};return lr(n,n.maxWidth,r)}return null};ap.filterProps=["maxWidth"];const T2=Yt({prop:"minWidth",transform:Un}),E2=Yt({prop:"height",transform:Un}),R2=Yt({prop:"maxHeight",transform:Un}),O2=Yt({prop:"minHeight",transform:Un});Yt({prop:"size",cssProperty:"width",transform:Un});Yt({prop:"size",cssProperty:"height",transform:Un});const M2=Yt({prop:"boxSizing"});$u(C2,ap,T2,E2,R2,O2,M2);const Ii={border:{themeKey:"borders",transform:ua},borderTop:{themeKey:"borders",transform:ua},borderRight:{themeKey:"borders",transform:ua},borderBottom:{themeKey:"borders",transform:ua},borderLeft:{themeKey:"borders",transform:ua},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:ua},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:ju},color:{themeKey:"palette",transform:ol},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:ol},backgroundColor:{themeKey:"palette",transform:ol},p:{style:Ut},pt:{style:Ut},pr:{style:Ut},pb:{style:Ut},pl:{style:Ut},px:{style:Ut},py:{style:Ut},padding:{style:Ut},paddingTop:{style:Ut},paddingRight:{style:Ut},paddingBottom:{style:Ut},paddingLeft:{style:Ut},paddingX:{style:Ut},paddingY:{style:Ut},paddingInline:{style:Ut},paddingInlineStart:{style:Ut},paddingInlineEnd:{style:Ut},paddingBlock:{style:Ut},paddingBlockStart:{style:Ut},paddingBlockEnd:{style:Ut},m:{style:Pt},mt:{style:Pt},mr:{style:Pt},mb:{style:Pt},ml:{style:Pt},mx:{style:Pt},my:{style:Pt},margin:{style:Pt},marginTop:{style:Pt},marginRight:{style:Pt},marginBottom:{style:Pt},marginLeft:{style:Pt},marginX:{style:Pt},marginY:{style:Pt},marginInline:{style:Pt},marginInlineStart:{style:Pt},marginInlineEnd:{style:Pt},marginBlock:{style:Pt},marginBlockStart:{style:Pt},marginBlockEnd:{style:Pt},displayPrint:{cssProperty:!1,transform:n=>({"@media print":{display:n}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Nu},rowGap:{style:_u},columnGap:{style:Bu},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Un},maxWidth:{style:ap},minWidth:{transform:Un},height:{transform:Un},maxHeight:{transform:Un},minHeight:{transform:Un},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function w2(...n){const r=n.reduce((i,u)=>i.concat(Object.keys(u)),[]),l=new Set(r);return n.every(i=>l.size===Object.keys(i).length)}function A2(n,r){return typeof n=="function"?n(r):n}function z2(){function n(l,i,u,c){const d={[l]:i,theme:u},p=c[l];if(!p)return{[l]:i};const{cssProperty:m=l,themeKey:h,transform:y,style:S}=p;if(i==null)return null;if(h==="typography"&&i==="inherit")return{[l]:i};const O=ku(u,h)||{};return S?S(d):lr(d,i,R=>{let E=xu(O,y,R);return R===E&&typeof R=="string"&&(E=xu(O,y,`${l}${R==="default"?"":de(R)}`,R)),m===!1?E:{[m]:E}})}function r(l){const{sx:i,theme:u={},nested:c}=l||{};if(!i)return null;const d=u.unstable_sxConfig??Ii;function p(m){let h=m;if(typeof m=="function")h=m(u);else if(typeof m!="object")return m;if(!h)return null;const y=Yx(u.breakpoints),S=Object.keys(y);let O=y;return Object.keys(h).forEach(w=>{const R=A2(h[w],u);if(R!=null)if(typeof R=="object")if(d[w])O=Ti(O,n(w,R,u,d));else{const E=lr({theme:u},R,x=>({[w]:x}));w2(E,R)?O[w]=r({sx:R,theme:u,nested:!0}):O=Ti(O,E)}else O=Ti(O,n(w,R,u,d))}),!c&&u.modularCssLayers?{"@layer sx":jy(u,By(S,O))}:jy(u,By(S,O))}return Array.isArray(i)?i.map(p):p(i)}return r}const Br=z2();Br.filterProps=["sx"];function D2(n,r){const l=this;if(l.vars){if(!l.colorSchemes?.[n]||typeof l.getColorSchemeSelector!="function")return{};let i=l.getColorSchemeSelector(n);return i==="&"?r:((i.includes("data-")||i.includes("."))&&(i=`*:where(${i.replace(/\s*&$/,"")}) &`),{[i]:r})}return l.palette.mode===n?r:{}}function Lu(n={},...r){const{breakpoints:l={},palette:i={},spacing:u,shape:c={},...d}=n,p=Hx(l),m=p0(u);let h=hn({breakpoints:p,direction:"ltr",components:{},palette:{mode:"light",...i},spacing:m,shape:{...qx,...c}},d);return h=Ix(h),h.applyStyles=D2,h=r.reduce((y,S)=>hn(y,S),h),h.unstable_sxConfig={...Ii,...d?.unstable_sxConfig},h.unstable_sx=function(S){return Br({sx:S,theme:this})},h}function k2(n){return Object.keys(n).length===0}function Hu(n=null){const r=M.useContext(Li);return!r||k2(r)?n:r}const $2=Lu();function qi(n=$2){return Hu(n)}function dd(n){const r=Nr(n);return n!==r&&r.styles?(r.styles.match(/^@layer\s+[^{]*$/)||(r.styles=`@layer global{${r.styles}}`),r):n}function m0({styles:n,themeId:r,defaultTheme:l={}}){const i=qi(l),u=r&&i[r]||i;let c=typeof n=="function"?n(u):n;return u.modularCssLayers&&(Array.isArray(c)?c=c.map(d=>dd(typeof d=="function"?d(u):d)):c=dd(c)),b.jsx(s0,{styles:c})}const j2=n=>{const r={systemProps:{},otherProps:{}},l=n?.theme?.unstable_sxConfig??Ii;return Object.keys(n).forEach(i=>{l[i]?r.systemProps[i]=n[i]:r.otherProps[i]=n[i]}),r};function rp(n){const{sx:r,...l}=n,{systemProps:i,otherProps:u}=j2(l);let c;return Array.isArray(r)?c=[i,...r]:typeof r=="function"?c=(...d)=>{const p=r(...d);return Na(p)?{...i,...p}:i}:c={...i,...r},{...u,sx:c}}const Ly=n=>n,N2=()=>{let n=Ly;return{configure(r){n=r},generate(r){return n(r)},reset(){n=Ly}}},h0=N2();function g0(n){var r,l,i="";if(typeof n=="string"||typeof n=="number")i+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(r=0;r<u;r++)n[r]&&(l=g0(n[r]))&&(i&&(i+=" "),i+=l)}else for(l in n)n[l]&&(i&&(i+=" "),i+=l);return i}function Me(){for(var n,r,l=0,i="",u=arguments.length;l<u;l++)(n=arguments[l])&&(r=g0(n))&&(i&&(i+=" "),i+=r);return i}function B2(n={}){const{themeId:r,defaultTheme:l,defaultClassName:i="MuiBox-root",generateClassName:u}=n,c=u0("div",{shouldForwardProp:p=>p!=="theme"&&p!=="sx"&&p!=="as"})(Br);return M.forwardRef(function(m,h){const y=qi(l),{className:S,component:O="div",...w}=rp(m);return b.jsx(c,{as:O,ref:h,className:Me(S,u?u(i):i),theme:r&&y[r]||y,...w})})}const _2={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Xe(n,r,l="Mui"){const i=_2[r];return i?`${l}-${i}`:`${h0.generate(n)}-${r}`}function Ke(n,r,l="Mui"){const i={};return r.forEach(u=>{i[u]=Xe(n,u,l)}),i}function y0(n){const{variants:r,...l}=n,i={variants:r,style:Nr(l),isProcessed:!0};return i.style===l||r&&r.forEach(u=>{typeof u.style!="function"&&(u.style=Nr(u.style))}),i}const L2=Lu();function pd(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}function io(n,r){return r&&n&&typeof n=="object"&&n.styles&&!n.styles.startsWith("@layer")&&(n.styles=`@layer ${r}{${String(n.styles)}}`),n}function H2(n){return n?(r,l)=>l[n]:null}function P2(n,r,l){n.theme=I2(n.theme)?l:n.theme[r]||n.theme}function pu(n,r,l){const i=typeof r=="function"?r(n):r;if(Array.isArray(i))return i.flatMap(u=>pu(n,u,l));if(Array.isArray(i?.variants)){let u;if(i.isProcessed)u=l?io(i.style,l):i.style;else{const{variants:c,...d}=i;u=l?io(Nr(d),l):d}return v0(n,i.variants,[u],l)}return i?.isProcessed?l?io(Nr(i.style),l):i.style:l?io(Nr(i),l):i}function v0(n,r,l=[],i=void 0){let u;e:for(let c=0;c<r.length;c+=1){const d=r[c];if(typeof d.props=="function"){if(u??={...n,...n.ownerState,ownerState:n.ownerState},!d.props(u))continue}else for(const p in d.props)if(n[p]!==d.props[p]&&n.ownerState?.[p]!==d.props[p])continue e;typeof d.style=="function"?(u??={...n,...n.ownerState,ownerState:n.ownerState},l.push(i?io(Nr(d.style(u)),i):d.style(u))):l.push(i?io(Nr(d.style),i):d.style)}return l}function b0(n={}){const{themeId:r,defaultTheme:l=L2,rootShouldForwardProp:i=pd,slotShouldForwardProp:u=pd}=n;function c(p){P2(p,r,l)}return(p,m={})=>{Nx(p,N=>N.filter(q=>q!==Br));const{name:h,slot:y,skipVariantsResolver:S,skipSx:O,overridesResolver:w=H2(V2(y)),...R}=m,E=h&&h.startsWith("Mui")||y?"components":"custom",x=S!==void 0?S:y&&y!=="Root"&&y!=="root"||!1,$=O||!1;let B=pd;y==="Root"||y==="root"?B=i:y?B=u:q2(p)&&(B=void 0);const D=u0(p,{shouldForwardProp:B,label:U2(),...R}),k=N=>{if(N.__emotion_real===N)return N;if(typeof N=="function")return function(G){return pu(G,N,G.theme.modularCssLayers?E:void 0)};if(Na(N)){const q=y0(N);return function(K){return q.variants?pu(K,q,K.theme.modularCssLayers?E:void 0):K.theme.modularCssLayers?io(q.style,E):q.style}}return N},z=(...N)=>{const q=[],G=N.map(k),K=[];if(q.push(c),h&&w&&K.push(function(ae){const L=ae.theme.components?.[h]?.styleOverrides;if(!L)return null;const T={};for(const H in L)T[H]=pu(ae,L[H],ae.theme.modularCssLayers?"theme":void 0);return w(ae,T)}),h&&!x&&K.push(function(ae){const L=ae.theme?.components?.[h]?.variants;return L?v0(ae,L,[],ae.theme.modularCssLayers?"theme":void 0):null}),$||K.push(Br),Array.isArray(G[0])){const V=G.shift(),ae=new Array(q.length).fill(""),ee=new Array(K.length).fill("");let L;L=[...ae,...V,...ee],L.raw=[...ae,...V.raw,...ee],q.unshift(L)}const v=[...q,...G,...K],_=D(...v);return p.muiName&&(_.muiName=p.muiName),_};return D.withConfig&&(z.withConfig=D.withConfig),z}}function U2(n,r){return void 0}function I2(n){for(const r in n)return!1;return!0}function q2(n){return typeof n=="string"&&n.charCodeAt(0)>96}function V2(n){return n&&n.charAt(0).toLowerCase()+n.slice(1)}const Y2=b0();function ki(n,r,l=!1){const i={...r};for(const u in n)if(Object.prototype.hasOwnProperty.call(n,u)){const c=u;if(c==="components"||c==="slots")i[c]={...n[c],...i[c]};else if(c==="componentsProps"||c==="slotProps"){const d=n[c],p=r[c];if(!p)i[c]=d||{};else if(!d)i[c]=p;else{i[c]={...p};for(const m in d)if(Object.prototype.hasOwnProperty.call(d,m)){const h=m;i[c][h]=ki(d[h],p[h],l)}}}else c==="className"&&l&&r.className?i.className=Me(n?.className,r?.className):c==="style"&&l&&r.style?i.style={...n?.style,...r?.style}:i[c]===void 0&&(i[c]=n[c])}return i}function S0(n){const{theme:r,name:l,props:i}=n;return!r||!r.components||!r.components[l]||!r.components[l].defaultProps?i:ki(r.components[l].defaultProps,i)}function G2({props:n,name:r,defaultTheme:l,themeId:i}){let u=qi(l);return i&&(u=u[i]||u),S0({theme:u,name:r,props:n})}const fa=typeof window<"u"?M.useLayoutEffect:M.useEffect;function X2(n,r,l,i,u){const[c,d]=M.useState(()=>u&&l?l(n).matches:i?i(n).matches:r);return fa(()=>{if(!l)return;const p=l(n),m=()=>{d(p.matches)};return m(),p.addEventListener("change",m),()=>{p.removeEventListener("change",m)}},[n,l]),c}const K2={...vu},x0=K2.useSyncExternalStore;function Q2(n,r,l,i,u){const c=M.useCallback(()=>r,[r]),d=M.useMemo(()=>{if(u&&l)return()=>l(n).matches;if(i!==null){const{matches:y}=i(n);return()=>y}return c},[c,n,i,u,l]),[p,m]=M.useMemo(()=>{if(l===null)return[c,()=>()=>{}];const y=l(n);return[()=>y.matches,S=>(y.addEventListener("change",S),()=>{y.removeEventListener("change",S)})]},[c,l,n]);return x0(m,p,d)}function C0(n={}){const{themeId:r}=n;return function(i,u={}){let c=Hu();c&&r&&(c=c[r]||c);const d=typeof window<"u"&&typeof window.matchMedia<"u",{defaultMatches:p=!1,matchMedia:m=d?window.matchMedia:null,ssrMatchMedia:h=null,noSsr:y=!1}=S0({name:"MuiUseMediaQuery",props:u,theme:c});let S=typeof i=="function"?i(c):i;return S=S.replace(/^@media( ?)/m,""),S.includes("print")&&console.warn(["MUI: You have provided a `print` query to the `useMediaQuery` hook.","Using the print media query to modify print styles can lead to unexpected results.","Consider using the `displayPrint` field in the `sx` prop instead.","More information about `displayPrint` on our docs: https://mui.com/system/display/#display-in-print."].join(`
`)),(x0!==void 0?Q2:X2)(S,p,m,h,y)}}C0();function W2(n,r=Number.MIN_SAFE_INTEGER,l=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(n,l))}function op(n,r=0,l=1){return W2(n,r,l)}function F2(n){n=n.slice(1);const r=new RegExp(`.{1,${n.length>=6?2:1}}`,"g");let l=n.match(r);return l&&l[0].length===1&&(l=l.map(i=>i+i)),l?`rgb${l.length===4?"a":""}(${l.map((i,u)=>u<3?parseInt(i,16):Math.round(parseInt(i,16)/255*1e3)/1e3).join(", ")})`:""}function _r(n){if(n.type)return n;if(n.charAt(0)==="#")return _r(F2(n));const r=n.indexOf("("),l=n.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(l))throw new Error(or(9,n));let i=n.substring(r+1,n.length-1),u;if(l==="color"){if(i=i.split(" "),u=i.shift(),i.length===4&&i[3].charAt(0)==="/"&&(i[3]=i[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(u))throw new Error(or(10,u))}else i=i.split(",");return i=i.map(c=>parseFloat(c)),{type:l,values:i,colorSpace:u}}const Z2=n=>{const r=_r(n);return r.values.slice(0,3).map((l,i)=>r.type.includes("hsl")&&i!==0?`${l}%`:l).join(" ")},bi=(n,r)=>{try{return Z2(n)}catch{return n}};function Pu(n){const{type:r,colorSpace:l}=n;let{values:i}=n;return r.includes("rgb")?i=i.map((u,c)=>c<3?parseInt(u,10):u):r.includes("hsl")&&(i[1]=`${i[1]}%`,i[2]=`${i[2]}%`),r.includes("color")?i=`${l} ${i.join(" ")}`:i=`${i.join(", ")}`,`${r}(${i})`}function T0(n){n=_r(n);const{values:r}=n,l=r[0],i=r[1]/100,u=r[2]/100,c=i*Math.min(u,1-u),d=(h,y=(h+l/30)%12)=>u-c*Math.max(Math.min(y-3,9-y,1),-1);let p="rgb";const m=[Math.round(d(0)*255),Math.round(d(8)*255),Math.round(d(4)*255)];return n.type==="hsla"&&(p+="a",m.push(r[3])),Pu({type:p,values:m})}function Rd(n){n=_r(n);let r=n.type==="hsl"||n.type==="hsla"?_r(T0(n)).values:n.values;return r=r.map(l=>(n.type!=="color"&&(l/=255),l<=.03928?l/12.92:((l+.055)/1.055)**2.4)),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function J2(n,r){const l=Rd(n),i=Rd(r);return(Math.max(l,i)+.05)/(Math.min(l,i)+.05)}function yt(n,r){return n=_r(n),r=op(r),(n.type==="rgb"||n.type==="hsl")&&(n.type+="a"),n.type==="color"?n.values[3]=`/${r}`:n.values[3]=r,Pu(n)}function nu(n,r,l){try{return yt(n,r)}catch{return n}}function Uu(n,r){if(n=_r(n),r=op(r),n.type.includes("hsl"))n.values[2]*=1-r;else if(n.type.includes("rgb")||n.type.includes("color"))for(let l=0;l<3;l+=1)n.values[l]*=1-r;return Pu(n)}function Et(n,r,l){try{return Uu(n,r)}catch{return n}}function Iu(n,r){if(n=_r(n),r=op(r),n.type.includes("hsl"))n.values[2]+=(100-n.values[2])*r;else if(n.type.includes("rgb"))for(let l=0;l<3;l+=1)n.values[l]+=(255-n.values[l])*r;else if(n.type.includes("color"))for(let l=0;l<3;l+=1)n.values[l]+=(1-n.values[l])*r;return Pu(n)}function Rt(n,r,l){try{return Iu(n,r)}catch{return n}}function eC(n,r=.15){return Rd(n)>.5?Uu(n,r):Iu(n,r)}function au(n,r,l){try{return eC(n,r)}catch{return n}}const E0=M.createContext(null);function lp(){return M.useContext(E0)}const tC=typeof Symbol=="function"&&Symbol.for,nC=tC?Symbol.for("mui.nested"):"__THEME_NESTED__";function aC(n,r){return typeof r=="function"?r(n):{...n,...r}}function rC(n){const{children:r,theme:l}=n,i=lp(),u=M.useMemo(()=>{const c=i===null?{...l}:aC(i,l);return c!=null&&(c[nC]=i!==null),c},[l,i]);return b.jsx(E0.Provider,{value:u,children:r})}const R0=M.createContext();function oC({value:n,...r}){return b.jsx(R0.Provider,{value:n??!0,...r})}const O0=()=>M.useContext(R0)??!1,M0=M.createContext(void 0);function lC({value:n,children:r}){return b.jsx(M0.Provider,{value:n,children:r})}function iC(n){const{theme:r,name:l,props:i}=n;if(!r||!r.components||!r.components[l])return i;const u=r.components[l];return u.defaultProps?ki(u.defaultProps,i,r.components.mergeClassNameAndStyle):!u.styleOverrides&&!u.variants?ki(u,i,r.components.mergeClassNameAndStyle):i}function sC({props:n,name:r}){const l=M.useContext(M0);return iC({props:n,name:r,theme:{components:l}})}let Hy=0;function uC(n){const[r,l]=M.useState(n),i=n||r;return M.useEffect(()=>{r==null&&(Hy+=1,l(`mui-${Hy}`))},[r]),i}const cC={...vu},Py=cC.useId;function ml(n){if(Py!==void 0){const r=Py();return n??r}return uC(n)}function fC(n){const r=Hu(),l=ml()||"",{modularCssLayers:i}=n;let u="mui.global, mui.components, mui.theme, mui.custom, mui.sx";return!i||r!==null?u="":typeof i=="string"?u=i.replace(/mui(?!\.)/g,u):u=`@layer ${u};`,fa(()=>{const c=document.querySelector("head");if(!c)return;const d=c.firstChild;if(u){if(d&&d.hasAttribute?.("data-mui-layer-order")&&d.getAttribute("data-mui-layer-order")===l)return;const p=document.createElement("style");p.setAttribute("data-mui-layer-order",l),p.textContent=u,c.prepend(p)}else c.querySelector(`style[data-mui-layer-order="${l}"]`)?.remove()},[u,l]),u?b.jsx(m0,{styles:u}):null}const Uy={};function Iy(n,r,l,i=!1){return M.useMemo(()=>{const u=n&&r[n]||r;if(typeof l=="function"){const c=l(u),d=n?{...r,[n]:c}:c;return i?()=>d:d}return n?{...r,[n]:l}:{...r,...l}},[n,r,l,i])}function w0(n){const{children:r,theme:l,themeId:i}=n,u=Hu(Uy),c=lp()||Uy,d=Iy(i,u,l),p=Iy(i,c,l,!0),m=(i?d[i]:d).direction==="rtl",h=fC(d);return b.jsx(rC,{theme:p,children:b.jsx(Li.Provider,{value:d,children:b.jsx(oC,{value:m,children:b.jsxs(lC,{value:i?d[i].components:d.components,children:[h,r]})})})})}const qy={theme:void 0};function dC(n){let r,l;return function(u){let c=r;return(c===void 0||u.theme!==l)&&(qy.theme=u.theme,c=y0(n(qy)),r=c,l=u.theme),c}}const ip="mode",sp="color-scheme",pC="data-color-scheme";function mC(n){const{defaultMode:r="system",defaultLightColorScheme:l="light",defaultDarkColorScheme:i="dark",modeStorageKey:u=ip,colorSchemeStorageKey:c=sp,attribute:d=pC,colorSchemeNode:p="document.documentElement",nonce:m}=n||{};let h="",y=d;if(d==="class"&&(y=".%s"),d==="data"&&(y="[data-%s]"),y.startsWith(".")){const O=y.substring(1);h+=`${p}.classList.remove('${O}'.replace('%s', light), '${O}'.replace('%s', dark));
      ${p}.classList.add('${O}'.replace('%s', colorScheme));`}const S=y.match(/\[([^\]]+)\]/);if(S){const[O,w]=S[1].split("=");w||(h+=`${p}.removeAttribute('${O}'.replace('%s', light));
      ${p}.removeAttribute('${O}'.replace('%s', dark));`),h+=`
      ${p}.setAttribute('${O}'.replace('%s', colorScheme), ${w?`${w}.replace('%s', colorScheme)`:'""'});`}else h+=`${p}.setAttribute('${y}', colorScheme);`;return b.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?m:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${u}') || '${r}';
  const dark = localStorage.getItem('${c}-dark') || '${i}';
  const light = localStorage.getItem('${c}-light') || '${l}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${h}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function hC(){}const gC=({key:n,storageWindow:r})=>(!r&&typeof window<"u"&&(r=window),{get(l){if(typeof window>"u")return;if(!r)return l;let i;try{i=r.localStorage.getItem(n)}catch{}return i||l},set:l=>{if(r)try{r.localStorage.setItem(n,l)}catch{}},subscribe:l=>{if(!r)return hC;const i=u=>{const c=u.newValue;u.key===n&&l(c)};return r.addEventListener("storage",i),()=>{r.removeEventListener("storage",i)}}});function md(){}function Vy(n){if(typeof window<"u"&&typeof window.matchMedia=="function"&&n==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function A0(n,r){if(n.mode==="light"||n.mode==="system"&&n.systemMode==="light")return r("light");if(n.mode==="dark"||n.mode==="system"&&n.systemMode==="dark")return r("dark")}function yC(n){return A0(n,r=>{if(r==="light")return n.lightColorScheme;if(r==="dark")return n.darkColorScheme})}function vC(n){const{defaultMode:r="light",defaultLightColorScheme:l,defaultDarkColorScheme:i,supportedColorSchemes:u=[],modeStorageKey:c=ip,colorSchemeStorageKey:d=sp,storageWindow:p=typeof window>"u"?void 0:window,storageManager:m=gC,noSsr:h=!1}=n,y=u.join(","),S=u.length>1,O=M.useMemo(()=>m?.({key:c,storageWindow:p}),[m,c,p]),w=M.useMemo(()=>m?.({key:`${d}-light`,storageWindow:p}),[m,d,p]),R=M.useMemo(()=>m?.({key:`${d}-dark`,storageWindow:p}),[m,d,p]),[E,x]=M.useState(()=>{const G=O?.get(r)||r,K=w?.get(l)||l,v=R?.get(i)||i;return{mode:G,systemMode:Vy(G),lightColorScheme:K,darkColorScheme:v}}),[$,B]=M.useState(h||!S);M.useEffect(()=>{B(!0)},[]);const D=yC(E),k=M.useCallback(G=>{x(K=>{if(G===K.mode)return K;const v=G??r;return O?.set(v),{...K,mode:v,systemMode:Vy(v)}})},[O,r]),z=M.useCallback(G=>{G?typeof G=="string"?G&&!y.includes(G)?console.error(`\`${G}\` does not exist in \`theme.colorSchemes\`.`):x(K=>{const v={...K};return A0(K,_=>{_==="light"&&(w?.set(G),v.lightColorScheme=G),_==="dark"&&(R?.set(G),v.darkColorScheme=G)}),v}):x(K=>{const v={...K},_=G.light===null?l:G.light,V=G.dark===null?i:G.dark;return _&&(y.includes(_)?(v.lightColorScheme=_,w?.set(_)):console.error(`\`${_}\` does not exist in \`theme.colorSchemes\`.`)),V&&(y.includes(V)?(v.darkColorScheme=V,R?.set(V)):console.error(`\`${V}\` does not exist in \`theme.colorSchemes\`.`)),v}):x(K=>(w?.set(l),R?.set(i),{...K,lightColorScheme:l,darkColorScheme:i}))},[y,w,R,l,i]),N=M.useCallback(G=>{E.mode==="system"&&x(K=>{const v=G?.matches?"dark":"light";return K.systemMode===v?K:{...K,systemMode:v}})},[E.mode]),q=M.useRef(N);return q.current=N,M.useEffect(()=>{if(typeof window.matchMedia!="function"||!S)return;const G=(...v)=>q.current(...v),K=window.matchMedia("(prefers-color-scheme: dark)");return K.addListener(G),G(K),()=>{K.removeListener(G)}},[S]),M.useEffect(()=>{if(S){const G=O?.subscribe(_=>{(!_||["light","dark","system"].includes(_))&&k(_||r)})||md,K=w?.subscribe(_=>{(!_||y.match(_))&&z({light:_})})||md,v=R?.subscribe(_=>{(!_||y.match(_))&&z({dark:_})})||md;return()=>{G(),K(),v()}}},[z,k,y,r,p,S,O,w,R]),{...E,mode:$?E.mode:void 0,systemMode:$?E.systemMode:void 0,colorScheme:$?D:void 0,setMode:k,setColorScheme:z}}const bC="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function SC(n){const{themeId:r,theme:l={},modeStorageKey:i=ip,colorSchemeStorageKey:u=sp,disableTransitionOnChange:c=!1,defaultColorScheme:d,resolveTheme:p}=n,m={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},h=M.createContext(void 0),y=()=>M.useContext(h)||m,S={},O={};function w($){const{children:B,theme:D,modeStorageKey:k=i,colorSchemeStorageKey:z=u,disableTransitionOnChange:N=c,storageManager:q,storageWindow:G=typeof window>"u"?void 0:window,documentNode:K=typeof document>"u"?void 0:document,colorSchemeNode:v=typeof document>"u"?void 0:document.documentElement,disableNestedContext:_=!1,disableStyleSheetGeneration:V=!1,defaultMode:ae="system",forceThemeRerender:ee=!1,noSsr:L}=$,T=M.useRef(!1),H=lp(),Y=M.useContext(h),X=!!Y&&!_,A=M.useMemo(()=>D||(typeof l=="function"?l():l),[D]),U=A[r],J=U||A,{colorSchemes:ne=S,components:fe=O,cssVarPrefix:ue}=J,le=Object.keys(ne).filter(rt=>!!ne[rt]).join(","),ve=M.useMemo(()=>le.split(","),[le]),xe=typeof d=="string"?d:d.light,be=typeof d=="string"?d:d.dark,me=ne[xe]&&ne[be]?ae:ne[J.defaultColorScheme]?.palette?.mode||J.palette?.mode,{mode:Re,setMode:Ne,systemMode:_e,lightColorScheme:ke,darkColorScheme:nt,colorScheme:Ve,setColorScheme:at}=vC({supportedColorSchemes:ve,defaultLightColorScheme:xe,defaultDarkColorScheme:be,modeStorageKey:k,colorSchemeStorageKey:z,defaultMode:me,storageManager:q,storageWindow:G,noSsr:L});let he=Re,st=Ve;X&&(he=Y.mode,st=Y.colorScheme);let Te=st||J.defaultColorScheme;J.vars&&!ee&&(Te=J.defaultColorScheme);const Le=M.useMemo(()=>{const rt=J.generateThemeVars?.()||J.vars,je={...J,components:fe,colorSchemes:ne,cssVarPrefix:ue,vars:rt};if(typeof je.generateSpacing=="function"&&(je.spacing=je.generateSpacing()),Te){const Ye=ne[Te];Ye&&typeof Ye=="object"&&Object.keys(Ye).forEach(et=>{Ye[et]&&typeof Ye[et]=="object"?je[et]={...je[et],...Ye[et]}:je[et]=Ye[et]})}return p?p(je):je},[J,Te,fe,ne,ue]),ye=J.colorSchemeSelector;fa(()=>{if(st&&v&&ye&&ye!=="media"){const rt=ye;let je=ye;if(rt==="class"&&(je=".%s"),rt==="data"&&(je="[data-%s]"),rt?.startsWith("data-")&&!rt.includes("%s")&&(je=`[${rt}="%s"]`),je.startsWith("."))v.classList.remove(...ve.map(Ye=>je.substring(1).replace("%s",Ye))),v.classList.add(je.substring(1).replace("%s",st));else{const Ye=je.replace("%s",st).match(/\[([^\]]+)\]/);if(Ye){const[et,vt]=Ye[1].split("=");vt||ve.forEach(Ae=>{v.removeAttribute(et.replace(st,Ae))}),v.setAttribute(et,vt?vt.replace(/"|'/g,""):"")}else v.setAttribute(je,st)}}},[st,ye,v,ve]),M.useEffect(()=>{let rt;if(N&&T.current&&K){const je=K.createElement("style");je.appendChild(K.createTextNode(bC)),K.head.appendChild(je),window.getComputedStyle(K.body),rt=setTimeout(()=>{K.head.removeChild(je)},1)}return()=>{clearTimeout(rt)}},[st,N,K]),M.useEffect(()=>(T.current=!0,()=>{T.current=!1}),[]);const It=M.useMemo(()=>({allColorSchemes:ve,colorScheme:st,darkColorScheme:nt,lightColorScheme:ke,mode:he,setColorScheme:at,setMode:Ne,systemMode:_e}),[ve,st,nt,ke,he,at,Ne,_e,Le.colorSchemeSelector]);let ut=!0;(V||J.cssVariables===!1||X&&H?.cssVarPrefix===ue)&&(ut=!1);const At=b.jsxs(M.Fragment,{children:[b.jsx(w0,{themeId:U?r:void 0,theme:Le,children:B}),ut&&b.jsx(s0,{styles:Le.generateStyleSheets?.()||[]})]});return X?At:b.jsx(h.Provider,{value:It,children:At})}const R=typeof d=="string"?d:d.light,E=typeof d=="string"?d:d.dark;return{CssVarsProvider:w,useColorScheme:y,getInitColorSchemeScript:$=>mC({colorSchemeStorageKey:u,defaultLightColorScheme:R,defaultDarkColorScheme:E,modeStorageKey:i,...$})}}function xC(n=""){function r(...i){if(!i.length)return"";const u=i[0];return typeof u=="string"&&!u.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${n?`${n}-`:""}${u}${r(...i.slice(1))})`:`, ${u}`}return(i,...u)=>`var(--${n?`${n}-`:""}${i}${r(...u)})`}const Yy=(n,r,l,i=[])=>{let u=n;r.forEach((c,d)=>{d===r.length-1?Array.isArray(u)?u[Number(c)]=l:u&&typeof u=="object"&&(u[c]=l):u&&typeof u=="object"&&(u[c]||(u[c]=i.includes(c)?[]:{}),u=u[c])})},CC=(n,r,l)=>{function i(u,c=[],d=[]){Object.entries(u).forEach(([p,m])=>{(!l||l&&!l([...c,p]))&&m!=null&&(typeof m=="object"&&Object.keys(m).length>0?i(m,[...c,p],Array.isArray(m)?[...d,p]:d):r([...c,p],m,d))})}i(n)},TC=(n,r)=>typeof r=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(i=>n.includes(i))||n[n.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r;function hd(n,r){const{prefix:l,shouldSkipGeneratingVar:i}=r||{},u={},c={},d={};return CC(n,(p,m,h)=>{if((typeof m=="string"||typeof m=="number")&&(!i||!i(p,m))){const y=`--${l?`${l}-`:""}${p.join("-")}`,S=TC(p,m);Object.assign(u,{[y]:S}),Yy(c,p,`var(${y})`,h),Yy(d,p,`var(${y}, ${S})`,h)}},p=>p[0]==="vars"),{css:u,vars:c,varsWithDefaults:d}}function EC(n,r={}){const{getSelector:l=x,disableCssColorScheme:i,colorSchemeSelector:u}=r,{colorSchemes:c={},components:d,defaultColorScheme:p="light",...m}=n,{vars:h,css:y,varsWithDefaults:S}=hd(m,r);let O=S;const w={},{[p]:R,...E}=c;if(Object.entries(E||{}).forEach(([D,k])=>{const{vars:z,css:N,varsWithDefaults:q}=hd(k,r);O=hn(O,q),w[D]={css:N,vars:z}}),R){const{css:D,vars:k,varsWithDefaults:z}=hd(R,r);O=hn(O,z),w[p]={css:D,vars:k}}function x(D,k){let z=u;if(u==="class"&&(z=".%s"),u==="data"&&(z="[data-%s]"),u?.startsWith("data-")&&!u.includes("%s")&&(z=`[${u}="%s"]`),D){if(z==="media")return n.defaultColorScheme===D?":root":{[`@media (prefers-color-scheme: ${c[D]?.palette?.mode||D})`]:{":root":k}};if(z)return n.defaultColorScheme===D?`:root, ${z.replace("%s",String(D))}`:z.replace("%s",String(D))}return":root"}return{vars:O,generateThemeVars:()=>{let D={...h};return Object.entries(w).forEach(([,{vars:k}])=>{D=hn(D,k)}),D},generateStyleSheets:()=>{const D=[],k=n.defaultColorScheme||"light";function z(G,K){Object.keys(K).length&&D.push(typeof G=="string"?{[G]:{...K}}:G)}z(l(void 0,{...y}),y);const{[k]:N,...q}=w;if(N){const{css:G}=N,K=c[k]?.palette?.mode,v=!i&&K?{colorScheme:K,...G}:{...G};z(l(k,{...v}),v)}return Object.entries(q).forEach(([G,{css:K}])=>{const v=c[G]?.palette?.mode,_=!i&&v?{colorScheme:v,...K}:{...K};z(l(G,{..._}),_)}),D}}}function RC(n){return function(l){return n==="media"?`@media (prefers-color-scheme: ${l})`:n?n.startsWith("data-")&&!n.includes("%s")?`[${n}="${l}"] &`:n==="class"?`.${l} &`:n==="data"?`[data-${l}] &`:`${n.replace("%s",l)} &`:"&"}}function Qe(n,r,l=void 0){const i={};for(const u in n){const c=n[u];let d="",p=!0;for(let m=0;m<c.length;m+=1){const h=c[m];h&&(d+=(p===!0?"":" ")+r(h),p=!1,l&&l[h]&&(d+=" "+l[h]))}i[u]=d}return i}function mu(n,r){return M.isValidElement(n)&&r.indexOf(n.type.muiName??n.type?._payload?.value?.muiName)!==-1}const OC=(n,r)=>n.filter(l=>r.includes(l)),hl=(n,r,l)=>{const i=n.keys[0];Array.isArray(r)?r.forEach((u,c)=>{l((d,p)=>{c<=n.keys.length-1&&(c===0?Object.assign(d,p):d[n.up(n.keys[c])]=p)},u)}):r&&typeof r=="object"?(Object.keys(r).length>n.keys.length?n.keys:OC(n.keys,Object.keys(r))).forEach(c=>{if(n.keys.includes(c)){const d=r[c];d!==void 0&&l((p,m)=>{i===c?Object.assign(p,m):p[n.up(c)]=m},d)}}):(typeof r=="number"||typeof r=="string")&&l((u,c)=>{Object.assign(u,c)},r)};function Cu(n){return`--Grid-${n}Spacing`}function qu(n){return`--Grid-parent-${n}Spacing`}const Gy="--Grid-columns",ll="--Grid-parent-columns",MC=({theme:n,ownerState:r})=>{const l={};return hl(n.breakpoints,r.size,(i,u)=>{let c={};u==="grow"&&(c={flexBasis:0,flexGrow:1,maxWidth:"100%"}),u==="auto"&&(c={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"}),typeof u=="number"&&(c={flexGrow:0,flexBasis:"auto",width:`calc(100% * ${u} / var(${ll}) - (var(${ll}) - ${u}) * (var(${qu("column")}) / var(${ll})))`}),i(l,c)}),l},wC=({theme:n,ownerState:r})=>{const l={};return hl(n.breakpoints,r.offset,(i,u)=>{let c={};u==="auto"&&(c={marginLeft:"auto"}),typeof u=="number"&&(c={marginLeft:u===0?"0px":`calc(100% * ${u} / var(${ll}) + var(${qu("column")}) * ${u} / var(${ll}))`}),i(l,c)}),l},AC=({theme:n,ownerState:r})=>{if(!r.container)return{};const l={[Gy]:12};return hl(n.breakpoints,r.columns,(i,u)=>{const c=u??12;i(l,{[Gy]:c,"> *":{[ll]:c}})}),l},zC=({theme:n,ownerState:r})=>{if(!r.container)return{};const l={};return hl(n.breakpoints,r.rowSpacing,(i,u)=>{const c=typeof u=="string"?u:n.spacing?.(u);i(l,{[Cu("row")]:c,"> *":{[qu("row")]:c}})}),l},DC=({theme:n,ownerState:r})=>{if(!r.container)return{};const l={};return hl(n.breakpoints,r.columnSpacing,(i,u)=>{const c=typeof u=="string"?u:n.spacing?.(u);i(l,{[Cu("column")]:c,"> *":{[qu("column")]:c}})}),l},kC=({theme:n,ownerState:r})=>{if(!r.container)return{};const l={};return hl(n.breakpoints,r.direction,(i,u)=>{i(l,{flexDirection:u})}),l},$C=({ownerState:n})=>({minWidth:0,boxSizing:"border-box",...n.container&&{display:"flex",flexWrap:"wrap",...n.wrap&&n.wrap!=="wrap"&&{flexWrap:n.wrap},gap:`var(${Cu("row")}) var(${Cu("column")})`}}),jC=n=>{const r=[];return Object.entries(n).forEach(([l,i])=>{i!==!1&&i!==void 0&&r.push(`grid-${l}-${String(i)}`)}),r},NC=(n,r="xs")=>{function l(i){return i===void 0?!1:typeof i=="string"&&!Number.isNaN(Number(i))||typeof i=="number"&&i>0}if(l(n))return[`spacing-${r}-${String(n)}`];if(typeof n=="object"&&!Array.isArray(n)){const i=[];return Object.entries(n).forEach(([u,c])=>{l(c)&&i.push(`spacing-${u}-${String(c)}`)}),i}return[]},BC=n=>n===void 0?[]:typeof n=="object"?Object.entries(n).map(([r,l])=>`direction-${r}-${l}`):[`direction-xs-${String(n)}`];function _C(n,r){n.item!==void 0&&delete n.item,n.zeroMinWidth!==void 0&&delete n.zeroMinWidth,r.keys.forEach(l=>{n[l]!==void 0&&delete n[l]})}const LC=Lu(),HC=Y2("div",{name:"MuiGrid",slot:"Root"});function PC(n){return G2({props:n,name:"MuiGrid",defaultTheme:LC})}function UC(n={}){const{createStyledComponent:r=HC,useThemeProps:l=PC,useTheme:i=qi,componentName:u="MuiGrid"}=n,c=(h,y)=>{const{container:S,direction:O,spacing:w,wrap:R,size:E}=h,x={root:["root",S&&"container",R!=="wrap"&&`wrap-xs-${String(R)}`,...BC(O),...jC(E),...S?NC(w,y.breakpoints.keys[0]):[]]};return Qe(x,$=>Xe(u,$),{})};function d(h,y,S=()=>!0){const O={};return h===null||(Array.isArray(h)?h.forEach((w,R)=>{w!==null&&S(w)&&y.keys[R]&&(O[y.keys[R]]=w)}):typeof h=="object"?Object.keys(h).forEach(w=>{const R=h[w];R!=null&&S(R)&&(O[w]=R)}):O[y.keys[0]]=h),O}const p=r(AC,DC,zC,MC,kC,$C,wC),m=M.forwardRef(function(y,S){const O=i(),w=l(y),R=rp(w);_C(R,O.breakpoints);const{className:E,children:x,columns:$=12,container:B=!1,component:D="div",direction:k="row",wrap:z="wrap",size:N={},offset:q={},spacing:G=0,rowSpacing:K=G,columnSpacing:v=G,unstable_level:_=0,...V}=R,ae=d(N,O.breakpoints,U=>U!==!1),ee=d(q,O.breakpoints),L=y.columns??(_?void 0:$),T=y.spacing??(_?void 0:G),H=y.rowSpacing??y.spacing??(_?void 0:K),Y=y.columnSpacing??y.spacing??(_?void 0:v),X={...R,level:_,columns:L,container:B,direction:k,wrap:z,spacing:T,rowSpacing:H,columnSpacing:Y,size:ae,offset:ee},A=c(X,O);return b.jsx(p,{ref:S,as:D,ownerState:X,className:Me(A.root,E),...V,children:M.Children.map(x,U=>M.isValidElement(U)&&mu(U,["Grid"])&&B&&U.props.container?M.cloneElement(U,{unstable_level:U.props?.unstable_level??_+1}):U)})});return m.muiName="Grid",m}function z0(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:wi.white,default:wi.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const IC=z0();function D0(){return{text:{primary:wi.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:wi.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const Xy=D0();function Ky(n,r,l,i){const u=i.light||i,c=i.dark||i*1.5;n[r]||(n.hasOwnProperty(l)?n[r]=n[l]:r==="light"?n.light=Iu(n.main,u):r==="dark"&&(n.dark=Uu(n.main,c)))}function qC(n="light"){return n==="dark"?{main:Zo[200],light:Zo[50],dark:Zo[400]}:{main:Zo[700],light:Zo[400],dark:Zo[800]}}function VC(n="light"){return n==="dark"?{main:Fo[200],light:Fo[50],dark:Fo[400]}:{main:Fo[500],light:Fo[300],dark:Fo[700]}}function YC(n="light"){return n==="dark"?{main:Wo[500],light:Wo[300],dark:Wo[700]}:{main:Wo[700],light:Wo[400],dark:Wo[800]}}function GC(n="light"){return n==="dark"?{main:Jo[400],light:Jo[300],dark:Jo[700]}:{main:Jo[700],light:Jo[500],dark:Jo[900]}}function XC(n="light"){return n==="dark"?{main:el[400],light:el[300],dark:el[700]}:{main:el[800],light:el[500],dark:el[900]}}function KC(n="light"){return n==="dark"?{main:mi[400],light:mi[300],dark:mi[700]}:{main:"#ed6c02",light:mi[500],dark:mi[900]}}function up(n){const{mode:r="light",contrastThreshold:l=3,tonalOffset:i=.2,...u}=n,c=n.primary||qC(r),d=n.secondary||VC(r),p=n.error||YC(r),m=n.info||GC(r),h=n.success||XC(r),y=n.warning||KC(r);function S(E){return J2(E,Xy.text.primary)>=l?Xy.text.primary:IC.text.primary}const O=({color:E,name:x,mainShade:$=500,lightShade:B=300,darkShade:D=700})=>{if(E={...E},!E.main&&E[$]&&(E.main=E[$]),!E.hasOwnProperty("main"))throw new Error(or(11,x?` (${x})`:"",$));if(typeof E.main!="string")throw new Error(or(12,x?` (${x})`:"",JSON.stringify(E.main)));return Ky(E,"light",B,i),Ky(E,"dark",D,i),E.contrastText||(E.contrastText=S(E.main)),E};let w;return r==="light"?w=z0():r==="dark"&&(w=D0()),hn({common:{...wi},mode:r,primary:O({color:c,name:"primary"}),secondary:O({color:d,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:O({color:p,name:"error"}),warning:O({color:y,name:"warning"}),info:O({color:m,name:"info"}),success:O({color:h,name:"success"}),grey:BS,contrastThreshold:l,getContrastText:S,augmentColor:O,tonalOffset:i,...w},u)}function QC(n){const r={};return Object.entries(n).forEach(i=>{const[u,c]=i;typeof c=="object"&&(r[u]=`${c.fontStyle?`${c.fontStyle} `:""}${c.fontVariant?`${c.fontVariant} `:""}${c.fontWeight?`${c.fontWeight} `:""}${c.fontStretch?`${c.fontStretch} `:""}${c.fontSize||""}${c.lineHeight?`/${c.lineHeight} `:""}${c.fontFamily||""}`)}),r}function WC(n,r){return{toolbar:{minHeight:56,[n.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[n.up("sm")]:{minHeight:64}},...r}}function FC(n){return Math.round(n*1e5)/1e5}const Qy={textTransform:"uppercase"},Wy='"Roboto", "Helvetica", "Arial", sans-serif';function k0(n,r){const{fontFamily:l=Wy,fontSize:i=14,fontWeightLight:u=300,fontWeightRegular:c=400,fontWeightMedium:d=500,fontWeightBold:p=700,htmlFontSize:m=16,allVariants:h,pxToRem:y,...S}=typeof r=="function"?r(n):r,O=i/14,w=y||(x=>`${x/m*O}rem`),R=(x,$,B,D,k)=>({fontFamily:l,fontWeight:x,fontSize:w($),lineHeight:B,...l===Wy?{letterSpacing:`${FC(D/$)}em`}:{},...k,...h}),E={h1:R(u,96,1.167,-1.5),h2:R(u,60,1.2,-.5),h3:R(c,48,1.167,0),h4:R(c,34,1.235,.25),h5:R(c,24,1.334,0),h6:R(d,20,1.6,.15),subtitle1:R(c,16,1.75,.15),subtitle2:R(d,14,1.57,.1),body1:R(c,16,1.5,.15),body2:R(c,14,1.43,.15),button:R(d,14,1.75,.4,Qy),caption:R(c,12,1.66,.4),overline:R(c,12,2.66,1,Qy),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return hn({htmlFontSize:m,pxToRem:w,fontFamily:l,fontSize:i,fontWeightLight:u,fontWeightRegular:c,fontWeightMedium:d,fontWeightBold:p,...E},S,{clone:!1})}const ZC=.2,JC=.14,eT=.12;function $t(...n){return[`${n[0]}px ${n[1]}px ${n[2]}px ${n[3]}px rgba(0,0,0,${ZC})`,`${n[4]}px ${n[5]}px ${n[6]}px ${n[7]}px rgba(0,0,0,${JC})`,`${n[8]}px ${n[9]}px ${n[10]}px ${n[11]}px rgba(0,0,0,${eT})`].join(",")}const tT=["none",$t(0,2,1,-1,0,1,1,0,0,1,3,0),$t(0,3,1,-2,0,2,2,0,0,1,5,0),$t(0,3,3,-2,0,3,4,0,0,1,8,0),$t(0,2,4,-1,0,4,5,0,0,1,10,0),$t(0,3,5,-1,0,5,8,0,0,1,14,0),$t(0,3,5,-1,0,6,10,0,0,1,18,0),$t(0,4,5,-2,0,7,10,1,0,2,16,1),$t(0,5,5,-3,0,8,10,1,0,3,14,2),$t(0,5,6,-3,0,9,12,1,0,3,16,2),$t(0,6,6,-3,0,10,14,1,0,4,18,3),$t(0,6,7,-4,0,11,15,1,0,4,20,3),$t(0,7,8,-4,0,12,17,2,0,5,22,4),$t(0,7,8,-4,0,13,19,2,0,5,24,4),$t(0,7,9,-4,0,14,21,2,0,5,26,4),$t(0,8,9,-5,0,15,22,2,0,6,28,5),$t(0,8,10,-5,0,16,24,2,0,6,30,5),$t(0,8,11,-5,0,17,26,2,0,6,32,5),$t(0,9,11,-5,0,18,28,2,0,7,34,6),$t(0,9,12,-6,0,19,29,2,0,7,36,6),$t(0,10,13,-6,0,20,31,3,0,8,38,7),$t(0,10,13,-6,0,21,33,3,0,8,40,7),$t(0,10,14,-6,0,22,35,3,0,8,42,7),$t(0,11,14,-7,0,23,36,3,0,9,44,8),$t(0,11,15,-7,0,24,38,3,0,9,46,8)],nT={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},aT={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Fy(n){return`${Math.round(n)}ms`}function rT(n){if(!n)return 0;const r=n/36;return Math.min(Math.round((4+15*r**.25+r/5)*10),3e3)}function oT(n){const r={...nT,...n.easing},l={...aT,...n.duration};return{getAutoHeightDuration:rT,create:(u=["all"],c={})=>{const{duration:d=l.standard,easing:p=r.easeInOut,delay:m=0,...h}=c;return(Array.isArray(u)?u:[u]).map(y=>`${y} ${typeof d=="string"?d:Fy(d)} ${p} ${typeof m=="string"?m:Fy(m)}`).join(",")},...n,easing:r,duration:l}}const lT={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function iT(n){return Na(n)||typeof n>"u"||typeof n=="string"||typeof n=="boolean"||typeof n=="number"||Array.isArray(n)}function $0(n={}){const r={...n};function l(i){const u=Object.entries(i);for(let c=0;c<u.length;c++){const[d,p]=u[c];!iT(p)||d.startsWith("unstable_")?delete i[d]:Na(p)&&(i[d]={...p},l(i[d]))}}return l(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(r,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Od(n={},...r){const{breakpoints:l,mixins:i={},spacing:u,palette:c={},transitions:d={},typography:p={},shape:m,...h}=n;if(n.vars&&n.generateThemeVars===void 0)throw new Error(or(20));const y=up(c),S=Lu(n);let O=hn(S,{mixins:WC(S.breakpoints,i),palette:y,shadows:tT.slice(),typography:k0(y,p),transitions:oT(d),zIndex:{...lT}});return O=hn(O,h),O=r.reduce((w,R)=>hn(w,R),O),O.unstable_sxConfig={...Ii,...h?.unstable_sxConfig},O.unstable_sx=function(R){return Br({sx:R,theme:this})},O.toRuntimeSource=$0,O}function Md(n){let r;return n<1?r=5.11916*n**2:r=4.5*Math.log(n+1)+2,Math.round(r*10)/1e3}const sT=[...Array(25)].map((n,r)=>{if(r===0)return"none";const l=Md(r);return`linear-gradient(rgba(255 255 255 / ${l}), rgba(255 255 255 / ${l}))`});function j0(n){return{inputPlaceholder:n==="dark"?.5:.42,inputUnderline:n==="dark"?.7:.42,switchTrackDisabled:n==="dark"?.2:.12,switchTrack:n==="dark"?.3:.38}}function N0(n){return n==="dark"?sT:[]}function uT(n){const{palette:r={mode:"light"},opacity:l,overlays:i,...u}=n,c=up(r);return{palette:c,opacity:{...j0(c.mode),...l},overlays:i||N0(c.mode),...u}}function cT(n){return!!n[0].match(/(cssVarPrefix|colorSchemeSelector|modularCssLayers|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!n[0].match(/sxConfig$/)||n[0]==="palette"&&!!n[1]?.match(/(mode|contrastThreshold|tonalOffset)/)}const fT=n=>[...[...Array(25)].map((r,l)=>`--${n?`${n}-`:""}overlays-${l}`),`--${n?`${n}-`:""}palette-AppBar-darkBg`,`--${n?`${n}-`:""}palette-AppBar-darkColor`],dT=n=>(r,l)=>{const i=n.rootSelector||":root",u=n.colorSchemeSelector;let c=u;if(u==="class"&&(c=".%s"),u==="data"&&(c="[data-%s]"),u?.startsWith("data-")&&!u.includes("%s")&&(c=`[${u}="%s"]`),n.defaultColorScheme===r){if(r==="dark"){const d={};return fT(n.cssVarPrefix).forEach(p=>{d[p]=l[p],delete l[p]}),c==="media"?{[i]:l,"@media (prefers-color-scheme: dark)":{[i]:d}}:c?{[c.replace("%s",r)]:d,[`${i}, ${c.replace("%s",r)}`]:l}:{[i]:{...l,...d}}}if(c&&c!=="media")return`${i}, ${c.replace("%s",String(r))}`}else if(r){if(c==="media")return{[`@media (prefers-color-scheme: ${String(r)})`]:{[i]:l}};if(c)return c.replace("%s",String(r))}return i};function pT(n,r){r.forEach(l=>{n[l]||(n[l]={})})}function oe(n,r,l){!n[r]&&l&&(n[r]=l)}function Si(n){return typeof n!="string"||!n.startsWith("hsl")?n:T0(n)}function ar(n,r){`${r}Channel`in n||(n[`${r}Channel`]=bi(Si(n[r])))}function mT(n){return typeof n=="number"?`${n}px`:typeof n=="string"||typeof n=="function"||Array.isArray(n)?n:"8px"}const za=n=>{try{return n()}catch{}},hT=(n="mui")=>xC(n);function gd(n,r,l,i){if(!r)return;r=r===!0?{}:r;const u=i==="dark"?"dark":"light";if(!l){n[i]=uT({...r,palette:{mode:u,...r?.palette}});return}const{palette:c,...d}=Od({...l,palette:{mode:u,...r?.palette}});return n[i]={...r,palette:c,opacity:{...j0(u),...r?.opacity},overlays:r?.overlays||N0(u)},d}function gT(n={},...r){const{colorSchemes:l={light:!0},defaultColorScheme:i,disableCssColorScheme:u=!1,cssVarPrefix:c="mui",shouldSkipGeneratingVar:d=cT,colorSchemeSelector:p=l.light&&l.dark?"media":void 0,rootSelector:m=":root",...h}=n,y=Object.keys(l)[0],S=i||(l.light&&y!=="light"?"light":y),O=hT(c),{[S]:w,light:R,dark:E,...x}=l,$={...x};let B=w;if((S==="dark"&&!("dark"in l)||S==="light"&&!("light"in l))&&(B=!0),!B)throw new Error(or(21,S));const D=gd($,B,h,S);R&&!$.light&&gd($,R,void 0,"light"),E&&!$.dark&&gd($,E,void 0,"dark");let k={defaultColorScheme:S,...D,cssVarPrefix:c,colorSchemeSelector:p,rootSelector:m,getCssVar:O,colorSchemes:$,font:{...QC(D.typography),...D.font},spacing:mT(h.spacing)};Object.keys(k.colorSchemes).forEach(K=>{const v=k.colorSchemes[K].palette,_=V=>{const ae=V.split("-"),ee=ae[1],L=ae[2];return O(V,v[ee][L])};if(v.mode==="light"&&(oe(v.common,"background","#fff"),oe(v.common,"onBackground","#000")),v.mode==="dark"&&(oe(v.common,"background","#000"),oe(v.common,"onBackground","#fff")),pT(v,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),v.mode==="light"){oe(v.Alert,"errorColor",Et(v.error.light,.6)),oe(v.Alert,"infoColor",Et(v.info.light,.6)),oe(v.Alert,"successColor",Et(v.success.light,.6)),oe(v.Alert,"warningColor",Et(v.warning.light,.6)),oe(v.Alert,"errorFilledBg",_("palette-error-main")),oe(v.Alert,"infoFilledBg",_("palette-info-main")),oe(v.Alert,"successFilledBg",_("palette-success-main")),oe(v.Alert,"warningFilledBg",_("palette-warning-main")),oe(v.Alert,"errorFilledColor",za(()=>v.getContrastText(v.error.main))),oe(v.Alert,"infoFilledColor",za(()=>v.getContrastText(v.info.main))),oe(v.Alert,"successFilledColor",za(()=>v.getContrastText(v.success.main))),oe(v.Alert,"warningFilledColor",za(()=>v.getContrastText(v.warning.main))),oe(v.Alert,"errorStandardBg",Rt(v.error.light,.9)),oe(v.Alert,"infoStandardBg",Rt(v.info.light,.9)),oe(v.Alert,"successStandardBg",Rt(v.success.light,.9)),oe(v.Alert,"warningStandardBg",Rt(v.warning.light,.9)),oe(v.Alert,"errorIconColor",_("palette-error-main")),oe(v.Alert,"infoIconColor",_("palette-info-main")),oe(v.Alert,"successIconColor",_("palette-success-main")),oe(v.Alert,"warningIconColor",_("palette-warning-main")),oe(v.AppBar,"defaultBg",_("palette-grey-100")),oe(v.Avatar,"defaultBg",_("palette-grey-400")),oe(v.Button,"inheritContainedBg",_("palette-grey-300")),oe(v.Button,"inheritContainedHoverBg",_("palette-grey-A100")),oe(v.Chip,"defaultBorder",_("palette-grey-400")),oe(v.Chip,"defaultAvatarColor",_("palette-grey-700")),oe(v.Chip,"defaultIconColor",_("palette-grey-700")),oe(v.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),oe(v.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),oe(v.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),oe(v.LinearProgress,"primaryBg",Rt(v.primary.main,.62)),oe(v.LinearProgress,"secondaryBg",Rt(v.secondary.main,.62)),oe(v.LinearProgress,"errorBg",Rt(v.error.main,.62)),oe(v.LinearProgress,"infoBg",Rt(v.info.main,.62)),oe(v.LinearProgress,"successBg",Rt(v.success.main,.62)),oe(v.LinearProgress,"warningBg",Rt(v.warning.main,.62)),oe(v.Skeleton,"bg",`rgba(${_("palette-text-primaryChannel")} / 0.11)`),oe(v.Slider,"primaryTrack",Rt(v.primary.main,.62)),oe(v.Slider,"secondaryTrack",Rt(v.secondary.main,.62)),oe(v.Slider,"errorTrack",Rt(v.error.main,.62)),oe(v.Slider,"infoTrack",Rt(v.info.main,.62)),oe(v.Slider,"successTrack",Rt(v.success.main,.62)),oe(v.Slider,"warningTrack",Rt(v.warning.main,.62));const V=au(v.background.default,.8);oe(v.SnackbarContent,"bg",V),oe(v.SnackbarContent,"color",za(()=>v.getContrastText(V))),oe(v.SpeedDialAction,"fabHoverBg",au(v.background.paper,.15)),oe(v.StepConnector,"border",_("palette-grey-400")),oe(v.StepContent,"border",_("palette-grey-400")),oe(v.Switch,"defaultColor",_("palette-common-white")),oe(v.Switch,"defaultDisabledColor",_("palette-grey-100")),oe(v.Switch,"primaryDisabledColor",Rt(v.primary.main,.62)),oe(v.Switch,"secondaryDisabledColor",Rt(v.secondary.main,.62)),oe(v.Switch,"errorDisabledColor",Rt(v.error.main,.62)),oe(v.Switch,"infoDisabledColor",Rt(v.info.main,.62)),oe(v.Switch,"successDisabledColor",Rt(v.success.main,.62)),oe(v.Switch,"warningDisabledColor",Rt(v.warning.main,.62)),oe(v.TableCell,"border",Rt(nu(v.divider,1),.88)),oe(v.Tooltip,"bg",nu(v.grey[700],.92))}if(v.mode==="dark"){oe(v.Alert,"errorColor",Rt(v.error.light,.6)),oe(v.Alert,"infoColor",Rt(v.info.light,.6)),oe(v.Alert,"successColor",Rt(v.success.light,.6)),oe(v.Alert,"warningColor",Rt(v.warning.light,.6)),oe(v.Alert,"errorFilledBg",_("palette-error-dark")),oe(v.Alert,"infoFilledBg",_("palette-info-dark")),oe(v.Alert,"successFilledBg",_("palette-success-dark")),oe(v.Alert,"warningFilledBg",_("palette-warning-dark")),oe(v.Alert,"errorFilledColor",za(()=>v.getContrastText(v.error.dark))),oe(v.Alert,"infoFilledColor",za(()=>v.getContrastText(v.info.dark))),oe(v.Alert,"successFilledColor",za(()=>v.getContrastText(v.success.dark))),oe(v.Alert,"warningFilledColor",za(()=>v.getContrastText(v.warning.dark))),oe(v.Alert,"errorStandardBg",Et(v.error.light,.9)),oe(v.Alert,"infoStandardBg",Et(v.info.light,.9)),oe(v.Alert,"successStandardBg",Et(v.success.light,.9)),oe(v.Alert,"warningStandardBg",Et(v.warning.light,.9)),oe(v.Alert,"errorIconColor",_("palette-error-main")),oe(v.Alert,"infoIconColor",_("palette-info-main")),oe(v.Alert,"successIconColor",_("palette-success-main")),oe(v.Alert,"warningIconColor",_("palette-warning-main")),oe(v.AppBar,"defaultBg",_("palette-grey-900")),oe(v.AppBar,"darkBg",_("palette-background-paper")),oe(v.AppBar,"darkColor",_("palette-text-primary")),oe(v.Avatar,"defaultBg",_("palette-grey-600")),oe(v.Button,"inheritContainedBg",_("palette-grey-800")),oe(v.Button,"inheritContainedHoverBg",_("palette-grey-700")),oe(v.Chip,"defaultBorder",_("palette-grey-700")),oe(v.Chip,"defaultAvatarColor",_("palette-grey-300")),oe(v.Chip,"defaultIconColor",_("palette-grey-300")),oe(v.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),oe(v.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),oe(v.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),oe(v.LinearProgress,"primaryBg",Et(v.primary.main,.5)),oe(v.LinearProgress,"secondaryBg",Et(v.secondary.main,.5)),oe(v.LinearProgress,"errorBg",Et(v.error.main,.5)),oe(v.LinearProgress,"infoBg",Et(v.info.main,.5)),oe(v.LinearProgress,"successBg",Et(v.success.main,.5)),oe(v.LinearProgress,"warningBg",Et(v.warning.main,.5)),oe(v.Skeleton,"bg",`rgba(${_("palette-text-primaryChannel")} / 0.13)`),oe(v.Slider,"primaryTrack",Et(v.primary.main,.5)),oe(v.Slider,"secondaryTrack",Et(v.secondary.main,.5)),oe(v.Slider,"errorTrack",Et(v.error.main,.5)),oe(v.Slider,"infoTrack",Et(v.info.main,.5)),oe(v.Slider,"successTrack",Et(v.success.main,.5)),oe(v.Slider,"warningTrack",Et(v.warning.main,.5));const V=au(v.background.default,.98);oe(v.SnackbarContent,"bg",V),oe(v.SnackbarContent,"color",za(()=>v.getContrastText(V))),oe(v.SpeedDialAction,"fabHoverBg",au(v.background.paper,.15)),oe(v.StepConnector,"border",_("palette-grey-600")),oe(v.StepContent,"border",_("palette-grey-600")),oe(v.Switch,"defaultColor",_("palette-grey-300")),oe(v.Switch,"defaultDisabledColor",_("palette-grey-600")),oe(v.Switch,"primaryDisabledColor",Et(v.primary.main,.55)),oe(v.Switch,"secondaryDisabledColor",Et(v.secondary.main,.55)),oe(v.Switch,"errorDisabledColor",Et(v.error.main,.55)),oe(v.Switch,"infoDisabledColor",Et(v.info.main,.55)),oe(v.Switch,"successDisabledColor",Et(v.success.main,.55)),oe(v.Switch,"warningDisabledColor",Et(v.warning.main,.55)),oe(v.TableCell,"border",Et(nu(v.divider,1),.68)),oe(v.Tooltip,"bg",nu(v.grey[700],.92))}ar(v.background,"default"),ar(v.background,"paper"),ar(v.common,"background"),ar(v.common,"onBackground"),ar(v,"divider"),Object.keys(v).forEach(V=>{const ae=v[V];V!=="tonalOffset"&&ae&&typeof ae=="object"&&(ae.main&&oe(v[V],"mainChannel",bi(Si(ae.main))),ae.light&&oe(v[V],"lightChannel",bi(Si(ae.light))),ae.dark&&oe(v[V],"darkChannel",bi(Si(ae.dark))),ae.contrastText&&oe(v[V],"contrastTextChannel",bi(Si(ae.contrastText))),V==="text"&&(ar(v[V],"primary"),ar(v[V],"secondary")),V==="action"&&(ae.active&&ar(v[V],"active"),ae.selected&&ar(v[V],"selected")))})}),k=r.reduce((K,v)=>hn(K,v),k);const z={prefix:c,disableCssColorScheme:u,shouldSkipGeneratingVar:d,getSelector:dT(k)},{vars:N,generateThemeVars:q,generateStyleSheets:G}=EC(k,z);return k.vars=N,Object.entries(k.colorSchemes[k.defaultColorScheme]).forEach(([K,v])=>{k[K]=v}),k.generateThemeVars=q,k.generateStyleSheets=G,k.generateSpacing=function(){return p0(h.spacing,np(this))},k.getColorSchemeSelector=RC(p),k.spacing=k.generateSpacing(),k.shouldSkipGeneratingVar=d,k.unstable_sxConfig={...Ii,...h?.unstable_sxConfig},k.unstable_sx=function(v){return Br({sx:v,theme:this})},k.toRuntimeSource=$0,k}function Zy(n,r,l){n.colorSchemes&&l&&(n.colorSchemes[r]={...l!==!0&&l,palette:up({...l===!0?{}:l.palette,mode:r})})}function Vu(n={},...r){const{palette:l,cssVariables:i=!1,colorSchemes:u=l?void 0:{light:!0},defaultColorScheme:c=l?.mode,...d}=n,p=c||"light",m=u?.[p],h={...u,...l?{[p]:{...typeof m!="boolean"&&m,palette:l}}:void 0};if(i===!1){if(!("colorSchemes"in n))return Od(n,...r);let y=l;"palette"in n||h[p]&&(h[p]!==!0?y=h[p].palette:p==="dark"&&(y={mode:"dark"}));const S=Od({...n,palette:y},...r);return S.defaultColorScheme=p,S.colorSchemes=h,S.palette.mode==="light"&&(S.colorSchemes.light={...h.light!==!0&&h.light,palette:S.palette},Zy(S,"dark",h.dark)),S.palette.mode==="dark"&&(S.colorSchemes.dark={...h.dark!==!0&&h.dark,palette:S.palette},Zy(S,"light",h.light)),S}return!l&&!("light"in h)&&p==="light"&&(h.light=!0),gT({...d,colorSchemes:h,defaultColorScheme:p,...typeof i!="boolean"&&i},...r)}const cp=Vu();function Yu(){const n=qi(cp);return n[Ta]||n}function B0(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}const Yn=n=>B0(n)&&n!=="classes",pe=b0({themeId:Ta,defaultTheme:cp,rootShouldForwardProp:Yn});function yT({theme:n,...r}){const l=Ta in n?n[Ta]:void 0;return b.jsx(w0,{...r,themeId:l?Ta:void 0,theme:l||n})}const ru={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:vT}=SC({themeId:Ta,theme:()=>Vu({cssVariables:!0}),colorSchemeStorageKey:ru.colorSchemeStorageKey,modeStorageKey:ru.modeStorageKey,defaultColorScheme:{light:ru.defaultLightColorScheme,dark:ru.defaultDarkColorScheme},resolveTheme:n=>{const r={...n,typography:k0(n.palette,n.typography)};return r.unstable_sx=function(i){return Br({sx:i,theme:this})},r}}),bT=vT;function ST({theme:n,...r}){const l=M.useMemo(()=>{if(typeof n=="function")return n;const i=Ta in n?n[Ta]:n;return"colorSchemes"in i?null:"vars"in i?n:{...n,vars:null}},[n]);return l?b.jsx(yT,{theme:l,...r}):b.jsx(bT,{theme:n,...r})}function Jy(...n){return n.reduce((r,l)=>l==null?r:function(...u){r.apply(this,u),l.apply(this,u)},()=>{})}function xT(n){return b.jsx(m0,{...n,defaultTheme:cp,themeId:Ta})}function fp(n){return function(l){return b.jsx(xT,{styles:typeof n=="function"?i=>n({theme:i,...l}):n})}}function CT(){return rp}const tt=dC;function Je(n){return sC(n)}function TT(n){return Xe("MuiSvgIcon",n)}Ke("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const ET=n=>{const{color:r,fontSize:l,classes:i}=n,u={root:["root",r!=="inherit"&&`color${de(r)}`,`fontSize${de(l)}`]};return Qe(u,TT,i)},RT=pe("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.color!=="inherit"&&r[`color${de(l.color)}`],r[`fontSize${de(l.fontSize)}`]]}})(tt(({theme:n})=>({userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:n.transitions?.create?.("fill",{duration:(n.vars??n).transitions?.duration?.shorter}),variants:[{props:r=>!r.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:n.typography?.pxToRem?.(20)||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:n.typography?.pxToRem?.(24)||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:n.typography?.pxToRem?.(35)||"2.1875rem"}},...Object.entries((n.vars??n).palette).filter(([,r])=>r&&r.main).map(([r])=>({props:{color:r},style:{color:(n.vars??n).palette?.[r]?.main}})),{props:{color:"action"},style:{color:(n.vars??n).palette?.action?.active}},{props:{color:"disabled"},style:{color:(n.vars??n).palette?.action?.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}))),wd=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiSvgIcon"}),{children:u,className:c,color:d="inherit",component:p="svg",fontSize:m="medium",htmlColor:h,inheritViewBox:y=!1,titleAccess:S,viewBox:O="0 0 24 24",...w}=i,R=M.isValidElement(u)&&u.type==="svg",E={...i,color:d,component:p,fontSize:m,instanceFontSize:r.fontSize,inheritViewBox:y,viewBox:O,hasSvgAsChild:R},x={};y||(x.viewBox=O);const $=ET(E);return b.jsxs(RT,{as:p,className:Me($.root,c),focusable:"false",color:h,"aria-hidden":S?void 0:!0,role:S?"img":void 0,ref:l,...x,...w,...R&&u.props,ownerState:E,children:[R?u.props.children:u,S?b.jsx("title",{children:S}):null]})});wd.muiName="SvgIcon";function An(n,r){function l(i,u){return b.jsx(wd,{"data-testid":void 0,ref:u,...i,children:n})}return l.muiName=wd.muiName,M.memo(M.forwardRef(l))}function _0(n,r=166){let l;function i(...u){const c=()=>{n.apply(this,u)};clearTimeout(l),l=setTimeout(c,r)}return i.clear=()=>{clearTimeout(l)},i}function qn(n){return n&&n.ownerDocument||document}function ir(n){return qn(n).defaultView||window}function Ad(n,r){typeof n=="function"?n(r):n&&(n.current=r)}function il(n){const{controlled:r,default:l,name:i,state:u="value"}=n,{current:c}=M.useRef(r!==void 0),[d,p]=M.useState(l),m=c?r:d,h=M.useCallback(y=>{c||p(y)},[]);return[m,h]}function Ca(n){const r=M.useRef(n);return fa(()=>{r.current=n}),M.useRef((...l)=>(0,r.current)(...l)).current}function fn(...n){const r=M.useRef(void 0),l=M.useCallback(i=>{const u=n.map(c=>{if(c==null)return null;if(typeof c=="function"){const d=c,p=d(i);return typeof p=="function"?p:()=>{d(null)}}return c.current=i,()=>{c.current=null}});return()=>{u.forEach(c=>c?.())}},n);return M.useMemo(()=>n.every(i=>i==null)?null:i=>{r.current&&(r.current(),r.current=void 0),i!=null&&(r.current=l(i))},n)}function OT(n,r){const l=n.charCodeAt(2);return n[0]==="o"&&n[1]==="n"&&l>=65&&l<=90&&typeof r=="function"}function L0(n,r){if(!n)return r;function l(d,p){const m={};return Object.keys(p).forEach(h=>{OT(h,p[h])&&typeof d[h]=="function"&&(m[h]=(...y)=>{d[h](...y),p[h](...y)})}),m}if(typeof n=="function"||typeof r=="function")return d=>{const p=typeof r=="function"?r(d):r,m=typeof n=="function"?n({...d,...p}):n,h=Me(d?.className,p?.className,m?.className),y=l(m,p);return{...p,...m,...y,...!!h&&{className:h},...p?.style&&m?.style&&{style:{...p.style,...m.style}},...p?.sx&&m?.sx&&{sx:[...Array.isArray(p.sx)?p.sx:[p.sx],...Array.isArray(m.sx)?m.sx:[m.sx]]}}};const i=r,u=l(n,i),c=Me(i?.className,n?.className);return{...r,...n,...u,...!!c&&{className:c},...i?.style&&n?.style&&{style:{...i.style,...n.style}},...i?.sx&&n?.sx&&{sx:[...Array.isArray(i.sx)?i.sx:[i.sx],...Array.isArray(n.sx)?n.sx:[n.sx]]}}}function H0(n,r){if(n==null)return{};var l={};for(var i in n)if({}.hasOwnProperty.call(n,i)){if(r.indexOf(i)!==-1)continue;l[i]=n[i]}return l}function zd(n,r){return zd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(l,i){return l.__proto__=i,l},zd(n,r)}function P0(n,r){n.prototype=Object.create(r.prototype),n.prototype.constructor=n,zd(n,r)}var U0=Kv();const ou=Vd(U0),ev={disabled:!1},Tu=Ba.createContext(null);var MT=function(r){return r.scrollTop},xi="unmounted",oo="exited",lo="entering",al="entered",Dd="exiting",Pa=function(n){P0(r,n);function r(i,u){var c;c=n.call(this,i,u)||this;var d=u,p=d&&!d.isMounting?i.enter:i.appear,m;return c.appearStatus=null,i.in?p?(m=oo,c.appearStatus=lo):m=al:i.unmountOnExit||i.mountOnEnter?m=xi:m=oo,c.state={status:m},c.nextCallback=null,c}r.getDerivedStateFromProps=function(u,c){var d=u.in;return d&&c.status===xi?{status:oo}:null};var l=r.prototype;return l.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},l.componentDidUpdate=function(u){var c=null;if(u!==this.props){var d=this.state.status;this.props.in?d!==lo&&d!==al&&(c=lo):(d===lo||d===al)&&(c=Dd)}this.updateStatus(!1,c)},l.componentWillUnmount=function(){this.cancelNextCallback()},l.getTimeouts=function(){var u=this.props.timeout,c,d,p;return c=d=p=u,u!=null&&typeof u!="number"&&(c=u.exit,d=u.enter,p=u.appear!==void 0?u.appear:d),{exit:c,enter:d,appear:p}},l.updateStatus=function(u,c){if(u===void 0&&(u=!1),c!==null)if(this.cancelNextCallback(),c===lo){if(this.props.unmountOnExit||this.props.mountOnEnter){var d=this.props.nodeRef?this.props.nodeRef.current:ou.findDOMNode(this);d&&MT(d)}this.performEnter(u)}else this.performExit();else this.props.unmountOnExit&&this.state.status===oo&&this.setState({status:xi})},l.performEnter=function(u){var c=this,d=this.props.enter,p=this.context?this.context.isMounting:u,m=this.props.nodeRef?[p]:[ou.findDOMNode(this),p],h=m[0],y=m[1],S=this.getTimeouts(),O=p?S.appear:S.enter;if(!u&&!d||ev.disabled){this.safeSetState({status:al},function(){c.props.onEntered(h)});return}this.props.onEnter(h,y),this.safeSetState({status:lo},function(){c.props.onEntering(h,y),c.onTransitionEnd(O,function(){c.safeSetState({status:al},function(){c.props.onEntered(h,y)})})})},l.performExit=function(){var u=this,c=this.props.exit,d=this.getTimeouts(),p=this.props.nodeRef?void 0:ou.findDOMNode(this);if(!c||ev.disabled){this.safeSetState({status:oo},function(){u.props.onExited(p)});return}this.props.onExit(p),this.safeSetState({status:Dd},function(){u.props.onExiting(p),u.onTransitionEnd(d.exit,function(){u.safeSetState({status:oo},function(){u.props.onExited(p)})})})},l.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},l.safeSetState=function(u,c){c=this.setNextCallback(c),this.setState(u,c)},l.setNextCallback=function(u){var c=this,d=!0;return this.nextCallback=function(p){d&&(d=!1,c.nextCallback=null,u(p))},this.nextCallback.cancel=function(){d=!1},this.nextCallback},l.onTransitionEnd=function(u,c){this.setNextCallback(c);var d=this.props.nodeRef?this.props.nodeRef.current:ou.findDOMNode(this),p=u==null&&!this.props.addEndListener;if(!d||p){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var m=this.props.nodeRef?[this.nextCallback]:[d,this.nextCallback],h=m[0],y=m[1];this.props.addEndListener(h,y)}u!=null&&setTimeout(this.nextCallback,u)},l.render=function(){var u=this.state.status;if(u===xi)return null;var c=this.props,d=c.children;c.in,c.mountOnEnter,c.unmountOnExit,c.appear,c.enter,c.exit,c.timeout,c.addEndListener,c.onEnter,c.onEntering,c.onEntered,c.onExit,c.onExiting,c.onExited,c.nodeRef;var p=H0(c,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return Ba.createElement(Tu.Provider,{value:null},typeof d=="function"?d(u,p):Ba.cloneElement(Ba.Children.only(d),p))},r}(Ba.Component);Pa.contextType=Tu;Pa.propTypes={};function tl(){}Pa.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:tl,onEntering:tl,onEntered:tl,onExit:tl,onExiting:tl,onExited:tl};Pa.UNMOUNTED=xi;Pa.EXITED=oo;Pa.ENTERING=lo;Pa.ENTERED=al;Pa.EXITING=Dd;function wT(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function dp(n,r){var l=function(c){return r&&M.isValidElement(c)?r(c):c},i=Object.create(null);return n&&M.Children.map(n,function(u){return u}).forEach(function(u){i[u.key]=l(u)}),i}function AT(n,r){n=n||{},r=r||{};function l(y){return y in r?r[y]:n[y]}var i=Object.create(null),u=[];for(var c in n)c in r?u.length&&(i[c]=u,u=[]):u.push(c);var d,p={};for(var m in r){if(i[m])for(d=0;d<i[m].length;d++){var h=i[m][d];p[i[m][d]]=l(h)}p[m]=l(m)}for(d=0;d<u.length;d++)p[u[d]]=l(u[d]);return p}function so(n,r,l){return l[r]!=null?l[r]:n.props[r]}function zT(n,r){return dp(n.children,function(l){return M.cloneElement(l,{onExited:r.bind(null,l),in:!0,appear:so(l,"appear",n),enter:so(l,"enter",n),exit:so(l,"exit",n)})})}function DT(n,r,l){var i=dp(n.children),u=AT(r,i);return Object.keys(u).forEach(function(c){var d=u[c];if(M.isValidElement(d)){var p=c in r,m=c in i,h=r[c],y=M.isValidElement(h)&&!h.props.in;m&&(!p||y)?u[c]=M.cloneElement(d,{onExited:l.bind(null,d),in:!0,exit:so(d,"exit",n),enter:so(d,"enter",n)}):!m&&p&&!y?u[c]=M.cloneElement(d,{in:!1}):m&&p&&M.isValidElement(h)&&(u[c]=M.cloneElement(d,{onExited:l.bind(null,d),in:h.props.in,exit:so(d,"exit",n),enter:so(d,"enter",n)}))}}),u}var kT=Object.values||function(n){return Object.keys(n).map(function(r){return n[r]})},$T={component:"div",childFactory:function(r){return r}},pp=function(n){P0(r,n);function r(i,u){var c;c=n.call(this,i,u)||this;var d=c.handleExited.bind(wT(c));return c.state={contextValue:{isMounting:!0},handleExited:d,firstRender:!0},c}var l=r.prototype;return l.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},l.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(u,c){var d=c.children,p=c.handleExited,m=c.firstRender;return{children:m?zT(u,p):DT(u,d,p),firstRender:!1}},l.handleExited=function(u,c){var d=dp(this.props.children);u.key in d||(u.props.onExited&&u.props.onExited(c),this.mounted&&this.setState(function(p){var m=bu({},p.children);return delete m[u.key],{children:m}}))},l.render=function(){var u=this.props,c=u.component,d=u.childFactory,p=H0(u,["component","childFactory"]),m=this.state.contextValue,h=kT(this.state.children).map(d);return delete p.appear,delete p.enter,delete p.exit,c===null?Ba.createElement(Tu.Provider,{value:m},h):Ba.createElement(Tu.Provider,{value:m},Ba.createElement(c,p,h))},r}(Ba.Component);pp.propTypes={};pp.defaultProps=$T;const tv={};function I0(n,r){const l=M.useRef(tv);return l.current===tv&&(l.current=n(r)),l}const jT=[];function NT(n){M.useEffect(n,jT)}class mp{static create(){return new mp}currentId=null;start(r,l){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,l()},r)}clear=()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)};disposeEffect=()=>this.clear}function q0(){const n=I0(mp.create).current;return NT(n.disposeEffect),n}const V0=n=>n.scrollTop;function Eu(n,r){const{timeout:l,easing:i,style:u={}}=n;return{duration:u.transitionDuration??(typeof l=="number"?l:l[r.mode]||0),easing:u.transitionTimingFunction??(typeof i=="object"?i[r.mode]:i),delay:u.transitionDelay}}function BT(n){return Xe("MuiPaper",n)}Ke("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const _T=n=>{const{square:r,elevation:l,variant:i,classes:u}=n,c={root:["root",i,!r&&"rounded",i==="elevation"&&`elevation${l}`]};return Qe(c,BT,u)},LT=pe("div",{name:"MuiPaper",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[l.variant],!l.square&&r.rounded,l.variant==="elevation"&&r[`elevation${l.elevation}`]]}})(tt(({theme:n})=>({backgroundColor:(n.vars||n).palette.background.paper,color:(n.vars||n).palette.text.primary,transition:n.transitions.create("box-shadow"),variants:[{props:({ownerState:r})=>!r.square,style:{borderRadius:n.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(n.vars||n).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),un=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiPaper"}),u=Yu(),{className:c,component:d="div",elevation:p=1,square:m=!1,variant:h="elevation",...y}=i,S={...i,component:d,elevation:p,square:m,variant:h},O=_T(S);return b.jsx(LT,{as:d,ownerState:S,className:Me(O.root,c),ref:l,...y,style:{...h==="elevation"&&{"--Paper-shadow":(u.vars||u).shadows[p],...u.vars&&{"--Paper-overlay":u.vars.overlays?.[p]},...!u.vars&&u.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${yt("#fff",Md(p))}, ${yt("#fff",Md(p))})`}},...y.style}})});function HT(n){return typeof n=="string"}function Y0(n,r,l){return n===void 0||HT(n)?r:{...r,ownerState:{...r.ownerState,...l}}}function G0(n,r,l){return typeof n=="function"?n(r,l):n}function X0(n,r=[]){if(n===void 0)return{};const l={};return Object.keys(n).filter(i=>i.match(/^on[A-Z]/)&&typeof n[i]=="function"&&!r.includes(i)).forEach(i=>{l[i]=n[i]}),l}function nv(n){if(n===void 0)return{};const r={};return Object.keys(n).filter(l=>!(l.match(/^on[A-Z]/)&&typeof n[l]=="function")).forEach(l=>{r[l]=n[l]}),r}function K0(n){const{getSlotProps:r,additionalProps:l,externalSlotProps:i,externalForwardedProps:u,className:c}=n;if(!r){const w=Me(l?.className,c,u?.className,i?.className),R={...l?.style,...u?.style,...i?.style},E={...l,...u,...i};return w.length>0&&(E.className=w),Object.keys(R).length>0&&(E.style=R),{props:E,internalRef:void 0}}const d=X0({...u,...i}),p=nv(i),m=nv(u),h=r(d),y=Me(h?.className,l?.className,c,u?.className,i?.className),S={...h?.style,...l?.style,...u?.style,...i?.style},O={...h,...l,...m,...p};return y.length>0&&(O.className=y),Object.keys(S).length>0&&(O.style=S),{props:O,internalRef:h.ref}}function wt(n,r){const{className:l,elementType:i,ownerState:u,externalForwardedProps:c,internalForwardedProps:d,shouldForwardComponentProp:p=!1,...m}=r,{component:h,slots:y={[n]:void 0},slotProps:S={[n]:void 0},...O}=c,w=y[n]||i,R=G0(S[n],u),{props:{component:E,...x},internalRef:$}=K0({className:l,...m,externalForwardedProps:n==="root"?O:void 0,externalSlotProps:R}),B=fn($,R?.ref,r.ref),D=n==="root"?E||h:E,k=Y0(w,{...n==="root"&&!h&&!y[n]&&d,...n!=="root"&&!y[n]&&d,...x,...D&&!p&&{as:D},...D&&p&&{component:D},ref:B},u);return[w,k]}function av(n){try{return n.matches(":focus-visible")}catch{}return!1}class Ru{static create(){return new Ru}static use(){const r=I0(Ru.create).current,[l,i]=M.useState(!1);return r.shouldMount=l,r.setShouldMount=i,M.useEffect(r.mountEffect,[l]),r}constructor(){this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}mount(){return this.mounted||(this.mounted=UT(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}mountEffect=()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())};start(...r){this.mount().then(()=>this.ref.current?.start(...r))}stop(...r){this.mount().then(()=>this.ref.current?.stop(...r))}pulsate(...r){this.mount().then(()=>this.ref.current?.pulsate(...r))}}function PT(){return Ru.use()}function UT(){let n,r;const l=new Promise((i,u)=>{n=i,r=u});return l.resolve=n,l.reject=r,l}function IT(n){const{className:r,classes:l,pulsate:i=!1,rippleX:u,rippleY:c,rippleSize:d,in:p,onExited:m,timeout:h}=n,[y,S]=M.useState(!1),O=Me(r,l.ripple,l.rippleVisible,i&&l.ripplePulsate),w={width:d,height:d,top:-(d/2)+c,left:-(d/2)+u},R=Me(l.child,y&&l.childLeaving,i&&l.childPulsate);return!p&&!y&&S(!0),M.useEffect(()=>{if(!p&&m!=null){const E=setTimeout(m,h);return()=>{clearTimeout(E)}}},[m,p,h]),b.jsx("span",{className:O,style:w,children:b.jsx("span",{className:R})})}const sa=Ke("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),kd=550,qT=80,VT=Hi`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,YT=Hi`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,GT=Hi`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,XT=pe("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),KT=pe(IT,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${sa.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${VT};
    animation-duration: ${kd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  &.${sa.ripplePulsate} {
    animation-duration: ${({theme:n})=>n.transitions.duration.shorter}ms;
  }

  & .${sa.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${sa.childLeaving} {
    opacity: 0;
    animation-name: ${YT};
    animation-duration: ${kd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  & .${sa.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${GT};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,QT=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTouchRipple"}),{center:u=!1,classes:c={},className:d,...p}=i,[m,h]=M.useState([]),y=M.useRef(0),S=M.useRef(null);M.useEffect(()=>{S.current&&(S.current(),S.current=null)},[m]);const O=M.useRef(!1),w=q0(),R=M.useRef(null),E=M.useRef(null),x=M.useCallback(k=>{const{pulsate:z,rippleX:N,rippleY:q,rippleSize:G,cb:K}=k;h(v=>[...v,b.jsx(KT,{classes:{ripple:Me(c.ripple,sa.ripple),rippleVisible:Me(c.rippleVisible,sa.rippleVisible),ripplePulsate:Me(c.ripplePulsate,sa.ripplePulsate),child:Me(c.child,sa.child),childLeaving:Me(c.childLeaving,sa.childLeaving),childPulsate:Me(c.childPulsate,sa.childPulsate)},timeout:kd,pulsate:z,rippleX:N,rippleY:q,rippleSize:G},y.current)]),y.current+=1,S.current=K},[c]),$=M.useCallback((k={},z={},N=()=>{})=>{const{pulsate:q=!1,center:G=u||z.pulsate,fakeElement:K=!1}=z;if(k?.type==="mousedown"&&O.current){O.current=!1;return}k?.type==="touchstart"&&(O.current=!0);const v=K?null:E.current,_=v?v.getBoundingClientRect():{width:0,height:0,left:0,top:0};let V,ae,ee;if(G||k===void 0||k.clientX===0&&k.clientY===0||!k.clientX&&!k.touches)V=Math.round(_.width/2),ae=Math.round(_.height/2);else{const{clientX:L,clientY:T}=k.touches&&k.touches.length>0?k.touches[0]:k;V=Math.round(L-_.left),ae=Math.round(T-_.top)}if(G)ee=Math.sqrt((2*_.width**2+_.height**2)/3),ee%2===0&&(ee+=1);else{const L=Math.max(Math.abs((v?v.clientWidth:0)-V),V)*2+2,T=Math.max(Math.abs((v?v.clientHeight:0)-ae),ae)*2+2;ee=Math.sqrt(L**2+T**2)}k?.touches?R.current===null&&(R.current=()=>{x({pulsate:q,rippleX:V,rippleY:ae,rippleSize:ee,cb:N})},w.start(qT,()=>{R.current&&(R.current(),R.current=null)})):x({pulsate:q,rippleX:V,rippleY:ae,rippleSize:ee,cb:N})},[u,x,w]),B=M.useCallback(()=>{$({},{pulsate:!0})},[$]),D=M.useCallback((k,z)=>{if(w.clear(),k?.type==="touchend"&&R.current){R.current(),R.current=null,w.start(0,()=>{D(k,z)});return}R.current=null,h(N=>N.length>0?N.slice(1):N),S.current=z},[w]);return M.useImperativeHandle(l,()=>({pulsate:B,start:$,stop:D}),[B,$,D]),b.jsx(XT,{className:Me(sa.root,c.root,d),ref:E,...p,children:b.jsx(pp,{component:null,exit:!0,children:m})})});function WT(n){return Xe("MuiButtonBase",n)}const FT=Ke("MuiButtonBase",["root","disabled","focusVisible"]),ZT=n=>{const{disabled:r,focusVisible:l,focusVisibleClassName:i,classes:u}=n,d=Qe({root:["root",r&&"disabled",l&&"focusVisible"]},WT,u);return l&&i&&(d.root+=` ${i}`),d},JT=pe("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${FT.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),$i=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiButtonBase"}),{action:u,centerRipple:c=!1,children:d,className:p,component:m="button",disabled:h=!1,disableRipple:y=!1,disableTouchRipple:S=!1,focusRipple:O=!1,focusVisibleClassName:w,LinkComponent:R="a",onBlur:E,onClick:x,onContextMenu:$,onDragLeave:B,onFocus:D,onFocusVisible:k,onKeyDown:z,onKeyUp:N,onMouseDown:q,onMouseLeave:G,onMouseUp:K,onTouchEnd:v,onTouchMove:_,onTouchStart:V,tabIndex:ae=0,TouchRippleProps:ee,touchRippleRef:L,type:T,...H}=i,Y=M.useRef(null),X=PT(),A=fn(X.ref,L),[U,J]=M.useState(!1);h&&U&&J(!1),M.useImperativeHandle(u,()=>({focusVisible:()=>{J(!0),Y.current.focus()}}),[]);const ne=X.shouldMount&&!y&&!h;M.useEffect(()=>{U&&O&&!y&&X.pulsate()},[y,O,U,X]);const fe=rr(X,"start",q,S),ue=rr(X,"stop",$,S),le=rr(X,"stop",B,S),ve=rr(X,"stop",K,S),xe=rr(X,"stop",ye=>{U&&ye.preventDefault(),G&&G(ye)},S),be=rr(X,"start",V,S),me=rr(X,"stop",v,S),Re=rr(X,"stop",_,S),Ne=rr(X,"stop",ye=>{av(ye.target)||J(!1),E&&E(ye)},!1),_e=Ca(ye=>{Y.current||(Y.current=ye.currentTarget),av(ye.target)&&(J(!0),k&&k(ye)),D&&D(ye)}),ke=()=>{const ye=Y.current;return m&&m!=="button"&&!(ye.tagName==="A"&&ye.href)},nt=Ca(ye=>{O&&!ye.repeat&&U&&ye.key===" "&&X.stop(ye,()=>{X.start(ye)}),ye.target===ye.currentTarget&&ke()&&ye.key===" "&&ye.preventDefault(),z&&z(ye),ye.target===ye.currentTarget&&ke()&&ye.key==="Enter"&&!h&&(ye.preventDefault(),x&&x(ye))}),Ve=Ca(ye=>{O&&ye.key===" "&&U&&!ye.defaultPrevented&&X.stop(ye,()=>{X.pulsate(ye)}),N&&N(ye),x&&ye.target===ye.currentTarget&&ke()&&ye.key===" "&&!ye.defaultPrevented&&x(ye)});let at=m;at==="button"&&(H.href||H.to)&&(at=R);const he={};at==="button"?(he.type=T===void 0?"button":T,he.disabled=h):(!H.href&&!H.to&&(he.role="button"),h&&(he["aria-disabled"]=h));const st=fn(l,Y),Te={...i,centerRipple:c,component:m,disabled:h,disableRipple:y,disableTouchRipple:S,focusRipple:O,tabIndex:ae,focusVisible:U},Le=ZT(Te);return b.jsxs(JT,{as:at,className:Me(Le.root,p),ownerState:Te,onBlur:Ne,onClick:x,onContextMenu:ue,onFocus:_e,onKeyDown:nt,onKeyUp:Ve,onMouseDown:fe,onMouseLeave:xe,onMouseUp:ve,onDragLeave:le,onTouchEnd:me,onTouchMove:Re,onTouchStart:be,ref:st,tabIndex:h?-1:ae,type:T,...he,...H,children:[d,ne?b.jsx(QT,{ref:A,center:c,...ee}):null]})});function rr(n,r,l,i=!1){return Ca(u=>(l&&l(u),i||n[r](u),!0))}function eE(n){return typeof n.main=="string"}function tE(n,r=[]){if(!eE(n))return!1;for(const l of r)if(!n.hasOwnProperty(l)||typeof n[l]!="string")return!1;return!0}function Sn(n=[]){return([,r])=>r&&tE(r,n)}function nE(n){return Xe("MuiCircularProgress",n)}Ke("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const $r=44,$d=Hi`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,jd=Hi`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,aE=typeof $d!="string"?Jd`
        animation: ${$d} 1.4s linear infinite;
      `:null,rE=typeof jd!="string"?Jd`
        animation: ${jd} 1.4s ease-in-out infinite;
      `:null,oE=n=>{const{classes:r,variant:l,color:i,disableShrink:u}=n,c={root:["root",l,`color${de(i)}`],svg:["svg"],circle:["circle",`circle${de(l)}`,u&&"circleDisableShrink"]};return Qe(c,nE,r)},lE=pe("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[l.variant],r[`color${de(l.color)}`]]}})(tt(({theme:n})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("transform")}},{props:{variant:"indeterminate"},style:aE||{animation:`${$d} 1.4s linear infinite`}},...Object.entries(n.palette).filter(Sn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}}))]}))),iE=pe("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),sE=pe("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.circle,r[`circle${de(l.variant)}`],l.disableShrink&&r.circleDisableShrink]}})(tt(({theme:n})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:r})=>r.variant==="indeterminate"&&!r.disableShrink,style:rE||{animation:`${jd} 1.4s ease-in-out infinite`}}]}))),Q0=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiCircularProgress"}),{className:u,color:c="primary",disableShrink:d=!1,size:p=40,style:m,thickness:h=3.6,value:y=0,variant:S="indeterminate",...O}=i,w={...i,color:c,disableShrink:d,size:p,thickness:h,value:y,variant:S},R=oE(w),E={},x={},$={};if(S==="determinate"){const B=2*Math.PI*(($r-h)/2);E.strokeDasharray=B.toFixed(3),$["aria-valuenow"]=Math.round(y),E.strokeDashoffset=`${((100-y)/100*B).toFixed(3)}px`,x.transform="rotate(-90deg)"}return b.jsx(lE,{className:Me(R.root,u),style:{width:p,height:p,...x,...m},ownerState:w,ref:l,role:"progressbar",...$,...O,children:b.jsx(iE,{className:R.svg,ownerState:w,viewBox:`${$r/2} ${$r/2} ${$r} ${$r}`,children:b.jsx(sE,{className:R.circle,style:E,ownerState:w,cx:$r,cy:$r,r:($r-h)/2,fill:"none",strokeWidth:h})})})});function uE(n){return Xe("MuiIconButton",n)}const rv=Ke("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),cE=n=>{const{classes:r,disabled:l,color:i,edge:u,size:c,loading:d}=n,p={root:["root",d&&"loading",l&&"disabled",i!=="default"&&`color${de(i)}`,u&&`edge${de(u)}`,`size${de(c)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return Qe(p,uE,r)},fE=pe($i,{name:"MuiIconButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.loading&&r.loading,l.color!=="default"&&r[`color${de(l.color)}`],l.edge&&r[`edge${de(l.edge)}`],r[`size${de(l.size)}`]]}})(tt(({theme:n})=>({textAlign:"center",flex:"0 0 auto",fontSize:n.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(n.vars||n).palette.action.active,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shortest}),variants:[{props:r=>!r.disableRipple,style:{"--IconButton-hoverBg":n.vars?`rgba(${n.vars.palette.action.activeChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette.action.active,n.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),tt(({theme:n})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(n.palette).filter(Sn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}})),...Object.entries(n.palette).filter(Sn()).map(([r])=>({props:{color:r},style:{"--IconButton-hoverBg":n.vars?`rgba(${(n.vars||n).palette[r].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt((n.vars||n).palette[r].main,n.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:n.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:n.typography.pxToRem(28)}}],[`&.${rv.disabled}`]:{backgroundColor:"transparent",color:(n.vars||n).palette.action.disabled},[`&.${rv.loading}`]:{color:"transparent"}}))),dE=pe("span",{name:"MuiIconButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(n.vars||n).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),hp=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiIconButton"}),{edge:u=!1,children:c,className:d,color:p="default",disabled:m=!1,disableFocusRipple:h=!1,size:y="medium",id:S,loading:O=null,loadingIndicator:w,...R}=i,E=ml(S),x=w??b.jsx(Q0,{"aria-labelledby":E,color:"inherit",size:16}),$={...i,edge:u,color:p,disabled:m,disableFocusRipple:h,loading:O,loadingIndicator:x,size:y},B=cE($);return b.jsxs(fE,{id:O?E:S,className:Me(B.root,d),centerRipple:!0,focusRipple:!h,disabled:m||O,ref:l,...R,ownerState:$,children:[typeof O=="boolean"&&b.jsx("span",{className:B.loadingWrapper,style:{display:"contents"},children:b.jsx(dE,{className:B.loadingIndicator,ownerState:$,children:O&&x})}),c]})}),pE=An(b.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}));function mE(n){return Xe("MuiTypography",n)}Ke("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const hE={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},gE=CT(),yE=n=>{const{align:r,gutterBottom:l,noWrap:i,paragraph:u,variant:c,classes:d}=n,p={root:["root",c,n.align!=="inherit"&&`align${de(r)}`,l&&"gutterBottom",i&&"noWrap",u&&"paragraph"]};return Qe(p,mE,d)},vE=pe("span",{name:"MuiTypography",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.variant&&r[l.variant],l.align!=="inherit"&&r[`align${de(l.align)}`],l.noWrap&&r.noWrap,l.gutterBottom&&r.gutterBottom,l.paragraph&&r.paragraph]}})(tt(({theme:n})=>({margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(n.typography).filter(([r,l])=>r!=="inherit"&&l&&typeof l=="object").map(([r,l])=>({props:{variant:r},style:l})),...Object.entries(n.palette).filter(Sn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}})),...Object.entries(n.palette?.text||{}).filter(([,r])=>typeof r=="string").map(([r])=>({props:{color:`text${de(r)}`},style:{color:(n.vars||n).palette.text[r]}})),{props:({ownerState:r})=>r.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:r})=>r.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:r})=>r.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:r})=>r.paragraph,style:{marginBottom:16}}]}))),ov={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},qe=M.forwardRef(function(r,l){const{color:i,...u}=Je({props:r,name:"MuiTypography"}),c=!hE[i],d=gE({...u,...c&&{color:i}}),{align:p="inherit",className:m,component:h,gutterBottom:y=!1,noWrap:S=!1,paragraph:O=!1,variant:w="body1",variantMapping:R=ov,...E}=d,x={...d,align:p,color:i,className:m,component:h,gutterBottom:y,noWrap:S,paragraph:O,variant:w,variantMapping:R},$=h||(O?"p":R[w]||ov[w])||"span",B=yE(x);return b.jsx(vE,{as:$,ref:l,className:Me(B.root,m),...E,ownerState:x,style:{...p!=="inherit"&&{"--Typography-textAlign":p},...E.style}})}),bE=n=>{const r=M.useRef({});return M.useEffect(()=>{r.current=n}),r.current};function lv(n){return n.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function SE(n={}){const{ignoreAccents:r=!0,ignoreCase:l=!0,limit:i,matchFrom:u="any",stringify:c,trim:d=!1}=n;return(p,{inputValue:m,getOptionLabel:h})=>{let y=d?m.trim():m;l&&(y=y.toLowerCase()),r&&(y=lv(y));const S=y?p.filter(O=>{let w=(c||h)(O);return l&&(w=w.toLowerCase()),r&&(w=lv(w)),u==="start"?w.startsWith(y):w.includes(y)}):p;return typeof i=="number"?S.slice(0,i):S}}const xE=SE(),iv=5,CE=n=>n.current!==null&&n.current.parentElement?.contains(document.activeElement),TE=[];function sv(n,r,l,i){if(r||n==null||i)return"";const u=l(n);return typeof u=="string"?u:""}function EE(n){const{unstable_isActiveElementInListbox:r=CE,unstable_classNamePrefix:l="Mui",autoComplete:i=!1,autoHighlight:u=!1,autoSelect:c=!1,blurOnSelect:d=!1,clearOnBlur:p=!n.freeSolo,clearOnEscape:m=!1,componentName:h="useAutocomplete",defaultValue:y=n.multiple?TE:null,disableClearable:S=!1,disableCloseOnSelect:O=!1,disabled:w,disabledItemsFocusable:R=!1,disableListWrap:E=!1,filterOptions:x=xE,filterSelectedOptions:$=!1,freeSolo:B=!1,getOptionDisabled:D,getOptionKey:k,getOptionLabel:z=te=>te.label??te,groupBy:N,handleHomeEndKeys:q=!n.freeSolo,id:G,includeInputInList:K=!1,inputValue:v,isOptionEqualToValue:_=(te,re)=>te===re,multiple:V=!1,onChange:ae,onClose:ee,onHighlightChange:L,onInputChange:T,onOpen:H,open:Y,openOnFocus:X=!1,options:A,readOnly:U=!1,renderValue:J,selectOnFocus:ne=!n.freeSolo,value:fe}=n,ue=ml(G);let le=z;le=te=>{const re=z(te);return typeof re!="string"?String(re):re};const ve=M.useRef(!1),xe=M.useRef(!0),be=M.useRef(null),me=M.useRef(null),[Re,Ne]=M.useState(null),[_e,ke]=M.useState(-1),nt=u?0:-1,Ve=M.useRef(nt),at=M.useRef(sv(y??fe,V,le)).current,[he,st]=il({controlled:fe,default:y,name:h}),[Te,Le]=il({controlled:v,default:at,name:h,state:"inputValue"}),[ye,It]=M.useState(!1),ut=M.useCallback((te,re,ge)=>{if(!(V?he.length<re.length:re!==null)&&!p)return;const Ee=sv(re,V,le,J);Te!==Ee&&(Le(Ee),T&&T(te,Ee,ge))},[le,Te,V,T,Le,p,he,J]),[At,rt]=il({controlled:Y,default:!1,name:h,state:"open"}),[je,Ye]=M.useState(!0),et=!V&&he!=null&&Te===le(he),vt=At&&!U,Ae=vt?x(A.filter(te=>!($&&(V?he:[he]).some(re=>re!==null&&_(te,re)))),{inputValue:et&&je?"":Te,getOptionLabel:le}):[],Gt=bE({filteredOptions:Ae,value:he,inputValue:Te});M.useEffect(()=>{const te=he!==Gt.value;ye&&!te||B&&!te||ut(null,he,"reset")},[he,ut,ye,Gt.value,B]);const xn=At&&Ae.length>0&&!U,Zt=Ca(te=>{if(te===-1)be.current.focus();else{const re=J?"data-item-index":"data-tag-index";Re.querySelector(`[${re}="${te}"]`).focus()}});M.useEffect(()=>{V&&_e>he.length-1&&(ke(-1),Zt(-1))},[he,V,_e,Zt]);function Ce(te,re){if(!me.current||te<0||te>=Ae.length)return-1;let ge=te;for(;;){const Be=me.current.querySelector(`[data-option-index="${ge}"]`),Ee=R?!1:!Be||Be.disabled||Be.getAttribute("aria-disabled")==="true";if(Be&&Be.hasAttribute("tabindex")&&!Ee)return ge;if(re==="next"?ge=(ge+1)%Ae.length:ge=(ge-1+Ae.length)%Ae.length,ge===te)return-1}}const Pe=Ca(({event:te,index:re,reason:ge})=>{if(Ve.current=re,re===-1?be.current.removeAttribute("aria-activedescendant"):be.current.setAttribute("aria-activedescendant",`${ue}-option-${re}`),L&&["mouse","keyboard","touch"].includes(ge)&&L(te,re===-1?null:Ae[re],ge),!me.current)return;const Be=me.current.querySelector(`[role="option"].${l}-focused`);Be&&(Be.classList.remove(`${l}-focused`),Be.classList.remove(`${l}-focusVisible`));let Ee=me.current;if(me.current.getAttribute("role")!=="listbox"&&(Ee=me.current.parentElement.querySelector('[role="listbox"]')),!Ee)return;if(re===-1){Ee.scrollTop=0;return}const Dt=me.current.querySelector(`[data-option-index="${re}"]`);if(Dt&&(Dt.classList.add(`${l}-focused`),ge==="keyboard"&&Dt.classList.add(`${l}-focusVisible`),Ee.scrollHeight>Ee.clientHeight&&ge!=="mouse"&&ge!=="touch")){const jt=Dt,yn=Ee.clientHeight+Ee.scrollTop,bl=jt.offsetTop+jt.offsetHeight;bl>yn?Ee.scrollTop=bl-Ee.clientHeight:jt.offsetTop-jt.offsetHeight*(N?1.3:0)<Ee.scrollTop&&(Ee.scrollTop=jt.offsetTop-jt.offsetHeight*(N?1.3:0))}}),ot=Ca(({event:te,diff:re,direction:ge="next",reason:Be})=>{if(!vt)return;const Dt=Ce((()=>{const jt=Ae.length-1;if(re==="reset")return nt;if(re==="start")return 0;if(re==="end")return jt;const yn=Ve.current+re;return yn<0?yn===-1&&K?-1:E&&Ve.current!==-1||Math.abs(re)>1?0:jt:yn>jt?yn===jt+1&&K?-1:E||Math.abs(re)>1?jt:0:yn})(),ge);if(Pe({index:Dt,reason:Be,event:te}),i&&re!=="reset")if(Dt===-1)be.current.value=Te;else{const jt=le(Ae[Dt]);be.current.value=jt,jt.toLowerCase().indexOf(Te.toLowerCase())===0&&Te.length>0&&be.current.setSelectionRange(Te.length,jt.length)}}),Gn=()=>{const te=(re,ge)=>{const Be=re?le(re):"",Ee=ge?le(ge):"";return Be===Ee};if(Ve.current!==-1&&Gt.filteredOptions&&Gt.filteredOptions.length!==Ae.length&&Gt.inputValue===Te&&(V?he.length===Gt.value.length&&Gt.value.every((re,ge)=>le(he[ge])===le(re)):te(Gt.value,he))){const re=Gt.filteredOptions[Ve.current];if(re)return Ae.findIndex(ge=>le(ge)===le(re))}return-1},ha=M.useCallback(()=>{if(!vt)return;const te=Gn();if(te!==-1){Ve.current=te;return}const re=V?he[0]:he;if(Ae.length===0||re==null){ot({diff:"reset"});return}if(me.current){if(re!=null){const ge=Ae[Ve.current];if(V&&ge&&he.findIndex(Ee=>_(ge,Ee))!==-1)return;const Be=Ae.findIndex(Ee=>_(Ee,re));Be===-1?ot({diff:"reset"}):Pe({index:Be});return}if(Ve.current>=Ae.length-1){Pe({index:Ae.length-1});return}Pe({index:Ve.current})}},[Ae.length,V?!1:he,$,ot,Pe,vt,Te,V]),po=Ca(te=>{Ad(me,te),te&&ha()});M.useEffect(()=>{ha()},[ha]);const tn=te=>{At||(rt(!0),Ye(!0),H&&H(te))},zn=(te,re)=>{At&&(rt(!1),ee&&ee(te,re))},ga=(te,re,ge,Be)=>{if(V){if(he.length===re.length&&he.every((Ee,Dt)=>Ee===re[Dt]))return}else if(he===re)return;ae&&ae(te,re,ge,Be),st(re)},Ua=M.useRef(!1),Dn=(te,re,ge="selectOption",Be="options")=>{let Ee=ge,Dt=re;if(V){Dt=Array.isArray(he)?he.slice():[];const jt=Dt.findIndex(yn=>_(re,yn));jt===-1?Dt.push(re):Be!=="freeSolo"&&(Dt.splice(jt,1),Ee="removeOption")}ut(te,Dt,Ee),ga(te,Dt,Ee,{option:re}),!O&&(!te||!te.ctrlKey&&!te.metaKey)&&zn(te,Ee),(d===!0||d==="touch"&&Ua.current||d==="mouse"&&!Ua.current)&&be.current.blur()};function ur(te,re){if(te===-1)return-1;let ge=te;for(;;){if(re==="next"&&ge===he.length||re==="previous"&&ge===-1)return-1;const Be=J?"data-item-index":"data-tag-index",Ee=Re.querySelector(`[${Be}="${ge}"]`);if(!Ee||!Ee.hasAttribute("tabindex")||Ee.disabled||Ee.getAttribute("aria-disabled")==="true")ge+=re==="next"?1:-1;else return ge}}const cr=(te,re)=>{if(!V)return;Te===""&&zn(te,"toggleInput");let ge=_e;_e===-1?Te===""&&re==="previous"&&(ge=he.length-1):(ge+=re==="next"?1:-1,ge<0&&(ge=0),ge===he.length&&(ge=-1)),ge=ur(ge,re),ke(ge),Zt(ge)},Ea=te=>{ve.current=!0,Le(""),T&&T(te,"","clear"),ga(te,V?[]:null,"clear")},gl=te=>re=>{if(te.onKeyDown&&te.onKeyDown(re),!re.defaultMuiPrevented&&(_e!==-1&&!["ArrowLeft","ArrowRight"].includes(re.key)&&(ke(-1),Zt(-1)),re.which!==229))switch(re.key){case"Home":vt&&q&&(re.preventDefault(),ot({diff:"start",direction:"next",reason:"keyboard",event:re}));break;case"End":vt&&q&&(re.preventDefault(),ot({diff:"end",direction:"previous",reason:"keyboard",event:re}));break;case"PageUp":re.preventDefault(),ot({diff:-iv,direction:"previous",reason:"keyboard",event:re}),tn(re);break;case"PageDown":re.preventDefault(),ot({diff:iv,direction:"next",reason:"keyboard",event:re}),tn(re);break;case"ArrowDown":re.preventDefault(),ot({diff:1,direction:"next",reason:"keyboard",event:re}),tn(re);break;case"ArrowUp":re.preventDefault(),ot({diff:-1,direction:"previous",reason:"keyboard",event:re}),tn(re);break;case"ArrowLeft":!V&&J?Zt(0):cr(re,"previous");break;case"ArrowRight":!V&&J?Zt(-1):cr(re,"next");break;case"Enter":if(Ve.current!==-1&&vt){const ge=Ae[Ve.current],Be=D?D(ge):!1;if(re.preventDefault(),Be)return;Dn(re,ge,"selectOption"),i&&be.current.setSelectionRange(be.current.value.length,be.current.value.length)}else B&&Te!==""&&et===!1&&(V&&re.preventDefault(),Dn(re,Te,"createOption","freeSolo"));break;case"Escape":vt?(re.preventDefault(),re.stopPropagation(),zn(re,"escape")):m&&(Te!==""||V&&he.length>0||J)&&(re.preventDefault(),re.stopPropagation(),Ea(re));break;case"Backspace":if(V&&!U&&Te===""&&he.length>0){const ge=_e===-1?he.length-1:_e,Be=he.slice();Be.splice(ge,1),ga(re,Be,"removeOption",{option:he[ge]})}!V&&J&&!U&&(st(null),Zt(-1));break;case"Delete":if(V&&!U&&Te===""&&he.length>0&&_e!==-1){const ge=_e,Be=he.slice();Be.splice(ge,1),ga(re,Be,"removeOption",{option:he[ge]})}!V&&J&&!U&&(st(null),Zt(-1));break}},Xn=te=>{It(!0),X&&!ve.current&&tn(te)},zt=te=>{if(r(me)){be.current.focus();return}It(!1),xe.current=!0,ve.current=!1,c&&Ve.current!==-1&&vt?Dn(te,Ae[Ve.current],"blur"):c&&B&&Te!==""?Dn(te,Te,"blur","freeSolo"):p&&ut(te,he,"blur"),zn(te,"blur")},Xt=te=>{const re=te.target.value;Te!==re&&(Le(re),Ye(!1),T&&T(te,re,"input")),re===""?!S&&!V&&ga(te,null,"clear"):tn(te)},Kn=te=>{const re=Number(te.currentTarget.getAttribute("data-option-index"));Ve.current!==re&&Pe({event:te,index:re,reason:"mouse"})},Pr=te=>{Pe({event:te,index:Number(te.currentTarget.getAttribute("data-option-index")),reason:"touch"}),Ua.current=!0},yl=te=>{const re=Number(te.currentTarget.getAttribute("data-option-index"));Dn(te,Ae[re],"selectOption"),Ua.current=!1},mo=te=>re=>{const ge=he.slice();ge.splice(te,1),ga(re,ge,"removeOption",{option:he[te]})},vl=te=>{ga(te,null,"removeOption",{option:he})},Qn=te=>{At?zn(te,"toggleInput"):tn(te)},Bt=te=>{te.currentTarget.contains(te.target)&&te.target.getAttribute("id")!==ue&&te.preventDefault()},Mt=te=>{te.currentTarget.contains(te.target)&&(be.current.focus(),ne&&xe.current&&be.current.selectionEnd-be.current.selectionStart===0&&be.current.select(),xe.current=!1)},gn=te=>{!w&&(Te===""||!At)&&Qn(te)};let ya=B&&Te.length>0;ya=ya||(V?he.length>0:he!==null);let va=Ae;return N&&(va=Ae.reduce((te,re,ge)=>{const Be=N(re);return te.length>0&&te[te.length-1].group===Be?te[te.length-1].options.push(re):te.push({key:ge,index:ge,group:Be,options:[re]}),te},[])),w&&ye&&zt(),{getRootProps:(te={})=>({...te,onKeyDown:gl(te),onMouseDown:Bt,onClick:Mt}),getInputLabelProps:()=>({id:`${ue}-label`,htmlFor:ue}),getInputProps:()=>({id:ue,value:Te,onBlur:zt,onFocus:Xn,onChange:Xt,onMouseDown:gn,"aria-activedescendant":vt?"":null,"aria-autocomplete":i?"both":"list","aria-controls":xn?`${ue}-listbox`:void 0,"aria-expanded":xn,autoComplete:"off",ref:be,autoCapitalize:"none",spellCheck:"false",role:"combobox",disabled:w}),getClearProps:()=>({tabIndex:-1,type:"button",onClick:Ea}),getItemProps:({index:te=0}={})=>({...V&&{key:te},...J?{"data-item-index":te}:{"data-tag-index":te},tabIndex:-1,...!U&&{onDelete:V?mo(te):vl}}),getPopupIndicatorProps:()=>({tabIndex:-1,type:"button",onClick:Qn}),getTagProps:({index:te})=>({key:te,"data-tag-index":te,tabIndex:-1,...!U&&{onDelete:mo(te)}}),getListboxProps:()=>({role:"listbox",id:`${ue}-listbox`,"aria-labelledby":`${ue}-label`,ref:po,onMouseDown:te=>{te.preventDefault()}}),getOptionProps:({index:te,option:re})=>{const ge=(V?he:[he]).some(Ee=>Ee!=null&&_(re,Ee)),Be=D?D(re):!1;return{key:k?.(re)??le(re),tabIndex:-1,role:"option",id:`${ue}-option-${te}`,onMouseMove:Kn,onClick:yl,onTouchStart:Pr,"data-option-index":te,"aria-disabled":Be,"aria-selected":ge}},id:ue,inputValue:Te,value:he,dirty:ya,expanded:vt&&Re,popupOpen:vt,focused:ye||_e!==-1,anchorEl:Re,setAnchorEl:Ne,focusedItem:_e,focusedTag:_e,groupedOptions:va}}var On="top",da="bottom",pa="right",Mn="left",gp="auto",Vi=[On,da,pa,Mn],ul="start",ji="end",RE="clippingParents",W0="viewport",gi="popper",OE="reference",uv=Vi.reduce(function(n,r){return n.concat([r+"-"+ul,r+"-"+ji])},[]),F0=[].concat(Vi,[gp]).reduce(function(n,r){return n.concat([r,r+"-"+ul,r+"-"+ji])},[]),ME="beforeRead",wE="read",AE="afterRead",zE="beforeMain",DE="main",kE="afterMain",$E="beforeWrite",jE="write",NE="afterWrite",BE=[ME,wE,AE,zE,DE,kE,$E,jE,NE];function Ha(n){return n?(n.nodeName||"").toLowerCase():null}function Vn(n){if(n==null)return window;if(n.toString()!=="[object Window]"){var r=n.ownerDocument;return r&&r.defaultView||window}return n}function co(n){var r=Vn(n).Element;return n instanceof r||n instanceof Element}function ca(n){var r=Vn(n).HTMLElement;return n instanceof r||n instanceof HTMLElement}function yp(n){if(typeof ShadowRoot>"u")return!1;var r=Vn(n).ShadowRoot;return n instanceof r||n instanceof ShadowRoot}function _E(n){var r=n.state;Object.keys(r.elements).forEach(function(l){var i=r.styles[l]||{},u=r.attributes[l]||{},c=r.elements[l];!ca(c)||!Ha(c)||(Object.assign(c.style,i),Object.keys(u).forEach(function(d){var p=u[d];p===!1?c.removeAttribute(d):c.setAttribute(d,p===!0?"":p)}))})}function LE(n){var r=n.state,l={popper:{position:r.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(r.elements.popper.style,l.popper),r.styles=l,r.elements.arrow&&Object.assign(r.elements.arrow.style,l.arrow),function(){Object.keys(r.elements).forEach(function(i){var u=r.elements[i],c=r.attributes[i]||{},d=Object.keys(r.styles.hasOwnProperty(i)?r.styles[i]:l[i]),p=d.reduce(function(m,h){return m[h]="",m},{});!ca(u)||!Ha(u)||(Object.assign(u.style,p),Object.keys(c).forEach(function(m){u.removeAttribute(m)}))})}}const HE={name:"applyStyles",enabled:!0,phase:"write",fn:_E,effect:LE,requires:["computeStyles"]};function La(n){return n.split("-")[0]}var uo=Math.max,Ou=Math.min,cl=Math.round;function Nd(){var n=navigator.userAgentData;return n!=null&&n.brands&&Array.isArray(n.brands)?n.brands.map(function(r){return r.brand+"/"+r.version}).join(" "):navigator.userAgent}function Z0(){return!/^((?!chrome|android).)*safari/i.test(Nd())}function fl(n,r,l){r===void 0&&(r=!1),l===void 0&&(l=!1);var i=n.getBoundingClientRect(),u=1,c=1;r&&ca(n)&&(u=n.offsetWidth>0&&cl(i.width)/n.offsetWidth||1,c=n.offsetHeight>0&&cl(i.height)/n.offsetHeight||1);var d=co(n)?Vn(n):window,p=d.visualViewport,m=!Z0()&&l,h=(i.left+(m&&p?p.offsetLeft:0))/u,y=(i.top+(m&&p?p.offsetTop:0))/c,S=i.width/u,O=i.height/c;return{width:S,height:O,top:y,right:h+S,bottom:y+O,left:h,x:h,y}}function vp(n){var r=fl(n),l=n.offsetWidth,i=n.offsetHeight;return Math.abs(r.width-l)<=1&&(l=r.width),Math.abs(r.height-i)<=1&&(i=r.height),{x:n.offsetLeft,y:n.offsetTop,width:l,height:i}}function J0(n,r){var l=r.getRootNode&&r.getRootNode();if(n.contains(r))return!0;if(l&&yp(l)){var i=r;do{if(i&&n.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function sr(n){return Vn(n).getComputedStyle(n)}function PE(n){return["table","td","th"].indexOf(Ha(n))>=0}function Lr(n){return((co(n)?n.ownerDocument:n.document)||window.document).documentElement}function Gu(n){return Ha(n)==="html"?n:n.assignedSlot||n.parentNode||(yp(n)?n.host:null)||Lr(n)}function cv(n){return!ca(n)||sr(n).position==="fixed"?null:n.offsetParent}function UE(n){var r=/firefox/i.test(Nd()),l=/Trident/i.test(Nd());if(l&&ca(n)){var i=sr(n);if(i.position==="fixed")return null}var u=Gu(n);for(yp(u)&&(u=u.host);ca(u)&&["html","body"].indexOf(Ha(u))<0;){var c=sr(u);if(c.transform!=="none"||c.perspective!=="none"||c.contain==="paint"||["transform","perspective"].indexOf(c.willChange)!==-1||r&&c.willChange==="filter"||r&&c.filter&&c.filter!=="none")return u;u=u.parentNode}return null}function Yi(n){for(var r=Vn(n),l=cv(n);l&&PE(l)&&sr(l).position==="static";)l=cv(l);return l&&(Ha(l)==="html"||Ha(l)==="body"&&sr(l).position==="static")?r:l||UE(n)||r}function bp(n){return["top","bottom"].indexOf(n)>=0?"x":"y"}function Ei(n,r,l){return uo(n,Ou(r,l))}function IE(n,r,l){var i=Ei(n,r,l);return i>l?l:i}function eb(){return{top:0,right:0,bottom:0,left:0}}function tb(n){return Object.assign({},eb(),n)}function nb(n,r){return r.reduce(function(l,i){return l[i]=n,l},{})}var qE=function(r,l){return r=typeof r=="function"?r(Object.assign({},l.rects,{placement:l.placement})):r,tb(typeof r!="number"?r:nb(r,Vi))};function VE(n){var r,l=n.state,i=n.name,u=n.options,c=l.elements.arrow,d=l.modifiersData.popperOffsets,p=La(l.placement),m=bp(p),h=[Mn,pa].indexOf(p)>=0,y=h?"height":"width";if(!(!c||!d)){var S=qE(u.padding,l),O=vp(c),w=m==="y"?On:Mn,R=m==="y"?da:pa,E=l.rects.reference[y]+l.rects.reference[m]-d[m]-l.rects.popper[y],x=d[m]-l.rects.reference[m],$=Yi(c),B=$?m==="y"?$.clientHeight||0:$.clientWidth||0:0,D=E/2-x/2,k=S[w],z=B-O[y]-S[R],N=B/2-O[y]/2+D,q=Ei(k,N,z),G=m;l.modifiersData[i]=(r={},r[G]=q,r.centerOffset=q-N,r)}}function YE(n){var r=n.state,l=n.options,i=l.element,u=i===void 0?"[data-popper-arrow]":i;u!=null&&(typeof u=="string"&&(u=r.elements.popper.querySelector(u),!u)||J0(r.elements.popper,u)&&(r.elements.arrow=u))}const GE={name:"arrow",enabled:!0,phase:"main",fn:VE,effect:YE,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function dl(n){return n.split("-")[1]}var XE={top:"auto",right:"auto",bottom:"auto",left:"auto"};function KE(n,r){var l=n.x,i=n.y,u=r.devicePixelRatio||1;return{x:cl(l*u)/u||0,y:cl(i*u)/u||0}}function fv(n){var r,l=n.popper,i=n.popperRect,u=n.placement,c=n.variation,d=n.offsets,p=n.position,m=n.gpuAcceleration,h=n.adaptive,y=n.roundOffsets,S=n.isFixed,O=d.x,w=O===void 0?0:O,R=d.y,E=R===void 0?0:R,x=typeof y=="function"?y({x:w,y:E}):{x:w,y:E};w=x.x,E=x.y;var $=d.hasOwnProperty("x"),B=d.hasOwnProperty("y"),D=Mn,k=On,z=window;if(h){var N=Yi(l),q="clientHeight",G="clientWidth";if(N===Vn(l)&&(N=Lr(l),sr(N).position!=="static"&&p==="absolute"&&(q="scrollHeight",G="scrollWidth")),N=N,u===On||(u===Mn||u===pa)&&c===ji){k=da;var K=S&&N===z&&z.visualViewport?z.visualViewport.height:N[q];E-=K-i.height,E*=m?1:-1}if(u===Mn||(u===On||u===da)&&c===ji){D=pa;var v=S&&N===z&&z.visualViewport?z.visualViewport.width:N[G];w-=v-i.width,w*=m?1:-1}}var _=Object.assign({position:p},h&&XE),V=y===!0?KE({x:w,y:E},Vn(l)):{x:w,y:E};if(w=V.x,E=V.y,m){var ae;return Object.assign({},_,(ae={},ae[k]=B?"0":"",ae[D]=$?"0":"",ae.transform=(z.devicePixelRatio||1)<=1?"translate("+w+"px, "+E+"px)":"translate3d("+w+"px, "+E+"px, 0)",ae))}return Object.assign({},_,(r={},r[k]=B?E+"px":"",r[D]=$?w+"px":"",r.transform="",r))}function QE(n){var r=n.state,l=n.options,i=l.gpuAcceleration,u=i===void 0?!0:i,c=l.adaptive,d=c===void 0?!0:c,p=l.roundOffsets,m=p===void 0?!0:p,h={placement:La(r.placement),variation:dl(r.placement),popper:r.elements.popper,popperRect:r.rects.popper,gpuAcceleration:u,isFixed:r.options.strategy==="fixed"};r.modifiersData.popperOffsets!=null&&(r.styles.popper=Object.assign({},r.styles.popper,fv(Object.assign({},h,{offsets:r.modifiersData.popperOffsets,position:r.options.strategy,adaptive:d,roundOffsets:m})))),r.modifiersData.arrow!=null&&(r.styles.arrow=Object.assign({},r.styles.arrow,fv(Object.assign({},h,{offsets:r.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:m})))),r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-placement":r.placement})}const WE={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:QE,data:{}};var lu={passive:!0};function FE(n){var r=n.state,l=n.instance,i=n.options,u=i.scroll,c=u===void 0?!0:u,d=i.resize,p=d===void 0?!0:d,m=Vn(r.elements.popper),h=[].concat(r.scrollParents.reference,r.scrollParents.popper);return c&&h.forEach(function(y){y.addEventListener("scroll",l.update,lu)}),p&&m.addEventListener("resize",l.update,lu),function(){c&&h.forEach(function(y){y.removeEventListener("scroll",l.update,lu)}),p&&m.removeEventListener("resize",l.update,lu)}}const ZE={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:FE,data:{}};var JE={left:"right",right:"left",bottom:"top",top:"bottom"};function hu(n){return n.replace(/left|right|bottom|top/g,function(r){return JE[r]})}var eR={start:"end",end:"start"};function dv(n){return n.replace(/start|end/g,function(r){return eR[r]})}function Sp(n){var r=Vn(n),l=r.pageXOffset,i=r.pageYOffset;return{scrollLeft:l,scrollTop:i}}function xp(n){return fl(Lr(n)).left+Sp(n).scrollLeft}function tR(n,r){var l=Vn(n),i=Lr(n),u=l.visualViewport,c=i.clientWidth,d=i.clientHeight,p=0,m=0;if(u){c=u.width,d=u.height;var h=Z0();(h||!h&&r==="fixed")&&(p=u.offsetLeft,m=u.offsetTop)}return{width:c,height:d,x:p+xp(n),y:m}}function nR(n){var r,l=Lr(n),i=Sp(n),u=(r=n.ownerDocument)==null?void 0:r.body,c=uo(l.scrollWidth,l.clientWidth,u?u.scrollWidth:0,u?u.clientWidth:0),d=uo(l.scrollHeight,l.clientHeight,u?u.scrollHeight:0,u?u.clientHeight:0),p=-i.scrollLeft+xp(n),m=-i.scrollTop;return sr(u||l).direction==="rtl"&&(p+=uo(l.clientWidth,u?u.clientWidth:0)-c),{width:c,height:d,x:p,y:m}}function Cp(n){var r=sr(n),l=r.overflow,i=r.overflowX,u=r.overflowY;return/auto|scroll|overlay|hidden/.test(l+u+i)}function ab(n){return["html","body","#document"].indexOf(Ha(n))>=0?n.ownerDocument.body:ca(n)&&Cp(n)?n:ab(Gu(n))}function Ri(n,r){var l;r===void 0&&(r=[]);var i=ab(n),u=i===((l=n.ownerDocument)==null?void 0:l.body),c=Vn(i),d=u?[c].concat(c.visualViewport||[],Cp(i)?i:[]):i,p=r.concat(d);return u?p:p.concat(Ri(Gu(d)))}function Bd(n){return Object.assign({},n,{left:n.x,top:n.y,right:n.x+n.width,bottom:n.y+n.height})}function aR(n,r){var l=fl(n,!1,r==="fixed");return l.top=l.top+n.clientTop,l.left=l.left+n.clientLeft,l.bottom=l.top+n.clientHeight,l.right=l.left+n.clientWidth,l.width=n.clientWidth,l.height=n.clientHeight,l.x=l.left,l.y=l.top,l}function pv(n,r,l){return r===W0?Bd(tR(n,l)):co(r)?aR(r,l):Bd(nR(Lr(n)))}function rR(n){var r=Ri(Gu(n)),l=["absolute","fixed"].indexOf(sr(n).position)>=0,i=l&&ca(n)?Yi(n):n;return co(i)?r.filter(function(u){return co(u)&&J0(u,i)&&Ha(u)!=="body"}):[]}function oR(n,r,l,i){var u=r==="clippingParents"?rR(n):[].concat(r),c=[].concat(u,[l]),d=c[0],p=c.reduce(function(m,h){var y=pv(n,h,i);return m.top=uo(y.top,m.top),m.right=Ou(y.right,m.right),m.bottom=Ou(y.bottom,m.bottom),m.left=uo(y.left,m.left),m},pv(n,d,i));return p.width=p.right-p.left,p.height=p.bottom-p.top,p.x=p.left,p.y=p.top,p}function rb(n){var r=n.reference,l=n.element,i=n.placement,u=i?La(i):null,c=i?dl(i):null,d=r.x+r.width/2-l.width/2,p=r.y+r.height/2-l.height/2,m;switch(u){case On:m={x:d,y:r.y-l.height};break;case da:m={x:d,y:r.y+r.height};break;case pa:m={x:r.x+r.width,y:p};break;case Mn:m={x:r.x-l.width,y:p};break;default:m={x:r.x,y:r.y}}var h=u?bp(u):null;if(h!=null){var y=h==="y"?"height":"width";switch(c){case ul:m[h]=m[h]-(r[y]/2-l[y]/2);break;case ji:m[h]=m[h]+(r[y]/2-l[y]/2);break}}return m}function Ni(n,r){r===void 0&&(r={});var l=r,i=l.placement,u=i===void 0?n.placement:i,c=l.strategy,d=c===void 0?n.strategy:c,p=l.boundary,m=p===void 0?RE:p,h=l.rootBoundary,y=h===void 0?W0:h,S=l.elementContext,O=S===void 0?gi:S,w=l.altBoundary,R=w===void 0?!1:w,E=l.padding,x=E===void 0?0:E,$=tb(typeof x!="number"?x:nb(x,Vi)),B=O===gi?OE:gi,D=n.rects.popper,k=n.elements[R?B:O],z=oR(co(k)?k:k.contextElement||Lr(n.elements.popper),m,y,d),N=fl(n.elements.reference),q=rb({reference:N,element:D,placement:u}),G=Bd(Object.assign({},D,q)),K=O===gi?G:N,v={top:z.top-K.top+$.top,bottom:K.bottom-z.bottom+$.bottom,left:z.left-K.left+$.left,right:K.right-z.right+$.right},_=n.modifiersData.offset;if(O===gi&&_){var V=_[u];Object.keys(v).forEach(function(ae){var ee=[pa,da].indexOf(ae)>=0?1:-1,L=[On,da].indexOf(ae)>=0?"y":"x";v[ae]+=V[L]*ee})}return v}function lR(n,r){r===void 0&&(r={});var l=r,i=l.placement,u=l.boundary,c=l.rootBoundary,d=l.padding,p=l.flipVariations,m=l.allowedAutoPlacements,h=m===void 0?F0:m,y=dl(i),S=y?p?uv:uv.filter(function(R){return dl(R)===y}):Vi,O=S.filter(function(R){return h.indexOf(R)>=0});O.length===0&&(O=S);var w=O.reduce(function(R,E){return R[E]=Ni(n,{placement:E,boundary:u,rootBoundary:c,padding:d})[La(E)],R},{});return Object.keys(w).sort(function(R,E){return w[R]-w[E]})}function iR(n){if(La(n)===gp)return[];var r=hu(n);return[dv(n),r,dv(r)]}function sR(n){var r=n.state,l=n.options,i=n.name;if(!r.modifiersData[i]._skip){for(var u=l.mainAxis,c=u===void 0?!0:u,d=l.altAxis,p=d===void 0?!0:d,m=l.fallbackPlacements,h=l.padding,y=l.boundary,S=l.rootBoundary,O=l.altBoundary,w=l.flipVariations,R=w===void 0?!0:w,E=l.allowedAutoPlacements,x=r.options.placement,$=La(x),B=$===x,D=m||(B||!R?[hu(x)]:iR(x)),k=[x].concat(D).reduce(function(fe,ue){return fe.concat(La(ue)===gp?lR(r,{placement:ue,boundary:y,rootBoundary:S,padding:h,flipVariations:R,allowedAutoPlacements:E}):ue)},[]),z=r.rects.reference,N=r.rects.popper,q=new Map,G=!0,K=k[0],v=0;v<k.length;v++){var _=k[v],V=La(_),ae=dl(_)===ul,ee=[On,da].indexOf(V)>=0,L=ee?"width":"height",T=Ni(r,{placement:_,boundary:y,rootBoundary:S,altBoundary:O,padding:h}),H=ee?ae?pa:Mn:ae?da:On;z[L]>N[L]&&(H=hu(H));var Y=hu(H),X=[];if(c&&X.push(T[V]<=0),p&&X.push(T[H]<=0,T[Y]<=0),X.every(function(fe){return fe})){K=_,G=!1;break}q.set(_,X)}if(G)for(var A=R?3:1,U=function(ue){var le=k.find(function(ve){var xe=q.get(ve);if(xe)return xe.slice(0,ue).every(function(be){return be})});if(le)return K=le,"break"},J=A;J>0;J--){var ne=U(J);if(ne==="break")break}r.placement!==K&&(r.modifiersData[i]._skip=!0,r.placement=K,r.reset=!0)}}const uR={name:"flip",enabled:!0,phase:"main",fn:sR,requiresIfExists:["offset"],data:{_skip:!1}};function mv(n,r,l){return l===void 0&&(l={x:0,y:0}),{top:n.top-r.height-l.y,right:n.right-r.width+l.x,bottom:n.bottom-r.height+l.y,left:n.left-r.width-l.x}}function hv(n){return[On,pa,da,Mn].some(function(r){return n[r]>=0})}function cR(n){var r=n.state,l=n.name,i=r.rects.reference,u=r.rects.popper,c=r.modifiersData.preventOverflow,d=Ni(r,{elementContext:"reference"}),p=Ni(r,{altBoundary:!0}),m=mv(d,i),h=mv(p,u,c),y=hv(m),S=hv(h);r.modifiersData[l]={referenceClippingOffsets:m,popperEscapeOffsets:h,isReferenceHidden:y,hasPopperEscaped:S},r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-reference-hidden":y,"data-popper-escaped":S})}const fR={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:cR};function dR(n,r,l){var i=La(n),u=[Mn,On].indexOf(i)>=0?-1:1,c=typeof l=="function"?l(Object.assign({},r,{placement:n})):l,d=c[0],p=c[1];return d=d||0,p=(p||0)*u,[Mn,pa].indexOf(i)>=0?{x:p,y:d}:{x:d,y:p}}function pR(n){var r=n.state,l=n.options,i=n.name,u=l.offset,c=u===void 0?[0,0]:u,d=F0.reduce(function(y,S){return y[S]=dR(S,r.rects,c),y},{}),p=d[r.placement],m=p.x,h=p.y;r.modifiersData.popperOffsets!=null&&(r.modifiersData.popperOffsets.x+=m,r.modifiersData.popperOffsets.y+=h),r.modifiersData[i]=d}const mR={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:pR};function hR(n){var r=n.state,l=n.name;r.modifiersData[l]=rb({reference:r.rects.reference,element:r.rects.popper,placement:r.placement})}const gR={name:"popperOffsets",enabled:!0,phase:"read",fn:hR,data:{}};function yR(n){return n==="x"?"y":"x"}function vR(n){var r=n.state,l=n.options,i=n.name,u=l.mainAxis,c=u===void 0?!0:u,d=l.altAxis,p=d===void 0?!1:d,m=l.boundary,h=l.rootBoundary,y=l.altBoundary,S=l.padding,O=l.tether,w=O===void 0?!0:O,R=l.tetherOffset,E=R===void 0?0:R,x=Ni(r,{boundary:m,rootBoundary:h,padding:S,altBoundary:y}),$=La(r.placement),B=dl(r.placement),D=!B,k=bp($),z=yR(k),N=r.modifiersData.popperOffsets,q=r.rects.reference,G=r.rects.popper,K=typeof E=="function"?E(Object.assign({},r.rects,{placement:r.placement})):E,v=typeof K=="number"?{mainAxis:K,altAxis:K}:Object.assign({mainAxis:0,altAxis:0},K),_=r.modifiersData.offset?r.modifiersData.offset[r.placement]:null,V={x:0,y:0};if(N){if(c){var ae,ee=k==="y"?On:Mn,L=k==="y"?da:pa,T=k==="y"?"height":"width",H=N[k],Y=H+x[ee],X=H-x[L],A=w?-G[T]/2:0,U=B===ul?q[T]:G[T],J=B===ul?-G[T]:-q[T],ne=r.elements.arrow,fe=w&&ne?vp(ne):{width:0,height:0},ue=r.modifiersData["arrow#persistent"]?r.modifiersData["arrow#persistent"].padding:eb(),le=ue[ee],ve=ue[L],xe=Ei(0,q[T],fe[T]),be=D?q[T]/2-A-xe-le-v.mainAxis:U-xe-le-v.mainAxis,me=D?-q[T]/2+A+xe+ve+v.mainAxis:J+xe+ve+v.mainAxis,Re=r.elements.arrow&&Yi(r.elements.arrow),Ne=Re?k==="y"?Re.clientTop||0:Re.clientLeft||0:0,_e=(ae=_?.[k])!=null?ae:0,ke=H+be-_e-Ne,nt=H+me-_e,Ve=Ei(w?Ou(Y,ke):Y,H,w?uo(X,nt):X);N[k]=Ve,V[k]=Ve-H}if(p){var at,he=k==="x"?On:Mn,st=k==="x"?da:pa,Te=N[z],Le=z==="y"?"height":"width",ye=Te+x[he],It=Te-x[st],ut=[On,Mn].indexOf($)!==-1,At=(at=_?.[z])!=null?at:0,rt=ut?ye:Te-q[Le]-G[Le]-At+v.altAxis,je=ut?Te+q[Le]+G[Le]-At-v.altAxis:It,Ye=w&&ut?IE(rt,Te,je):Ei(w?rt:ye,Te,w?je:It);N[z]=Ye,V[z]=Ye-Te}r.modifiersData[i]=V}}const bR={name:"preventOverflow",enabled:!0,phase:"main",fn:vR,requiresIfExists:["offset"]};function SR(n){return{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}}function xR(n){return n===Vn(n)||!ca(n)?Sp(n):SR(n)}function CR(n){var r=n.getBoundingClientRect(),l=cl(r.width)/n.offsetWidth||1,i=cl(r.height)/n.offsetHeight||1;return l!==1||i!==1}function TR(n,r,l){l===void 0&&(l=!1);var i=ca(r),u=ca(r)&&CR(r),c=Lr(r),d=fl(n,u,l),p={scrollLeft:0,scrollTop:0},m={x:0,y:0};return(i||!i&&!l)&&((Ha(r)!=="body"||Cp(c))&&(p=xR(r)),ca(r)?(m=fl(r,!0),m.x+=r.clientLeft,m.y+=r.clientTop):c&&(m.x=xp(c))),{x:d.left+p.scrollLeft-m.x,y:d.top+p.scrollTop-m.y,width:d.width,height:d.height}}function ER(n){var r=new Map,l=new Set,i=[];n.forEach(function(c){r.set(c.name,c)});function u(c){l.add(c.name);var d=[].concat(c.requires||[],c.requiresIfExists||[]);d.forEach(function(p){if(!l.has(p)){var m=r.get(p);m&&u(m)}}),i.push(c)}return n.forEach(function(c){l.has(c.name)||u(c)}),i}function RR(n){var r=ER(n);return BE.reduce(function(l,i){return l.concat(r.filter(function(u){return u.phase===i}))},[])}function OR(n){var r;return function(){return r||(r=new Promise(function(l){Promise.resolve().then(function(){r=void 0,l(n())})})),r}}function MR(n){var r=n.reduce(function(l,i){var u=l[i.name];return l[i.name]=u?Object.assign({},u,i,{options:Object.assign({},u.options,i.options),data:Object.assign({},u.data,i.data)}):i,l},{});return Object.keys(r).map(function(l){return r[l]})}var gv={placement:"bottom",modifiers:[],strategy:"absolute"};function yv(){for(var n=arguments.length,r=new Array(n),l=0;l<n;l++)r[l]=arguments[l];return!r.some(function(i){return!(i&&typeof i.getBoundingClientRect=="function")})}function wR(n){n===void 0&&(n={});var r=n,l=r.defaultModifiers,i=l===void 0?[]:l,u=r.defaultOptions,c=u===void 0?gv:u;return function(p,m,h){h===void 0&&(h=c);var y={placement:"bottom",orderedModifiers:[],options:Object.assign({},gv,c),modifiersData:{},elements:{reference:p,popper:m},attributes:{},styles:{}},S=[],O=!1,w={state:y,setOptions:function($){var B=typeof $=="function"?$(y.options):$;E(),y.options=Object.assign({},c,y.options,B),y.scrollParents={reference:co(p)?Ri(p):p.contextElement?Ri(p.contextElement):[],popper:Ri(m)};var D=RR(MR([].concat(i,y.options.modifiers)));return y.orderedModifiers=D.filter(function(k){return k.enabled}),R(),w.update()},forceUpdate:function(){if(!O){var $=y.elements,B=$.reference,D=$.popper;if(yv(B,D)){y.rects={reference:TR(B,Yi(D),y.options.strategy==="fixed"),popper:vp(D)},y.reset=!1,y.placement=y.options.placement,y.orderedModifiers.forEach(function(v){return y.modifiersData[v.name]=Object.assign({},v.data)});for(var k=0;k<y.orderedModifiers.length;k++){if(y.reset===!0){y.reset=!1,k=-1;continue}var z=y.orderedModifiers[k],N=z.fn,q=z.options,G=q===void 0?{}:q,K=z.name;typeof N=="function"&&(y=N({state:y,options:G,name:K,instance:w})||y)}}}},update:OR(function(){return new Promise(function(x){w.forceUpdate(),x(y)})}),destroy:function(){E(),O=!0}};if(!yv(p,m))return w;w.setOptions(h).then(function(x){!O&&h.onFirstUpdate&&h.onFirstUpdate(x)});function R(){y.orderedModifiers.forEach(function(x){var $=x.name,B=x.options,D=B===void 0?{}:B,k=x.effect;if(typeof k=="function"){var z=k({state:y,name:$,instance:w,options:D}),N=function(){};S.push(z||N)}})}function E(){S.forEach(function(x){return x()}),S=[]}return w}}var AR=[ZE,gR,WE,HE,mR,uR,bR,GE,fR],zR=wR({defaultModifiers:AR});function ob(n){const{elementType:r,externalSlotProps:l,ownerState:i,skipResolvingSlotProps:u=!1,...c}=n,d=u?{}:G0(l,i),{props:p,internalRef:m}=K0({...c,externalSlotProps:d}),h=fn(m,d?.ref,n.additionalProps?.ref);return Y0(r,{...p,ref:h},i)}function Gi(n){return parseInt(M.version,10)>=19?n?.props?.ref||null:n?.ref||null}function DR(n){return typeof n=="function"?n():n}const lb=M.forwardRef(function(r,l){const{children:i,container:u,disablePortal:c=!1}=r,[d,p]=M.useState(null),m=fn(M.isValidElement(i)?Gi(i):null,l);if(fa(()=>{c||p(DR(u)||document.body)},[u,c]),fa(()=>{if(d&&!c)return Ad(l,d),()=>{Ad(l,null)}},[l,d,c]),c){if(M.isValidElement(i)){const h={ref:m};return M.cloneElement(i,h)}return i}return d&&U0.createPortal(i,d)});function kR(n){return Xe("MuiPopper",n)}Ke("MuiPopper",["root"]);function $R(n,r){if(r==="ltr")return n;switch(n){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return n}}function _d(n){return typeof n=="function"?n():n}function jR(n){return n.nodeType!==void 0}const NR=n=>{const{classes:r}=n;return Qe({root:["root"]},kR,r)},BR={},_R=M.forwardRef(function(r,l){const{anchorEl:i,children:u,direction:c,disablePortal:d,modifiers:p,open:m,placement:h,popperOptions:y,popperRef:S,slotProps:O={},slots:w={},TransitionProps:R,ownerState:E,...x}=r,$=M.useRef(null),B=fn($,l),D=M.useRef(null),k=fn(D,S),z=M.useRef(k);fa(()=>{z.current=k},[k]),M.useImperativeHandle(S,()=>D.current,[]);const N=$R(h,c),[q,G]=M.useState(N),[K,v]=M.useState(_d(i));M.useEffect(()=>{D.current&&D.current.forceUpdate()}),M.useEffect(()=>{i&&v(_d(i))},[i]),fa(()=>{if(!K||!m)return;const L=Y=>{G(Y.placement)};let T=[{name:"preventOverflow",options:{altBoundary:d}},{name:"flip",options:{altBoundary:d}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:Y})=>{L(Y)}}];p!=null&&(T=T.concat(p)),y&&y.modifiers!=null&&(T=T.concat(y.modifiers));const H=zR(K,$.current,{placement:N,...y,modifiers:T});return z.current(H),()=>{H.destroy(),z.current(null)}},[K,d,p,m,y,N]);const _={placement:q};R!==null&&(_.TransitionProps=R);const V=NR(r),ae=w.root??"div",ee=ob({elementType:ae,externalSlotProps:O.root,externalForwardedProps:x,additionalProps:{role:"tooltip",ref:B},ownerState:r,className:V.root});return b.jsx(ae,{...ee,children:typeof u=="function"?u(_):u})}),LR=M.forwardRef(function(r,l){const{anchorEl:i,children:u,container:c,direction:d="ltr",disablePortal:p=!1,keepMounted:m=!1,modifiers:h,open:y,placement:S="bottom",popperOptions:O=BR,popperRef:w,style:R,transition:E=!1,slotProps:x={},slots:$={},...B}=r,[D,k]=M.useState(!0),z=()=>{k(!1)},N=()=>{k(!0)};if(!m&&!y&&(!E||D))return null;let q;if(c)q=c;else if(i){const v=_d(i);q=v&&jR(v)?qn(v).body:qn(null).body}const G=!y&&m&&(!E||D)?"none":void 0,K=E?{in:y,onEnter:z,onExited:N}:void 0;return b.jsx(lb,{disablePortal:p,container:q,children:b.jsx(_R,{anchorEl:i,direction:d,disablePortal:p,modifiers:h,ref:l,open:E?!D:y,placement:S,popperOptions:O,popperRef:w,slotProps:x,slots:$,...B,style:{position:"fixed",top:0,left:0,display:G,...R},TransitionProps:K,children:u})})}),HR=pe(LR,{name:"MuiPopper",slot:"Root"})({}),ib=M.forwardRef(function(r,l){const i=O0(),u=Je({props:r,name:"MuiPopper"}),{anchorEl:c,component:d,components:p,componentsProps:m,container:h,disablePortal:y,keepMounted:S,modifiers:O,open:w,placement:R,popperOptions:E,popperRef:x,transition:$,slots:B,slotProps:D,...k}=u,z=B?.root??p?.Root,N={anchorEl:c,container:h,disablePortal:y,keepMounted:S,modifiers:O,open:w,placement:R,popperOptions:E,popperRef:x,transition:$,...k};return b.jsx(HR,{as:d,direction:i?"rtl":"ltr",slots:{root:z},slotProps:D??m,...N,ref:l})});function PR(n){return Xe("MuiListSubheader",n)}Ke("MuiListSubheader",["root","colorPrimary","colorInherit","gutters","inset","sticky"]);const UR=n=>{const{classes:r,color:l,disableGutters:i,inset:u,disableSticky:c}=n,d={root:["root",l!=="default"&&`color${de(l)}`,!i&&"gutters",u&&"inset",!c&&"sticky"]};return Qe(d,PR,r)},IR=pe("li",{name:"MuiListSubheader",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.color!=="default"&&r[`color${de(l.color)}`],!l.disableGutters&&r.gutters,l.inset&&r.inset,!l.disableSticky&&r.sticky]}})(tt(({theme:n})=>({boxSizing:"border-box",lineHeight:"48px",listStyle:"none",color:(n.vars||n).palette.text.secondary,fontFamily:n.typography.fontFamily,fontWeight:n.typography.fontWeightMedium,fontSize:n.typography.pxToRem(14),variants:[{props:{color:"primary"},style:{color:(n.vars||n).palette.primary.main}},{props:{color:"inherit"},style:{color:"inherit"}},{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:r})=>r.inset,style:{paddingLeft:72}},{props:({ownerState:r})=>!r.disableSticky,style:{position:"sticky",top:0,zIndex:1,backgroundColor:(n.vars||n).palette.background.paper}}]}))),Ld=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiListSubheader"}),{className:u,color:c="default",component:d="li",disableGutters:p=!1,disableSticky:m=!1,inset:h=!1,...y}=i,S={...i,color:c,component:d,disableGutters:p,disableSticky:m,inset:h},O=UR(S);return b.jsx(IR,{as:d,className:Me(O.root,u),ref:l,ownerState:S,...y})});Ld&&(Ld.muiSkipListHighlight=!0);const qR=An(b.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}));function VR(n){return Xe("MuiChip",n)}const Ze=Ke("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),YR=n=>{const{classes:r,disabled:l,size:i,color:u,iconColor:c,onDelete:d,clickable:p,variant:m}=n,h={root:["root",m,l&&"disabled",`size${de(i)}`,`color${de(u)}`,p&&"clickable",p&&`clickableColor${de(u)}`,d&&"deletable",d&&`deletableColor${de(u)}`,`${m}${de(u)}`],label:["label",`label${de(i)}`],avatar:["avatar",`avatar${de(i)}`,`avatarColor${de(u)}`],icon:["icon",`icon${de(i)}`,`iconColor${de(c)}`],deleteIcon:["deleteIcon",`deleteIcon${de(i)}`,`deleteIconColor${de(u)}`,`deleteIcon${de(m)}Color${de(u)}`]};return Qe(h,VR,r)},GR=pe("div",{name:"MuiChip",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n,{color:i,iconColor:u,clickable:c,onDelete:d,size:p,variant:m}=l;return[{[`& .${Ze.avatar}`]:r.avatar},{[`& .${Ze.avatar}`]:r[`avatar${de(p)}`]},{[`& .${Ze.avatar}`]:r[`avatarColor${de(i)}`]},{[`& .${Ze.icon}`]:r.icon},{[`& .${Ze.icon}`]:r[`icon${de(p)}`]},{[`& .${Ze.icon}`]:r[`iconColor${de(u)}`]},{[`& .${Ze.deleteIcon}`]:r.deleteIcon},{[`& .${Ze.deleteIcon}`]:r[`deleteIcon${de(p)}`]},{[`& .${Ze.deleteIcon}`]:r[`deleteIconColor${de(i)}`]},{[`& .${Ze.deleteIcon}`]:r[`deleteIcon${de(m)}Color${de(i)}`]},r.root,r[`size${de(p)}`],r[`color${de(i)}`],c&&r.clickable,c&&i!=="default"&&r[`clickableColor${de(i)})`],d&&r.deletable,d&&i!=="default"&&r[`deletableColor${de(i)}`],r[m],r[`${m}${de(i)}`]]}})(tt(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[700]:n.palette.grey[300];return{maxWidth:"100%",fontFamily:n.typography.fontFamily,fontSize:n.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,lineHeight:1.5,color:(n.vars||n).palette.text.primary,backgroundColor:(n.vars||n).palette.action.selected,borderRadius:32/2,whiteSpace:"nowrap",transition:n.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${Ze.disabled}`]:{opacity:(n.vars||n).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${Ze.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:n.vars?n.vars.palette.Chip.defaultAvatarColor:r,fontSize:n.typography.pxToRem(12)},[`& .${Ze.avatarColorPrimary}`]:{color:(n.vars||n).palette.primary.contrastText,backgroundColor:(n.vars||n).palette.primary.dark},[`& .${Ze.avatarColorSecondary}`]:{color:(n.vars||n).palette.secondary.contrastText,backgroundColor:(n.vars||n).palette.secondary.dark},[`& .${Ze.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:n.typography.pxToRem(10)},[`& .${Ze.icon}`]:{marginLeft:5,marginRight:-6},[`& .${Ze.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:n.vars?`rgba(${n.vars.palette.text.primaryChannel} / 0.26)`:yt(n.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:n.vars?`rgba(${n.vars.palette.text.primaryChannel} / 0.4)`:yt(n.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${Ze.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${Ze.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(n.palette).filter(Sn(["contrastText"])).map(([l])=>({props:{color:l},style:{backgroundColor:(n.vars||n).palette[l].main,color:(n.vars||n).palette[l].contrastText,[`& .${Ze.deleteIcon}`]:{color:n.vars?`rgba(${n.vars.palette[l].contrastTextChannel} / 0.7)`:yt(n.palette[l].contrastText,.7),"&:hover, &:active":{color:(n.vars||n).palette[l].contrastText}}}})),{props:l=>l.iconColor===l.color,style:{[`& .${Ze.icon}`]:{color:n.vars?n.vars.palette.Chip.defaultIconColor:r}}},{props:l=>l.iconColor===l.color&&l.color!=="default",style:{[`& .${Ze.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${Ze.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:yt(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)}}},...Object.entries(n.palette).filter(Sn(["dark"])).map(([l])=>({props:{color:l,onDelete:!0},style:{[`&.${Ze.focusVisible}`]:{background:(n.vars||n).palette[l].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:yt(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity)},[`&.${Ze.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:yt(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)},"&:active":{boxShadow:(n.vars||n).shadows[1]}}},...Object.entries(n.palette).filter(Sn(["dark"])).map(([l])=>({props:{color:l,clickable:!0},style:{[`&:hover, &.${Ze.focusVisible}`]:{backgroundColor:(n.vars||n).palette[l].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:n.vars?`1px solid ${n.vars.palette.Chip.defaultBorder}`:`1px solid ${n.palette.mode==="light"?n.palette.grey[400]:n.palette.grey[700]}`,[`&.${Ze.clickable}:hover`]:{backgroundColor:(n.vars||n).palette.action.hover},[`&.${Ze.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},[`& .${Ze.avatar}`]:{marginLeft:4},[`& .${Ze.avatarSmall}`]:{marginLeft:2},[`& .${Ze.icon}`]:{marginLeft:4},[`& .${Ze.iconSmall}`]:{marginLeft:2},[`& .${Ze.deleteIcon}`]:{marginRight:5},[`& .${Ze.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(n.palette).filter(Sn()).map(([l])=>({props:{variant:"outlined",color:l},style:{color:(n.vars||n).palette[l].main,border:`1px solid ${n.vars?`rgba(${n.vars.palette[l].mainChannel} / 0.7)`:yt(n.palette[l].main,.7)}`,[`&.${Ze.clickable}:hover`]:{backgroundColor:n.vars?`rgba(${n.vars.palette[l].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette[l].main,n.palette.action.hoverOpacity)},[`&.${Ze.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette[l].mainChannel} / ${n.vars.palette.action.focusOpacity})`:yt(n.palette[l].main,n.palette.action.focusOpacity)},[`& .${Ze.deleteIcon}`]:{color:n.vars?`rgba(${n.vars.palette[l].mainChannel} / 0.7)`:yt(n.palette[l].main,.7),"&:hover, &:active":{color:(n.vars||n).palette[l].main}}}}))]}})),XR=pe("span",{name:"MuiChip",slot:"Label",overridesResolver:(n,r)=>{const{ownerState:l}=n,{size:i}=l;return[r.label,r[`label${de(i)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function vv(n){return n.key==="Backspace"||n.key==="Delete"}const Hd=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiChip"}),{avatar:u,className:c,clickable:d,color:p="default",component:m,deleteIcon:h,disabled:y=!1,icon:S,label:O,onClick:w,onDelete:R,onKeyDown:E,onKeyUp:x,size:$="medium",variant:B="filled",tabIndex:D,skipFocusWhenDisabled:k=!1,slots:z={},slotProps:N={},...q}=i,G=M.useRef(null),K=fn(G,l),v=le=>{le.stopPropagation(),R&&R(le)},_=le=>{le.currentTarget===le.target&&vv(le)&&le.preventDefault(),E&&E(le)},V=le=>{le.currentTarget===le.target&&R&&vv(le)&&R(le),x&&x(le)},ae=d!==!1&&w?!0:d,ee=ae||R?$i:m||"div",L={...i,component:ee,disabled:y,size:$,color:p,iconColor:M.isValidElement(S)&&S.props.color||p,onDelete:!!R,clickable:ae,variant:B},T=YR(L),H=ee===$i?{component:m||"div",focusVisibleClassName:T.focusVisible,...R&&{disableRipple:!0}}:{};let Y=null;R&&(Y=h&&M.isValidElement(h)?M.cloneElement(h,{className:Me(h.props.className,T.deleteIcon),onClick:v}):b.jsx(qR,{className:T.deleteIcon,onClick:v}));let X=null;u&&M.isValidElement(u)&&(X=M.cloneElement(u,{className:Me(T.avatar,u.props.className)}));let A=null;S&&M.isValidElement(S)&&(A=M.cloneElement(S,{className:Me(T.icon,S.props.className)}));const U={slots:z,slotProps:N},[J,ne]=wt("root",{elementType:GR,externalForwardedProps:{...U,...q},ownerState:L,shouldForwardComponentProp:!0,ref:K,className:Me(T.root,c),additionalProps:{disabled:ae&&y?!0:void 0,tabIndex:k&&y?-1:D,...H},getSlotProps:le=>({...le,onClick:ve=>{le.onClick?.(ve),w?.(ve)},onKeyDown:ve=>{le.onKeyDown?.(ve),_?.(ve)},onKeyUp:ve=>{le.onKeyUp?.(ve),V?.(ve)}})}),[fe,ue]=wt("label",{elementType:XR,externalForwardedProps:U,ownerState:L,className:T.label});return b.jsxs(J,{as:ee,...ne,children:[X||A,b.jsx(fe,{...ue,children:O}),Y]})});function iu(n){return parseInt(n,10)||0}const KR={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function QR(n){for(const r in n)return!1;return!0}function bv(n){return QR(n)||n.outerHeightStyle===0&&!n.overflowing}const WR=M.forwardRef(function(r,l){const{onChange:i,maxRows:u,minRows:c=1,style:d,value:p,...m}=r,{current:h}=M.useRef(p!=null),y=M.useRef(null),S=fn(l,y),O=M.useRef(null),w=M.useRef(null),R=M.useCallback(()=>{const D=y.current,k=w.current;if(!D||!k)return;const N=ir(D).getComputedStyle(D);if(N.width==="0px")return{outerHeightStyle:0,overflowing:!1};k.style.width=N.width,k.value=D.value||r.placeholder||"x",k.value.slice(-1)===`
`&&(k.value+=" ");const q=N.boxSizing,G=iu(N.paddingBottom)+iu(N.paddingTop),K=iu(N.borderBottomWidth)+iu(N.borderTopWidth),v=k.scrollHeight;k.value="x";const _=k.scrollHeight;let V=v;c&&(V=Math.max(Number(c)*_,V)),u&&(V=Math.min(Number(u)*_,V)),V=Math.max(V,_);const ae=V+(q==="border-box"?G+K:0),ee=Math.abs(V-v)<=1;return{outerHeightStyle:ae,overflowing:ee}},[u,c,r.placeholder]),E=Ca(()=>{const D=y.current,k=R();if(!D||!k||bv(k))return!1;const z=k.outerHeightStyle;return O.current!=null&&O.current!==z}),x=M.useCallback(()=>{const D=y.current,k=R();if(!D||!k||bv(k))return;const z=k.outerHeightStyle;O.current!==z&&(O.current=z,D.style.height=`${z}px`),D.style.overflow=k.overflowing?"hidden":""},[R]),$=M.useRef(-1);fa(()=>{const D=_0(x),k=y?.current;if(!k)return;const z=ir(k);z.addEventListener("resize",D);let N;return typeof ResizeObserver<"u"&&(N=new ResizeObserver(()=>{E()&&(N.unobserve(k),cancelAnimationFrame($.current),x(),$.current=requestAnimationFrame(()=>{N.observe(k)}))}),N.observe(k)),()=>{D.clear(),cancelAnimationFrame($.current),z.removeEventListener("resize",D),N&&N.disconnect()}},[R,x,E]),fa(()=>{x()});const B=D=>{h||x();const k=D.target,z=k.value.length,N=k.value.endsWith(`
`),q=k.selectionStart===z;N&&q&&k.setSelectionRange(z,z),i&&i(D)};return b.jsxs(M.Fragment,{children:[b.jsx("textarea",{value:p,onChange:B,ref:S,rows:c,style:d,...m}),b.jsx("textarea",{"aria-hidden":!0,className:r.className,readOnly:!0,ref:w,tabIndex:-1,style:{...KR.shadow,...d,paddingTop:0,paddingBottom:0}})]})});function Pd(n){return typeof n=="string"}function fo({props:n,states:r,muiFormControl:l}){return r.reduce((i,u)=>(i[u]=n[u],l&&typeof n[u]>"u"&&(i[u]=l[u]),i),{})}const Tp=M.createContext(void 0);function Hr(){return M.useContext(Tp)}function Sv(n){return n!=null&&!(Array.isArray(n)&&n.length===0)}function Mu(n,r=!1){return n&&(Sv(n.value)&&n.value!==""||r&&Sv(n.defaultValue)&&n.defaultValue!=="")}function FR(n){return n.startAdornment}function ZR(n){return Xe("MuiInputBase",n)}const Hn=Ke("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]);var xv;const Xu=(n,r)=>{const{ownerState:l}=n;return[r.root,l.formControl&&r.formControl,l.startAdornment&&r.adornedStart,l.endAdornment&&r.adornedEnd,l.error&&r.error,l.size==="small"&&r.sizeSmall,l.multiline&&r.multiline,l.color&&r[`color${de(l.color)}`],l.fullWidth&&r.fullWidth,l.hiddenLabel&&r.hiddenLabel]},Ku=(n,r)=>{const{ownerState:l}=n;return[r.input,l.size==="small"&&r.inputSizeSmall,l.multiline&&r.inputMultiline,l.type==="search"&&r.inputTypeSearch,l.startAdornment&&r.inputAdornedStart,l.endAdornment&&r.inputAdornedEnd,l.hiddenLabel&&r.inputHiddenLabel]},JR=n=>{const{classes:r,color:l,disabled:i,error:u,endAdornment:c,focused:d,formControl:p,fullWidth:m,hiddenLabel:h,multiline:y,readOnly:S,size:O,startAdornment:w,type:R}=n,E={root:["root",`color${de(l)}`,i&&"disabled",u&&"error",m&&"fullWidth",d&&"focused",p&&"formControl",O&&O!=="medium"&&`size${de(O)}`,y&&"multiline",w&&"adornedStart",c&&"adornedEnd",h&&"hiddenLabel",S&&"readOnly"],input:["input",i&&"disabled",R==="search"&&"inputTypeSearch",y&&"inputMultiline",O==="small"&&"inputSizeSmall",h&&"inputHiddenLabel",w&&"inputAdornedStart",c&&"inputAdornedEnd",S&&"readOnly"]};return Qe(E,ZR,r)},Qu=pe("div",{name:"MuiInputBase",slot:"Root",overridesResolver:Xu})(tt(({theme:n})=>({...n.typography.body1,color:(n.vars||n).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${Hn.disabled}`]:{color:(n.vars||n).palette.text.disabled,cursor:"default"},variants:[{props:({ownerState:r})=>r.multiline,style:{padding:"4px 0 5px"}},{props:({ownerState:r,size:l})=>r.multiline&&l==="small",style:{paddingTop:1}},{props:({ownerState:r})=>r.fullWidth,style:{width:"100%"}}]}))),Wu=pe("input",{name:"MuiInputBase",slot:"Input",overridesResolver:Ku})(tt(({theme:n})=>{const r=n.palette.mode==="light",l={color:"currentColor",...n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5},transition:n.transitions.create("opacity",{duration:n.transitions.duration.shorter})},i={opacity:"0 !important"},u=n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":l,"&::-moz-placeholder":l,"&::-ms-input-placeholder":l,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${Hn.formControl} &`]:{"&::-webkit-input-placeholder":i,"&::-moz-placeholder":i,"&::-ms-input-placeholder":i,"&:focus::-webkit-input-placeholder":u,"&:focus::-moz-placeholder":u,"&:focus::-ms-input-placeholder":u},[`&.${Hn.disabled}`]:{opacity:1,WebkitTextFillColor:(n.vars||n).palette.text.disabled},variants:[{props:({ownerState:c})=>!c.disableInjectingGlobalStyles,style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:({ownerState:c})=>c.multiline,style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}})),Cv=fp({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),Ep=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiInputBase"}),{"aria-describedby":u,autoComplete:c,autoFocus:d,className:p,color:m,components:h={},componentsProps:y={},defaultValue:S,disabled:O,disableInjectingGlobalStyles:w,endAdornment:R,error:E,fullWidth:x=!1,id:$,inputComponent:B="input",inputProps:D={},inputRef:k,margin:z,maxRows:N,minRows:q,multiline:G=!1,name:K,onBlur:v,onChange:_,onClick:V,onFocus:ae,onKeyDown:ee,onKeyUp:L,placeholder:T,readOnly:H,renderSuffix:Y,rows:X,size:A,slotProps:U={},slots:J={},startAdornment:ne,type:fe="text",value:ue,...le}=i,ve=D.value!=null?D.value:ue,{current:xe}=M.useRef(ve!=null),be=M.useRef(),me=M.useCallback(Ae=>{},[]),Re=fn(be,k,D.ref,me),[Ne,_e]=M.useState(!1),ke=Hr(),nt=fo({props:i,muiFormControl:ke,states:["color","disabled","error","hiddenLabel","size","required","filled"]});nt.focused=ke?ke.focused:Ne,M.useEffect(()=>{!ke&&O&&Ne&&(_e(!1),v&&v())},[ke,O,Ne,v]);const Ve=ke&&ke.onFilled,at=ke&&ke.onEmpty,he=M.useCallback(Ae=>{Mu(Ae)?Ve&&Ve():at&&at()},[Ve,at]);fa(()=>{xe&&he({value:ve})},[ve,he,xe]);const st=Ae=>{ae&&ae(Ae),D.onFocus&&D.onFocus(Ae),ke&&ke.onFocus?ke.onFocus(Ae):_e(!0)},Te=Ae=>{v&&v(Ae),D.onBlur&&D.onBlur(Ae),ke&&ke.onBlur?ke.onBlur(Ae):_e(!1)},Le=(Ae,...Gt)=>{if(!xe){const xn=Ae.target||be.current;if(xn==null)throw new Error(or(1));he({value:xn.value})}D.onChange&&D.onChange(Ae,...Gt),_&&_(Ae,...Gt)};M.useEffect(()=>{he(be.current)},[]);const ye=Ae=>{be.current&&Ae.currentTarget===Ae.target&&be.current.focus(),V&&V(Ae)};let It=B,ut=D;G&&It==="input"&&(X?ut={type:void 0,minRows:X,maxRows:X,...ut}:ut={type:void 0,maxRows:N,minRows:q,...ut},It=WR);const At=Ae=>{he(Ae.animationName==="mui-auto-fill-cancel"?be.current:{value:"x"})};M.useEffect(()=>{ke&&ke.setAdornedStart(!!ne)},[ke,ne]);const rt={...i,color:nt.color||"primary",disabled:nt.disabled,endAdornment:R,error:nt.error,focused:nt.focused,formControl:ke,fullWidth:x,hiddenLabel:nt.hiddenLabel,multiline:G,size:nt.size,startAdornment:ne,type:fe},je=JR(rt),Ye=J.root||h.Root||Qu,et=U.root||y.root||{},vt=J.input||h.Input||Wu;return ut={...ut,...U.input??y.input},b.jsxs(M.Fragment,{children:[!w&&typeof Cv=="function"&&(xv||(xv=b.jsx(Cv,{}))),b.jsxs(Ye,{...et,ref:l,onClick:ye,...le,...!Pd(Ye)&&{ownerState:{...rt,...et.ownerState}},className:Me(je.root,et.className,p,H&&"MuiInputBase-readOnly"),children:[ne,b.jsx(Tp.Provider,{value:null,children:b.jsx(vt,{"aria-invalid":nt.error,"aria-describedby":u,autoComplete:c,autoFocus:d,defaultValue:S,disabled:nt.disabled,id:$,onAnimationStart:At,name:K,placeholder:T,readOnly:H,required:nt.required,rows:X,value:ve,onKeyDown:ee,onKeyUp:L,type:fe,...ut,...!Pd(vt)&&{as:It,ownerState:{...rt,...ut.ownerState}},ref:Re,className:Me(je.input,ut.className,H&&"MuiInputBase-readOnly"),onBlur:Te,onChange:Le,onFocus:st})}),R,Y?Y({...nt,startAdornment:ne}):null]})]})});function eO(n){return Xe("MuiInput",n)}const jr={...Hn,...Ke("MuiInput",["root","underline","input"])};function tO(n){return Xe("MuiOutlinedInput",n)}const ia={...Hn,...Ke("MuiOutlinedInput",["root","notchedOutline","input"])};function nO(n){return Xe("MuiFilledInput",n)}const Pn={...Hn,...Ke("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])},sb=An(b.jsx("path",{d:"M7 10l5 5 5-5z"}));function aO(n){return Xe("MuiAutocomplete",n)}const Ue=Ke("MuiAutocomplete",["root","expanded","fullWidth","focused","focusVisible","tag","tagSizeSmall","tagSizeMedium","hasPopupIcon","hasClearIcon","inputRoot","input","inputFocused","endAdornment","clearIndicator","popupIndicator","popupIndicatorOpen","popper","popperDisablePortal","paper","listbox","loading","noOptions","option","groupLabel","groupUl"]);var Tv,Ev;const rO=n=>{const{classes:r,disablePortal:l,expanded:i,focused:u,fullWidth:c,hasClearIcon:d,hasPopupIcon:p,inputFocused:m,popupOpen:h,size:y}=n,S={root:["root",i&&"expanded",u&&"focused",c&&"fullWidth",d&&"hasClearIcon",p&&"hasPopupIcon"],inputRoot:["inputRoot"],input:["input",m&&"inputFocused"],tag:["tag",`tagSize${de(y)}`],endAdornment:["endAdornment"],clearIndicator:["clearIndicator"],popupIndicator:["popupIndicator",h&&"popupIndicatorOpen"],popper:["popper",l&&"popperDisablePortal"],paper:["paper"],listbox:["listbox"],loading:["loading"],noOptions:["noOptions"],option:["option"],groupLabel:["groupLabel"],groupUl:["groupUl"]};return Qe(S,aO,r)},oO=pe("div",{name:"MuiAutocomplete",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n,{fullWidth:i,hasClearIcon:u,hasPopupIcon:c,inputFocused:d,size:p}=l;return[{[`& .${Ue.tag}`]:r.tag},{[`& .${Ue.tag}`]:r[`tagSize${de(p)}`]},{[`& .${Ue.inputRoot}`]:r.inputRoot},{[`& .${Ue.input}`]:r.input},{[`& .${Ue.input}`]:d&&r.inputFocused},r.root,i&&r.fullWidth,c&&r.hasPopupIcon,u&&r.hasClearIcon]}})({[`&.${Ue.focused} .${Ue.clearIndicator}`]:{visibility:"visible"},"@media (pointer: fine)":{[`&:hover .${Ue.clearIndicator}`]:{visibility:"visible"}},[`& .${Ue.tag}`]:{margin:3,maxWidth:"calc(100% - 6px)"},[`& .${Ue.inputRoot}`]:{[`.${Ue.hasPopupIcon}&, .${Ue.hasClearIcon}&`]:{paddingRight:30},[`.${Ue.hasPopupIcon}.${Ue.hasClearIcon}&`]:{paddingRight:56},[`& .${Ue.input}`]:{width:0,minWidth:30}},[`& .${jr.root}`]:{paddingBottom:1,"& .MuiInput-input":{padding:"4px 4px 4px 0px"}},[`& .${jr.root}.${Hn.sizeSmall}`]:{[`& .${jr.input}`]:{padding:"2px 4px 3px 0"}},[`& .${ia.root}`]:{padding:9,[`.${Ue.hasPopupIcon}&, .${Ue.hasClearIcon}&`]:{paddingRight:39},[`.${Ue.hasPopupIcon}.${Ue.hasClearIcon}&`]:{paddingRight:65},[`& .${Ue.input}`]:{padding:"7.5px 4px 7.5px 5px"},[`& .${Ue.endAdornment}`]:{right:9}},[`& .${ia.root}.${Hn.sizeSmall}`]:{paddingTop:6,paddingBottom:6,paddingLeft:6,[`& .${Ue.input}`]:{padding:"2.5px 4px 2.5px 8px"}},[`& .${Pn.root}`]:{paddingTop:19,paddingLeft:8,[`.${Ue.hasPopupIcon}&, .${Ue.hasClearIcon}&`]:{paddingRight:39},[`.${Ue.hasPopupIcon}.${Ue.hasClearIcon}&`]:{paddingRight:65},[`& .${Pn.input}`]:{padding:"7px 4px"},[`& .${Ue.endAdornment}`]:{right:9}},[`& .${Pn.root}.${Hn.sizeSmall}`]:{paddingBottom:1,[`& .${Pn.input}`]:{padding:"2.5px 4px"}},[`& .${Hn.hiddenLabel}`]:{paddingTop:8},[`& .${Pn.root}.${Hn.hiddenLabel}`]:{paddingTop:0,paddingBottom:0,[`& .${Ue.input}`]:{paddingTop:16,paddingBottom:17}},[`& .${Pn.root}.${Hn.hiddenLabel}.${Hn.sizeSmall}`]:{[`& .${Ue.input}`]:{paddingTop:8,paddingBottom:9}},[`& .${Ue.input}`]:{flexGrow:1,textOverflow:"ellipsis",opacity:0},variants:[{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{[`& .${Ue.tag}`]:{margin:2,maxWidth:"calc(100% - 4px)"}}},{props:{inputFocused:!0},style:{[`& .${Ue.input}`]:{opacity:1}}},{props:{multiple:!0},style:{[`& .${Ue.inputRoot}`]:{flexWrap:"wrap"}}}]}),lO=pe("div",{name:"MuiAutocomplete",slot:"EndAdornment"})({position:"absolute",right:0,top:"50%",transform:"translate(0, -50%)"}),iO=pe(hp,{name:"MuiAutocomplete",slot:"ClearIndicator"})({marginRight:-2,padding:4,visibility:"hidden"}),sO=pe(hp,{name:"MuiAutocomplete",slot:"PopupIndicator",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.popupIndicator,l.popupOpen&&r.popupIndicatorOpen]}})({padding:2,marginRight:-2,variants:[{props:{popupOpen:!0},style:{transform:"rotate(180deg)"}}]}),uO=pe(ib,{name:"MuiAutocomplete",slot:"Popper",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[{[`& .${Ue.option}`]:r.option},r.popper,l.disablePortal&&r.popperDisablePortal]}})(tt(({theme:n})=>({zIndex:(n.vars||n).zIndex.modal,variants:[{props:{disablePortal:!0},style:{position:"absolute"}}]}))),cO=pe(un,{name:"MuiAutocomplete",slot:"Paper"})(tt(({theme:n})=>({...n.typography.body1,overflow:"auto"}))),fO=pe("div",{name:"MuiAutocomplete",slot:"Loading"})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,padding:"14px 16px"}))),dO=pe("div",{name:"MuiAutocomplete",slot:"NoOptions"})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,padding:"14px 16px"}))),pO=pe("ul",{name:"MuiAutocomplete",slot:"Listbox"})(tt(({theme:n})=>({listStyle:"none",margin:0,padding:"8px 0",maxHeight:"40vh",overflow:"auto",position:"relative",[`& .${Ue.option}`]:{minHeight:48,display:"flex",overflow:"hidden",justifyContent:"flex-start",alignItems:"center",cursor:"pointer",paddingTop:6,boxSizing:"border-box",outline:"0",WebkitTapHighlightColor:"transparent",paddingBottom:6,paddingLeft:16,paddingRight:16,[n.breakpoints.up("sm")]:{minHeight:"auto"},[`&.${Ue.focused}`]:{backgroundColor:(n.vars||n).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},'&[aria-disabled="true"]':{opacity:(n.vars||n).palette.action.disabledOpacity,pointerEvents:"none"},[`&.${Ue.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},'&[aria-selected="true"]':{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:yt(n.palette.primary.main,n.palette.action.selectedOpacity),[`&.${Ue.focused}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:yt(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:(n.vars||n).palette.action.selected}},[`&.${Ue.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:yt(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)}}}}))),mO=pe(Ld,{name:"MuiAutocomplete",slot:"GroupLabel"})(tt(({theme:n})=>({backgroundColor:(n.vars||n).palette.background.paper,top:-8}))),hO=pe("ul",{name:"MuiAutocomplete",slot:"GroupUl"})({padding:0,[`& .${Ue.option}`]:{paddingLeft:24}}),nl=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiAutocomplete"}),{autoComplete:u=!1,autoHighlight:c=!1,autoSelect:d=!1,blurOnSelect:p=!1,ChipProps:m,className:h,clearIcon:y=Tv||(Tv=b.jsx(pE,{fontSize:"small"})),clearOnBlur:S=!i.freeSolo,clearOnEscape:O=!1,clearText:w="Clear",closeText:R="Close",componentsProps:E,defaultValue:x=i.multiple?[]:null,disableClearable:$=!1,disableCloseOnSelect:B=!1,disabled:D=!1,disabledItemsFocusable:k=!1,disableListWrap:z=!1,disablePortal:N=!1,filterOptions:q,filterSelectedOptions:G=!1,forcePopupIcon:K="auto",freeSolo:v=!1,fullWidth:_=!1,getLimitTagsText:V=lt=>`+${lt}`,getOptionDisabled:ae,getOptionKey:ee,getOptionLabel:L,isOptionEqualToValue:T,groupBy:H,handleHomeEndKeys:Y=!i.freeSolo,id:X,includeInputInList:A=!1,inputValue:U,limitTags:J=-1,ListboxComponent:ne,ListboxProps:fe,loading:ue=!1,loadingText:le="Loading…",multiple:ve=!1,noOptionsText:xe="No options",onChange:be,onClose:me,onHighlightChange:Re,onInputChange:Ne,onOpen:_e,open:ke,openOnFocus:nt=!1,openText:Ve="Open",options:at,PaperComponent:he,PopperComponent:st,popupIcon:Te=Ev||(Ev=b.jsx(sb,{})),readOnly:Le=!1,renderGroup:ye,renderInput:It,renderOption:ut,renderTags:At,renderValue:rt,selectOnFocus:je=!i.freeSolo,size:Ye="medium",slots:et={},slotProps:vt={},value:Ae,...Gt}=i,{getRootProps:xn,getInputProps:Zt,getInputLabelProps:Ce,getPopupIndicatorProps:Pe,getClearProps:ot,getItemProps:Gn,getListboxProps:ha,getOptionProps:po,value:tn,dirty:zn,expanded:ga,id:Ua,popupOpen:Dn,focused:ur,focusedItem:cr,anchorEl:Ea,setAnchorEl:gl,inputValue:Xn,groupedOptions:zt}=EE({...i,componentName:"Autocomplete"}),Xt=!$&&!D&&zn&&!Le,Kn=(!v||K===!0)&&K!==!1,{onMouseDown:Pr}=Zt(),{ref:yl,...mo}=ha(),Qn=L||(lt=>lt.label??lt),Bt={...i,disablePortal:N,expanded:ga,focused:ur,fullWidth:_,getOptionLabel:Qn,hasClearIcon:Xt,hasPopupIcon:Kn,inputFocused:cr===-1,popupOpen:Dn,size:Ye},Mt=rO(Bt),gn={slots:{paper:he,popper:st,...et},slotProps:{chip:m,listbox:fe,...E,...vt}},[ya,va]=wt("listbox",{elementType:pO,externalForwardedProps:gn,ownerState:Bt,className:Mt.listbox,additionalProps:mo,ref:yl}),[te,re]=wt("paper",{elementType:un,externalForwardedProps:gn,ownerState:Bt,className:Mt.paper}),[ge,Be]=wt("popper",{elementType:ib,externalForwardedProps:gn,ownerState:Bt,className:Mt.popper,additionalProps:{disablePortal:N,style:{width:Ea?Ea.clientWidth:null},role:"presentation",anchorEl:Ea,open:Dn}});let Ee;const Dt=lt=>({className:Mt.tag,disabled:D,...Gn(lt)});if(ve?tn.length>0&&(At?Ee=At(tn,Dt,Bt):rt?Ee=rt(tn,Dt,Bt):Ee=tn.map((lt,vn)=>{const{key:Fn,...Ia}=Dt({index:vn});return b.jsx(Hd,{label:Qn(lt),size:Ye,...Ia,...gn.slotProps.chip},Fn)})):rt&&tn!=null&&(Ee=rt(tn,Dt,Bt)),J>-1&&Array.isArray(Ee)){const lt=Ee.length-J;!ur&&lt>0&&(Ee=Ee.splice(0,J),Ee.push(b.jsx("span",{className:Mt.tag,children:V(lt)},Ee.length)))}const yn=ye||(lt=>b.jsxs("li",{children:[b.jsx(mO,{className:Mt.groupLabel,ownerState:Bt,component:"div",children:lt.group}),b.jsx(hO,{className:Mt.groupUl,ownerState:Bt,children:lt.children})]},lt.key)),ho=ut||((lt,vn)=>{const{key:Fn,...Ia}=lt;return b.jsx("li",{...Ia,children:Qn(vn)},Fn)}),Ur=(lt,vn)=>{const Fn=po({option:lt,index:vn});return ho({...Fn,className:Mt.option},lt,{selected:Fn["aria-selected"],index:vn,inputValue:Xn},Bt)},Wn=gn.slotProps.clearIndicator,go=gn.slotProps.popupIndicator;return b.jsxs(M.Fragment,{children:[b.jsx(oO,{ref:l,className:Me(Mt.root,h),ownerState:Bt,...xn(Gt),children:It({id:Ua,disabled:D,fullWidth:!0,size:Ye==="small"?"small":void 0,InputLabelProps:Ce(),InputProps:{ref:gl,className:Mt.inputRoot,startAdornment:Ee,onMouseDown:lt=>{lt.target===lt.currentTarget&&Pr(lt)},...(Xt||Kn)&&{endAdornment:b.jsxs(lO,{className:Mt.endAdornment,ownerState:Bt,children:[Xt?b.jsx(iO,{...ot(),"aria-label":w,title:w,ownerState:Bt,...Wn,className:Me(Mt.clearIndicator,Wn?.className),children:y}):null,Kn?b.jsx(sO,{...Pe(),disabled:D,"aria-label":Dn?R:Ve,title:Dn?R:Ve,ownerState:Bt,...go,className:Me(Mt.popupIndicator,go?.className),children:Te}):null]})}},inputProps:{className:Mt.input,disabled:D,readOnly:Le,...Zt()}})}),Ea?b.jsx(uO,{as:ge,...Be,children:b.jsxs(cO,{as:te,...re,children:[ue&&zt.length===0?b.jsx(fO,{className:Mt.loading,ownerState:Bt,children:le}):null,zt.length===0&&!v&&!ue?b.jsx(dO,{className:Mt.noOptions,ownerState:Bt,role:"presentation",onMouseDown:lt=>{lt.preventDefault()},children:xe}):null,zt.length>0?b.jsx(ya,{as:ne,...va,children:zt.map((lt,vn)=>H?yn({key:lt.key,group:lt.group,children:lt.options.map((Fn,Ia)=>Ur(Fn,lt.index+Ia))}):Ur(lt,vn))}):null]})}):null]})}),gO={entering:{opacity:1},entered:{opacity:1}},yO=M.forwardRef(function(r,l){const i=Yu(),u={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{addEndListener:c,appear:d=!0,children:p,easing:m,in:h,onEnter:y,onEntered:S,onEntering:O,onExit:w,onExited:R,onExiting:E,style:x,timeout:$=u,TransitionComponent:B=Pa,...D}=r,k=M.useRef(null),z=fn(k,Gi(p),l),N=ee=>L=>{if(ee){const T=k.current;L===void 0?ee(T):ee(T,L)}},q=N(O),G=N((ee,L)=>{V0(ee);const T=Eu({style:x,timeout:$,easing:m},{mode:"enter"});ee.style.webkitTransition=i.transitions.create("opacity",T),ee.style.transition=i.transitions.create("opacity",T),y&&y(ee,L)}),K=N(S),v=N(E),_=N(ee=>{const L=Eu({style:x,timeout:$,easing:m},{mode:"exit"});ee.style.webkitTransition=i.transitions.create("opacity",L),ee.style.transition=i.transitions.create("opacity",L),w&&w(ee)}),V=N(R),ae=ee=>{c&&c(k.current,ee)};return b.jsx(B,{appear:d,in:h,nodeRef:k,onEnter:G,onEntered:K,onEntering:q,onExit:_,onExited:V,onExiting:v,addEndListener:ae,timeout:$,...D,children:(ee,{ownerState:L,...T})=>M.cloneElement(p,{style:{opacity:0,visibility:ee==="exited"&&!h?"hidden":void 0,...gO[ee],...x,...p.props.style},ref:z,...T})})});function vO(n){return Xe("MuiBackdrop",n)}Ke("MuiBackdrop",["root","invisible"]);const bO=n=>{const{classes:r,invisible:l}=n;return Qe({root:["root",l&&"invisible"]},vO,r)},SO=pe("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.invisible&&r.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),xO=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiBackdrop"}),{children:u,className:c,component:d="div",invisible:p=!1,open:m,components:h={},componentsProps:y={},slotProps:S={},slots:O={},TransitionComponent:w,transitionDuration:R,...E}=i,x={...i,component:d,invisible:p},$=bO(x),B={transition:w,root:h.Root,...O},D={...y,...S},k={component:d,slots:B,slotProps:D},[z,N]=wt("root",{elementType:SO,externalForwardedProps:k,className:Me($.root,c),ownerState:x}),[q,G]=wt("transition",{elementType:yO,externalForwardedProps:k,ownerState:x});return b.jsx(q,{in:m,timeout:R,...E,...G,children:b.jsx(z,{"aria-hidden":!0,...N,classes:$,ref:l,children:u})})}),CO=Ke("MuiBox",["root"]),TO=Vu(),Ht=B2({themeId:Ta,defaultTheme:TO,defaultClassName:CO.root,generateClassName:h0.generate});function EO(n){return Xe("MuiButton",n)}const ro=Ke("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),RO=M.createContext({}),OO=M.createContext(void 0),MO=n=>{const{color:r,disableElevation:l,fullWidth:i,size:u,variant:c,loading:d,loadingPosition:p,classes:m}=n,h={root:["root",d&&"loading",c,`${c}${de(r)}`,`size${de(u)}`,`${c}Size${de(u)}`,`color${de(r)}`,l&&"disableElevation",i&&"fullWidth",d&&`loadingPosition${de(p)}`],startIcon:["icon","startIcon",`iconSize${de(u)}`],endIcon:["icon","endIcon",`iconSize${de(u)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},y=Qe(h,EO,m);return{...m,...y}},ub=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],wO=pe($i,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[l.variant],r[`${l.variant}${de(l.color)}`],r[`size${de(l.size)}`],r[`${l.variant}Size${de(l.size)}`],l.color==="inherit"&&r.colorInherit,l.disableElevation&&r.disableElevation,l.fullWidth&&r.fullWidth,l.loading&&r.loading]}})(tt(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[300]:n.palette.grey[800],l=n.palette.mode==="light"?n.palette.grey.A100:n.palette.grey[700];return{...n.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create(["background-color","box-shadow","border-color","color"],{duration:n.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${ro.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(n.vars||n).shadows[2],"&:hover":{boxShadow:(n.vars||n).shadows[4],"@media (hover: none)":{boxShadow:(n.vars||n).shadows[2]}},"&:active":{boxShadow:(n.vars||n).shadows[8]},[`&.${ro.focusVisible}`]:{boxShadow:(n.vars||n).shadows[6]},[`&.${ro.disabled}`]:{color:(n.vars||n).palette.action.disabled,boxShadow:(n.vars||n).shadows[0],backgroundColor:(n.vars||n).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${ro.disabled}`]:{border:`1px solid ${(n.vars||n).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(n.palette).filter(Sn()).map(([i])=>({props:{color:i},style:{"--variant-textColor":(n.vars||n).palette[i].main,"--variant-outlinedColor":(n.vars||n).palette[i].main,"--variant-outlinedBorder":n.vars?`rgba(${n.vars.palette[i].mainChannel} / 0.5)`:yt(n.palette[i].main,.5),"--variant-containedColor":(n.vars||n).palette[i].contrastText,"--variant-containedBg":(n.vars||n).palette[i].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(n.vars||n).palette[i].dark,"--variant-textBg":n.vars?`rgba(${n.vars.palette[i].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette[i].main,n.palette.action.hoverOpacity),"--variant-outlinedBorder":(n.vars||n).palette[i].main,"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette[i].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette[i].main,n.palette.action.hoverOpacity)}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedBg:r,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedHoverBg:l,"--variant-textBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette.text.primary,n.palette.action.hoverOpacity),"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette.text.primary,n.palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:n.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${ro.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${ro.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:n.transitions.create(["background-color","box-shadow","border-color"],{duration:n.transitions.duration.short}),[`&.${ro.loading}`]:{color:"transparent"}}}]}})),AO=pe("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.startIcon,l.loading&&r.startIconLoadingStart,r[`iconSize${de(l.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...ub]})),zO=pe("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.endIcon,l.loading&&r.endIconLoadingEnd,r[`iconSize${de(l.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...ub]})),DO=pe("span",{name:"MuiButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(n.vars||n).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]})),Rv=pe("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),Ov=M.forwardRef(function(r,l){const i=M.useContext(RO),u=M.useContext(OO),c=ki(i,r),d=Je({props:c,name:"MuiButton"}),{children:p,color:m="primary",component:h="button",className:y,disabled:S=!1,disableElevation:O=!1,disableFocusRipple:w=!1,endIcon:R,focusVisibleClassName:E,fullWidth:x=!1,id:$,loading:B=null,loadingIndicator:D,loadingPosition:k="center",size:z="medium",startIcon:N,type:q,variant:G="text",...K}=d,v=ml($),_=D??b.jsx(Q0,{"aria-labelledby":v,color:"inherit",size:16}),V={...d,color:m,component:h,disabled:S,disableElevation:O,disableFocusRipple:w,fullWidth:x,loading:B,loadingIndicator:_,loadingPosition:k,size:z,type:q,variant:G},ae=MO(V),ee=(N||B&&k==="start")&&b.jsx(AO,{className:ae.startIcon,ownerState:V,children:N||b.jsx(Rv,{className:ae.loadingIconPlaceholder,ownerState:V})}),L=(R||B&&k==="end")&&b.jsx(zO,{className:ae.endIcon,ownerState:V,children:R||b.jsx(Rv,{className:ae.loadingIconPlaceholder,ownerState:V})}),T=u||"",H=typeof B=="boolean"?b.jsx("span",{className:ae.loadingWrapper,style:{display:"contents"},children:B&&b.jsx(DO,{className:ae.loadingIndicator,ownerState:V,children:_})}):null;return b.jsxs(wO,{ownerState:V,className:Me(i.className,ae.root,y,T),component:h,disabled:S||B,focusRipple:!w,focusVisibleClassName:Me(ae.focusVisible,E),ref:l,type:q,id:B?v:$,...K,classes:ae,children:[ee,k!=="end"&&H,p,k==="end"&&H,L]})});function kO(n){return Xe("PrivateSwitchBase",n)}Ke("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const $O=n=>{const{classes:r,checked:l,disabled:i,edge:u}=n,c={root:["root",l&&"checked",i&&"disabled",u&&`edge${de(u)}`],input:["input"]};return Qe(c,kO,r)},jO=pe($i,{name:"MuiSwitchBase"})({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:n,ownerState:r})=>n==="start"&&r.size!=="small",style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:n,ownerState:r})=>n==="end"&&r.size!=="small",style:{marginRight:-12}}]}),NO=pe("input",{name:"MuiSwitchBase",shouldForwardProp:Yn})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),BO=M.forwardRef(function(r,l){const{autoFocus:i,checked:u,checkedIcon:c,defaultChecked:d,disabled:p,disableFocusRipple:m=!1,edge:h=!1,icon:y,id:S,inputProps:O,inputRef:w,name:R,onBlur:E,onChange:x,onFocus:$,readOnly:B,required:D=!1,tabIndex:k,type:z,value:N,slots:q={},slotProps:G={},...K}=r,[v,_]=il({controlled:u,default:!!d,name:"SwitchBase",state:"checked"}),V=Hr(),ae=ue=>{$&&$(ue),V&&V.onFocus&&V.onFocus(ue)},ee=ue=>{E&&E(ue),V&&V.onBlur&&V.onBlur(ue)},L=ue=>{if(ue.nativeEvent.defaultPrevented)return;const le=ue.target.checked;_(le),x&&x(ue,le)};let T=p;V&&typeof T>"u"&&(T=V.disabled);const H=z==="checkbox"||z==="radio",Y={...r,checked:v,disabled:T,disableFocusRipple:m,edge:h},X=$O(Y),A={slots:q,slotProps:{input:O,...G}},[U,J]=wt("root",{ref:l,elementType:jO,className:X.root,shouldForwardComponentProp:!0,externalForwardedProps:{...A,component:"span",...K},getSlotProps:ue=>({...ue,onFocus:le=>{ue.onFocus?.(le),ae(le)},onBlur:le=>{ue.onBlur?.(le),ee(le)}}),ownerState:Y,additionalProps:{centerRipple:!0,focusRipple:!m,disabled:T,role:void 0,tabIndex:null}}),[ne,fe]=wt("input",{ref:w,elementType:NO,className:X.input,externalForwardedProps:A,getSlotProps:ue=>({...ue,onChange:le=>{ue.onChange?.(le),L(le)}}),ownerState:Y,additionalProps:{autoFocus:i,checked:u,defaultChecked:d,disabled:T,id:H?S:void 0,name:R,readOnly:B,required:D,tabIndex:k,type:z,...z==="checkbox"&&N===void 0?{}:{value:N}}});return b.jsxs(U,{...J,children:[b.jsx(ne,{...fe}),v?c:y]})}),_O=An(b.jsx("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"})),LO=An(b.jsx("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})),HO=An(b.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}));function PO(n){return Xe("MuiCheckbox",n)}const yd=Ke("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]),UO=n=>{const{classes:r,indeterminate:l,color:i,size:u}=n,c={root:["root",l&&"indeterminate",`color${de(i)}`,`size${de(u)}`]},d=Qe(c,PO,r);return{...r,...d}},IO=pe(BO,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiCheckbox",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.indeterminate&&r.indeterminate,r[`size${de(l.size)}`],l.color!=="default"&&r[`color${de(l.color)}`]]}})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.action.activeChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette.action.active,n.palette.action.hoverOpacity)}}},...Object.entries(n.palette).filter(Sn()).map(([r])=>({props:{color:r,disableRipple:!1},style:{"&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette[r].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette[r].main,n.palette.action.hoverOpacity)}}})),...Object.entries(n.palette).filter(Sn()).map(([r])=>({props:{color:r},style:{[`&.${yd.checked}, &.${yd.indeterminate}`]:{color:(n.vars||n).palette[r].main},[`&.${yd.disabled}`]:{color:(n.vars||n).palette.action.disabled}}})),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}))),qO=b.jsx(LO,{}),VO=b.jsx(_O,{}),YO=b.jsx(HO,{}),GO=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiCheckbox"}),{checkedIcon:u=qO,color:c="primary",icon:d=VO,indeterminate:p=!1,indeterminateIcon:m=YO,inputProps:h,size:y="medium",disableRipple:S=!1,className:O,slots:w={},slotProps:R={},...E}=i,x=p?m:d,$=p?m:u,B={...i,disableRipple:S,color:c,indeterminate:p,size:y},D=UO(B),k=R.input??h,[z,N]=wt("root",{ref:l,elementType:IO,className:Me(D.root,O),shouldForwardComponentProp:!0,externalForwardedProps:{slots:w,slotProps:R,...E},ownerState:B,additionalProps:{type:"checkbox",icon:M.cloneElement(x,{fontSize:x.props.fontSize??y}),checkedIcon:M.cloneElement($,{fontSize:$.props.fontSize??y}),disableRipple:S,slots:w,slotProps:{input:L0(typeof k=="function"?k(B):k,{"data-indeterminate":p})}}});return b.jsx(z,{...N,classes:D})}),Ud=typeof fp({})=="function",XO=(n,r)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...r&&!n.vars&&{colorScheme:n.palette.mode}}),KO=n=>({color:(n.vars||n).palette.text.primary,...n.typography.body1,backgroundColor:(n.vars||n).palette.background.default,"@media print":{backgroundColor:(n.vars||n).palette.common.white}}),cb=(n,r=!1)=>{const l={};r&&n.colorSchemes&&typeof n.getColorSchemeSelector=="function"&&Object.entries(n.colorSchemes).forEach(([c,d])=>{const p=n.getColorSchemeSelector(c);p.startsWith("@")?l[p]={":root":{colorScheme:d.palette?.mode}}:l[p.replace(/\s*&/,"")]={colorScheme:d.palette?.mode}});let i={html:XO(n,r),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:n.typography.fontWeightBold},body:{margin:0,...KO(n),"&::backdrop":{backgroundColor:(n.vars||n).palette.background.default}},...l};const u=n.components?.MuiCssBaseline?.styleOverrides;return u&&(i=[i,u]),i},gu="mui-ecs",QO=n=>{const r=cb(n,!1),l=Array.isArray(r)?r[0]:r;return!n.vars&&l&&(l.html[`:root:has(${gu})`]={colorScheme:n.palette.mode}),n.colorSchemes&&Object.entries(n.colorSchemes).forEach(([i,u])=>{const c=n.getColorSchemeSelector(i);c.startsWith("@")?l[c]={[`:root:not(:has(.${gu}))`]:{colorScheme:u.palette?.mode}}:l[c.replace(/\s*&/,"")]={[`&:not(:has(.${gu}))`]:{colorScheme:u.palette?.mode}}}),r},WO=fp(Ud?({theme:n,enableColorScheme:r})=>cb(n,r):({theme:n})=>QO(n));function FO(n){const r=Je({props:n,name:"MuiCssBaseline"}),{children:l,enableColorScheme:i=!1}=r;return b.jsxs(M.Fragment,{children:[Ud&&b.jsx(WO,{enableColorScheme:i}),!Ud&&!i&&b.jsx("span",{className:gu,style:{display:"none"}}),l]})}function fb(n=window){const r=n.document.documentElement.clientWidth;return n.innerWidth-r}function ZO(n){const r=qn(n);return r.body===n?ir(n).innerWidth>r.documentElement.clientWidth:n.scrollHeight>n.clientHeight}function Oi(n,r){r?n.setAttribute("aria-hidden","true"):n.removeAttribute("aria-hidden")}function Mv(n){return parseInt(ir(n).getComputedStyle(n).paddingRight,10)||0}function JO(n){const l=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(n.tagName),i=n.tagName==="INPUT"&&n.getAttribute("type")==="hidden";return l||i}function wv(n,r,l,i,u){const c=[r,l,...i];[].forEach.call(n.children,d=>{const p=!c.includes(d),m=!JO(d);p&&m&&Oi(d,u)})}function vd(n,r){let l=-1;return n.some((i,u)=>r(i)?(l=u,!0):!1),l}function eM(n,r){const l=[],i=n.container;if(!r.disableScrollLock){if(ZO(i)){const d=fb(ir(i));l.push({value:i.style.paddingRight,property:"padding-right",el:i}),i.style.paddingRight=`${Mv(i)+d}px`;const p=qn(i).querySelectorAll(".mui-fixed");[].forEach.call(p,m=>{l.push({value:m.style.paddingRight,property:"padding-right",el:m}),m.style.paddingRight=`${Mv(m)+d}px`})}let c;if(i.parentNode instanceof DocumentFragment)c=qn(i).body;else{const d=i.parentElement,p=ir(i);c=d?.nodeName==="HTML"&&p.getComputedStyle(d).overflowY==="scroll"?d:i}l.push({value:c.style.overflow,property:"overflow",el:c},{value:c.style.overflowX,property:"overflow-x",el:c},{value:c.style.overflowY,property:"overflow-y",el:c}),c.style.overflow="hidden"}return()=>{l.forEach(({value:c,el:d,property:p})=>{c?d.style.setProperty(p,c):d.style.removeProperty(p)})}}function tM(n){const r=[];return[].forEach.call(n.children,l=>{l.getAttribute("aria-hidden")==="true"&&r.push(l)}),r}class nM{constructor(){this.modals=[],this.containers=[]}add(r,l){let i=this.modals.indexOf(r);if(i!==-1)return i;i=this.modals.length,this.modals.push(r),r.modalRef&&Oi(r.modalRef,!1);const u=tM(l);wv(l,r.mount,r.modalRef,u,!0);const c=vd(this.containers,d=>d.container===l);return c!==-1?(this.containers[c].modals.push(r),i):(this.containers.push({modals:[r],container:l,restore:null,hiddenSiblings:u}),i)}mount(r,l){const i=vd(this.containers,c=>c.modals.includes(r)),u=this.containers[i];u.restore||(u.restore=eM(u,l))}remove(r,l=!0){const i=this.modals.indexOf(r);if(i===-1)return i;const u=vd(this.containers,d=>d.modals.includes(r)),c=this.containers[u];if(c.modals.splice(c.modals.indexOf(r),1),this.modals.splice(i,1),c.modals.length===0)c.restore&&c.restore(),r.modalRef&&Oi(r.modalRef,l),wv(c.container,r.mount,r.modalRef,c.hiddenSiblings,!1),this.containers.splice(u,1);else{const d=c.modals[c.modals.length-1];d.modalRef&&Oi(d.modalRef,!1)}return i}isTopModal(r){return this.modals.length>0&&this.modals[this.modals.length-1]===r}}const aM=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function rM(n){const r=parseInt(n.getAttribute("tabindex")||"",10);return Number.isNaN(r)?n.contentEditable==="true"||(n.nodeName==="AUDIO"||n.nodeName==="VIDEO"||n.nodeName==="DETAILS")&&n.getAttribute("tabindex")===null?0:n.tabIndex:r}function oM(n){if(n.tagName!=="INPUT"||n.type!=="radio"||!n.name)return!1;const r=i=>n.ownerDocument.querySelector(`input[type="radio"]${i}`);let l=r(`[name="${n.name}"]:checked`);return l||(l=r(`[name="${n.name}"]`)),l!==n}function lM(n){return!(n.disabled||n.tagName==="INPUT"&&n.type==="hidden"||oM(n))}function iM(n){const r=[],l=[];return Array.from(n.querySelectorAll(aM)).forEach((i,u)=>{const c=rM(i);c===-1||!lM(i)||(c===0?r.push(i):l.push({documentOrder:u,tabIndex:c,node:i}))}),l.sort((i,u)=>i.tabIndex===u.tabIndex?i.documentOrder-u.documentOrder:i.tabIndex-u.tabIndex).map(i=>i.node).concat(r)}function sM(){return!0}function uM(n){const{children:r,disableAutoFocus:l=!1,disableEnforceFocus:i=!1,disableRestoreFocus:u=!1,getTabbable:c=iM,isEnabled:d=sM,open:p}=n,m=M.useRef(!1),h=M.useRef(null),y=M.useRef(null),S=M.useRef(null),O=M.useRef(null),w=M.useRef(!1),R=M.useRef(null),E=fn(Gi(r),R),x=M.useRef(null);M.useEffect(()=>{!p||!R.current||(w.current=!l)},[l,p]),M.useEffect(()=>{if(!p||!R.current)return;const D=qn(R.current);return R.current.contains(D.activeElement)||(R.current.hasAttribute("tabIndex")||R.current.setAttribute("tabIndex","-1"),w.current&&R.current.focus()),()=>{u||(S.current&&S.current.focus&&(m.current=!0,S.current.focus()),S.current=null)}},[p]),M.useEffect(()=>{if(!p||!R.current)return;const D=qn(R.current),k=q=>{x.current=q,!(i||!d()||q.key!=="Tab")&&D.activeElement===R.current&&q.shiftKey&&(m.current=!0,y.current&&y.current.focus())},z=()=>{const q=R.current;if(q===null)return;if(!D.hasFocus()||!d()||m.current){m.current=!1;return}if(q.contains(D.activeElement)||i&&D.activeElement!==h.current&&D.activeElement!==y.current)return;if(D.activeElement!==O.current)O.current=null;else if(O.current!==null)return;if(!w.current)return;let G=[];if((D.activeElement===h.current||D.activeElement===y.current)&&(G=c(R.current)),G.length>0){const K=!!(x.current?.shiftKey&&x.current?.key==="Tab"),v=G[0],_=G[G.length-1];typeof v!="string"&&typeof _!="string"&&(K?_.focus():v.focus())}else q.focus()};D.addEventListener("focusin",z),D.addEventListener("keydown",k,!0);const N=setInterval(()=>{D.activeElement&&D.activeElement.tagName==="BODY"&&z()},50);return()=>{clearInterval(N),D.removeEventListener("focusin",z),D.removeEventListener("keydown",k,!0)}},[l,i,u,d,p,c]);const $=D=>{S.current===null&&(S.current=D.relatedTarget),w.current=!0,O.current=D.target;const k=r.props.onFocus;k&&k(D)},B=D=>{S.current===null&&(S.current=D.relatedTarget),w.current=!0};return b.jsxs(M.Fragment,{children:[b.jsx("div",{tabIndex:p?0:-1,onFocus:B,ref:h,"data-testid":"sentinelStart"}),M.cloneElement(r,{ref:E,onFocus:$}),b.jsx("div",{tabIndex:p?0:-1,onFocus:B,ref:y,"data-testid":"sentinelEnd"})]})}function cM(n){return typeof n=="function"?n():n}function fM(n){return n?n.props.hasOwnProperty("in"):!1}const Av=()=>{},su=new nM;function dM(n){const{container:r,disableEscapeKeyDown:l=!1,disableScrollLock:i=!1,closeAfterTransition:u=!1,onTransitionEnter:c,onTransitionExited:d,children:p,onClose:m,open:h,rootRef:y}=n,S=M.useRef({}),O=M.useRef(null),w=M.useRef(null),R=fn(w,y),[E,x]=M.useState(!h),$=fM(p);let B=!0;(n["aria-hidden"]==="false"||n["aria-hidden"]===!1)&&(B=!1);const D=()=>qn(O.current),k=()=>(S.current.modalRef=w.current,S.current.mount=O.current,S.current),z=()=>{su.mount(k(),{disableScrollLock:i}),w.current&&(w.current.scrollTop=0)},N=Ca(()=>{const L=cM(r)||D().body;su.add(k(),L),w.current&&z()}),q=()=>su.isTopModal(k()),G=Ca(L=>{O.current=L,L&&(h&&q()?z():w.current&&Oi(w.current,B))}),K=M.useCallback(()=>{su.remove(k(),B)},[B]);M.useEffect(()=>()=>{K()},[K]),M.useEffect(()=>{h?N():(!$||!u)&&K()},[h,K,$,u,N]);const v=L=>T=>{L.onKeyDown?.(T),!(T.key!=="Escape"||T.which===229||!q())&&(l||(T.stopPropagation(),m&&m(T,"escapeKeyDown")))},_=L=>T=>{L.onClick?.(T),T.target===T.currentTarget&&m&&m(T,"backdropClick")};return{getRootProps:(L={})=>{const T=X0(n);delete T.onTransitionEnter,delete T.onTransitionExited;const H={...T,...L};return{role:"presentation",...H,onKeyDown:v(H),ref:R}},getBackdropProps:(L={})=>{const T=L;return{"aria-hidden":!0,...T,onClick:_(T),open:h}},getTransitionProps:()=>{const L=()=>{x(!1),c&&c()},T=()=>{x(!0),d&&d(),u&&K()};return{onEnter:Jy(L,p?.props.onEnter??Av),onExited:Jy(T,p?.props.onExited??Av)}},rootRef:R,portalRef:G,isTopModal:q,exited:E,hasTransition:$}}function pM(n){return Xe("MuiModal",n)}Ke("MuiModal",["root","hidden","backdrop"]);const mM=n=>{const{open:r,exited:l,classes:i}=n;return Qe({root:["root",!r&&l&&"hidden"],backdrop:["backdrop"]},pM,i)},hM=pe("div",{name:"MuiModal",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,!l.open&&l.exited&&r.hidden]}})(tt(({theme:n})=>({position:"fixed",zIndex:(n.vars||n).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:r})=>!r.open&&r.exited,style:{visibility:"hidden"}}]}))),gM=pe(xO,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),yM=M.forwardRef(function(r,l){const i=Je({name:"MuiModal",props:r}),{BackdropComponent:u=gM,BackdropProps:c,classes:d,className:p,closeAfterTransition:m=!1,children:h,container:y,component:S,components:O={},componentsProps:w={},disableAutoFocus:R=!1,disableEnforceFocus:E=!1,disableEscapeKeyDown:x=!1,disablePortal:$=!1,disableRestoreFocus:B=!1,disableScrollLock:D=!1,hideBackdrop:k=!1,keepMounted:z=!1,onClose:N,onTransitionEnter:q,onTransitionExited:G,open:K,slotProps:v={},slots:_={},theme:V,...ae}=i,ee={...i,closeAfterTransition:m,disableAutoFocus:R,disableEnforceFocus:E,disableEscapeKeyDown:x,disablePortal:$,disableRestoreFocus:B,disableScrollLock:D,hideBackdrop:k,keepMounted:z},{getRootProps:L,getBackdropProps:T,getTransitionProps:H,portalRef:Y,isTopModal:X,exited:A,hasTransition:U}=dM({...ee,rootRef:l}),J={...ee,exited:A},ne=mM(J),fe={};if(h.props.tabIndex===void 0&&(fe.tabIndex="-1"),U){const{onEnter:me,onExited:Re}=H();fe.onEnter=me,fe.onExited=Re}const ue={slots:{root:O.Root,backdrop:O.Backdrop,..._},slotProps:{...w,...v}},[le,ve]=wt("root",{ref:l,elementType:hM,externalForwardedProps:{...ue,...ae,component:S},getSlotProps:L,ownerState:J,className:Me(p,ne?.root,!J.open&&J.exited&&ne?.hidden)}),[xe,be]=wt("backdrop",{ref:c?.ref,elementType:u,externalForwardedProps:ue,shouldForwardComponentProp:!0,additionalProps:c,getSlotProps:me=>T({...me,onClick:Re=>{me?.onClick&&me.onClick(Re)}}),className:Me(c?.className,ne?.backdrop),ownerState:J});return!z&&!K&&(!U||A)?null:b.jsx(lb,{ref:Y,container:y,disablePortal:$,children:b.jsxs(le,{...ve,children:[!k&&u?b.jsx(xe,{...be}):null,b.jsx(uM,{disableEnforceFocus:E,disableAutoFocus:R,disableRestoreFocus:B,isEnabled:X,open:K,children:M.cloneElement(h,fe)})]})})}),vM=n=>{const{classes:r,disableUnderline:l,startAdornment:i,endAdornment:u,size:c,hiddenLabel:d,multiline:p}=n,m={root:["root",!l&&"underline",i&&"adornedStart",u&&"adornedEnd",c==="small"&&`size${de(c)}`,d&&"hiddenLabel",p&&"multiline"],input:["input"]},h=Qe(m,nO,r);return{...r,...h}},bM=pe(Qu,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiFilledInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[...Xu(n,r),!l.disableUnderline&&r.underline]}})(tt(({theme:n})=>{const r=n.palette.mode==="light",l=r?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",i=r?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",u=r?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",c=r?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return{position:"relative",backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i,borderTopLeftRadius:(n.vars||n).shape.borderRadius,borderTopRightRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),"&:hover":{backgroundColor:n.vars?n.vars.palette.FilledInput.hoverBg:u,"@media (hover: none)":{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i}},[`&.${Pn.focused}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i},[`&.${Pn.disabled}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.disabledBg:c},variants:[{props:({ownerState:d})=>!d.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${Pn.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${Pn.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`:l}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${Pn.disabled}, .${Pn.error}):before`]:{borderBottom:`1px solid ${(n.vars||n).palette.text.primary}`},[`&.${Pn.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(Sn()).map(([d])=>({props:{disableUnderline:!1,color:d},style:{"&::after":{borderBottom:`2px solid ${(n.vars||n).palette[d]?.main}`}}})),{props:({ownerState:d})=>d.startAdornment,style:{paddingLeft:12}},{props:({ownerState:d})=>d.endAdornment,style:{paddingRight:12}},{props:({ownerState:d})=>d.multiline,style:{padding:"25px 12px 8px"}},{props:({ownerState:d,size:p})=>d.multiline&&p==="small",style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel&&d.size==="small",style:{paddingTop:8,paddingBottom:9}}]}})),SM=pe(Wu,{name:"MuiFilledInput",slot:"Input",overridesResolver:Ku})(tt(({theme:n})=>({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12,...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:r})=>r.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}},{props:({ownerState:r})=>r.hiddenLabel&&r.size==="small",style:{paddingTop:8,paddingBottom:9}},{props:({ownerState:r})=>r.multiline,style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]}))),Rp=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFilledInput"}),{disableUnderline:u=!1,components:c={},componentsProps:d,fullWidth:p=!1,hiddenLabel:m,inputComponent:h="input",multiline:y=!1,slotProps:S,slots:O={},type:w="text",...R}=i,E={...i,disableUnderline:u,fullWidth:p,inputComponent:h,multiline:y,type:w},x=vM(i),$={root:{ownerState:E},input:{ownerState:E}},B=S??d?hn($,S??d):$,D=O.root??c.Root??bM,k=O.input??c.Input??SM;return b.jsx(Ep,{slots:{root:D,input:k},slotProps:B,fullWidth:p,inputComponent:h,multiline:y,ref:l,type:w,...R,classes:x})});Rp.muiName="Input";function xM(n){return Xe("MuiFormControl",n)}Ke("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const CM=n=>{const{classes:r,margin:l,fullWidth:i}=n,u={root:["root",l!=="none"&&`margin${de(l)}`,i&&"fullWidth"]};return Qe(u,xM,r)},TM=pe("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[`margin${de(l.margin)}`],l.fullWidth&&r.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),EM=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFormControl"}),{children:u,className:c,color:d="primary",component:p="div",disabled:m=!1,error:h=!1,focused:y,fullWidth:S=!1,hiddenLabel:O=!1,margin:w="none",required:R=!1,size:E="medium",variant:x="outlined",...$}=i,B={...i,color:d,component:p,disabled:m,error:h,fullWidth:S,hiddenLabel:O,margin:w,required:R,size:E,variant:x},D=CM(B),[k,z]=M.useState(()=>{let L=!1;return u&&M.Children.forEach(u,T=>{if(!mu(T,["Input","Select"]))return;const H=mu(T,["Select"])?T.props.input:T;H&&FR(H.props)&&(L=!0)}),L}),[N,q]=M.useState(()=>{let L=!1;return u&&M.Children.forEach(u,T=>{mu(T,["Input","Select"])&&(Mu(T.props,!0)||Mu(T.props.inputProps,!0))&&(L=!0)}),L}),[G,K]=M.useState(!1);m&&G&&K(!1);const v=y!==void 0&&!m?y:G;let _;M.useRef(!1);const V=M.useCallback(()=>{q(!0)},[]),ae=M.useCallback(()=>{q(!1)},[]),ee=M.useMemo(()=>({adornedStart:k,setAdornedStart:z,color:d,disabled:m,error:h,filled:N,focused:v,fullWidth:S,hiddenLabel:O,size:E,onBlur:()=>{K(!1)},onFocus:()=>{K(!0)},onEmpty:ae,onFilled:V,registerEffect:_,required:R,variant:x}),[k,d,m,h,N,v,S,O,_,ae,V,R,E,x]);return b.jsx(Tp.Provider,{value:ee,children:b.jsx(TM,{as:p,ownerState:B,className:Me(D.root,c),ref:l,...$,children:u})})});function RM(n){return Xe("MuiFormControlLabel",n)}const Ci=Ke("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]),OM=n=>{const{classes:r,disabled:l,labelPlacement:i,error:u,required:c}=n,d={root:["root",l&&"disabled",`labelPlacement${de(i)}`,u&&"error",c&&"required"],label:["label",l&&"disabled"],asterisk:["asterisk",u&&"error"]};return Qe(d,RM,r)},MM=pe("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[{[`& .${Ci.label}`]:r.label},r.root,r[`labelPlacement${de(l.labelPlacement)}`]]}})(tt(({theme:n})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${Ci.disabled}`]:{cursor:"default"},[`& .${Ci.label}`]:{[`&.${Ci.disabled}`]:{color:(n.vars||n).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:r})=>r==="start"||r==="top"||r==="bottom",style:{marginLeft:16}}]}))),wM=pe("span",{name:"MuiFormControlLabel",slot:"Asterisk"})(tt(({theme:n})=>({[`&.${Ci.error}`]:{color:(n.vars||n).palette.error.main}}))),AM=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFormControlLabel"}),{checked:u,className:c,componentsProps:d={},control:p,disabled:m,disableTypography:h,inputRef:y,label:S,labelPlacement:O="end",name:w,onChange:R,required:E,slots:x={},slotProps:$={},value:B,...D}=i,k=Hr(),z=m??p.props.disabled??k?.disabled,N=E??p.props.required,q={disabled:z,required:N};["checked","name","onChange","value","inputRef"].forEach(L=>{typeof p.props[L]>"u"&&typeof i[L]<"u"&&(q[L]=i[L])});const G=fo({props:i,muiFormControl:k,states:["error"]}),K={...i,disabled:z,labelPlacement:O,required:N,error:G.error},v=OM(K),_={slots:x,slotProps:{...d,...$}},[V,ae]=wt("typography",{elementType:qe,externalForwardedProps:_,ownerState:K});let ee=S;return ee!=null&&ee.type!==qe&&!h&&(ee=b.jsx(V,{component:"span",...ae,className:Me(v.label,ae?.className),children:ee})),b.jsxs(MM,{className:Me(v.root,c),ownerState:K,ref:l,...D,children:[M.cloneElement(p,q),N?b.jsxs("div",{children:[ee,b.jsxs(wM,{ownerState:K,"aria-hidden":!0,className:v.asterisk,children:[" ","*"]})]}):ee]})});function zM(n){return Xe("MuiFormHelperText",n)}const zv=Ke("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var Dv;const DM=n=>{const{classes:r,contained:l,size:i,disabled:u,error:c,filled:d,focused:p,required:m}=n,h={root:["root",u&&"disabled",c&&"error",i&&`size${de(i)}`,l&&"contained",p&&"focused",d&&"filled",m&&"required"]};return Qe(h,zM,r)},kM=pe("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.size&&r[`size${de(l.size)}`],l.contained&&r.contained,l.filled&&r.filled]}})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${zv.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${zv.error}`]:{color:(n.vars||n).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:({ownerState:r})=>r.contained,style:{marginLeft:14,marginRight:14}}]}))),$M=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFormHelperText"}),{children:u,className:c,component:d="p",disabled:p,error:m,filled:h,focused:y,margin:S,required:O,variant:w,...R}=i,E=Hr(),x=fo({props:i,muiFormControl:E,states:["variant","size","disabled","error","filled","focused","required"]}),$={...i,component:d,contained:x.variant==="filled"||x.variant==="outlined",variant:x.variant,size:x.size,disabled:x.disabled,error:x.error,filled:x.filled,focused:x.focused,required:x.required};delete $.ownerState;const B=DM($);return b.jsx(kM,{as:d,className:Me(B.root,c),ref:l,...R,ownerState:$,children:u===" "?Dv||(Dv=b.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):u})});function jM(n){return Xe("MuiFormLabel",n)}const Mi=Ke("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),NM=n=>{const{classes:r,color:l,focused:i,disabled:u,error:c,filled:d,required:p}=n,m={root:["root",`color${de(l)}`,u&&"disabled",c&&"error",d&&"filled",i&&"focused",p&&"required"],asterisk:["asterisk",c&&"error"]};return Qe(m,jM,r)},BM=pe("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.color==="secondary"&&r.colorSecondary,l.filled&&r.filled]}})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.body1,lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(n.palette).filter(Sn()).map(([r])=>({props:{color:r},style:{[`&.${Mi.focused}`]:{color:(n.vars||n).palette[r].main}}})),{props:{},style:{[`&.${Mi.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${Mi.error}`]:{color:(n.vars||n).palette.error.main}}}]}))),_M=pe("span",{name:"MuiFormLabel",slot:"Asterisk"})(tt(({theme:n})=>({[`&.${Mi.error}`]:{color:(n.vars||n).palette.error.main}}))),LM=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFormLabel"}),{children:u,className:c,color:d,component:p="label",disabled:m,error:h,filled:y,focused:S,required:O,...w}=i,R=Hr(),E=fo({props:i,muiFormControl:R,states:["color","required","focused","disabled","error","filled"]}),x={...i,color:E.color||"primary",component:p,disabled:E.disabled,error:E.error,filled:E.filled,focused:E.focused,required:E.required},$=NM(x);return b.jsxs(BM,{as:p,ownerState:x,className:Me($.root,c),ref:l,...w,children:[u,E.required&&b.jsxs(_M,{ownerState:x,"aria-hidden":!0,className:$.asterisk,children:[" ","*"]})]})}),we=UC({createStyledComponent:pe("div",{name:"MuiGrid",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.container&&r.container]}}),componentName:"MuiGrid",useThemeProps:n=>Je({props:n,name:"MuiGrid"}),useTheme:Yu});function Id(n){return`scale(${n}, ${n**2})`}const HM={entering:{opacity:1,transform:Id(1)},entered:{opacity:1,transform:"none"}},bd=typeof navigator<"u"&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),qd=M.forwardRef(function(r,l){const{addEndListener:i,appear:u=!0,children:c,easing:d,in:p,onEnter:m,onEntered:h,onEntering:y,onExit:S,onExited:O,onExiting:w,style:R,timeout:E="auto",TransitionComponent:x=Pa,...$}=r,B=q0(),D=M.useRef(),k=Yu(),z=M.useRef(null),N=fn(z,Gi(c),l),q=L=>T=>{if(L){const H=z.current;T===void 0?L(H):L(H,T)}},G=q(y),K=q((L,T)=>{V0(L);const{duration:H,delay:Y,easing:X}=Eu({style:R,timeout:E,easing:d},{mode:"enter"});let A;E==="auto"?(A=k.transitions.getAutoHeightDuration(L.clientHeight),D.current=A):A=H,L.style.transition=[k.transitions.create("opacity",{duration:A,delay:Y}),k.transitions.create("transform",{duration:bd?A:A*.666,delay:Y,easing:X})].join(","),m&&m(L,T)}),v=q(h),_=q(w),V=q(L=>{const{duration:T,delay:H,easing:Y}=Eu({style:R,timeout:E,easing:d},{mode:"exit"});let X;E==="auto"?(X=k.transitions.getAutoHeightDuration(L.clientHeight),D.current=X):X=T,L.style.transition=[k.transitions.create("opacity",{duration:X,delay:H}),k.transitions.create("transform",{duration:bd?X:X*.666,delay:bd?H:H||X*.333,easing:Y})].join(","),L.style.opacity=0,L.style.transform=Id(.75),S&&S(L)}),ae=q(O),ee=L=>{E==="auto"&&B.start(D.current||0,L),i&&i(z.current,L)};return b.jsx(x,{appear:u,in:p,nodeRef:z,onEnter:K,onEntered:v,onEntering:G,onExit:V,onExited:ae,onExiting:_,addEndListener:ee,timeout:E==="auto"?null:E,...$,children:(L,{ownerState:T,...H})=>M.cloneElement(c,{style:{opacity:0,transform:Id(.75),visibility:L==="exited"&&!p?"hidden":void 0,...HM[L],...R,...c.props.style},ref:N,...H})})});qd&&(qd.muiSupportAuto=!0);const PM=n=>{const{classes:r,disableUnderline:l}=n,u=Qe({root:["root",!l&&"underline"],input:["input"]},eO,r);return{...r,...u}},UM=pe(Qu,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[...Xu(n,r),!l.disableUnderline&&r.underline]}})(tt(({theme:n})=>{let l=n.palette.mode==="light"?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return n.vars&&(l=`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`),{position:"relative",variants:[{props:({ownerState:i})=>i.formControl,style:{"label + &":{marginTop:16}}},{props:({ownerState:i})=>!i.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${jr.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${jr.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${l}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${jr.disabled}, .${jr.error}):before`]:{borderBottom:`2px solid ${(n.vars||n).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${l}`}},[`&.${jr.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(Sn()).map(([i])=>({props:{color:i,disableUnderline:!1},style:{"&::after":{borderBottom:`2px solid ${(n.vars||n).palette[i].main}`}}}))]}})),IM=pe(Wu,{name:"MuiInput",slot:"Input",overridesResolver:Ku})({}),Op=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiInput"}),{disableUnderline:u=!1,components:c={},componentsProps:d,fullWidth:p=!1,inputComponent:m="input",multiline:h=!1,slotProps:y,slots:S={},type:O="text",...w}=i,R=PM(i),x={root:{ownerState:{disableUnderline:u}}},$=y??d?hn(y??d,x):x,B=S.root??c.Root??UM,D=S.input??c.Input??IM;return b.jsx(Ep,{slots:{root:B,input:D},slotProps:$,fullWidth:p,inputComponent:m,multiline:h,ref:l,type:O,...w,classes:R})});Op.muiName="Input";function qM(n){return Xe("MuiInputLabel",n)}Ke("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const VM=n=>{const{classes:r,formControl:l,size:i,shrink:u,disableAnimation:c,variant:d,required:p}=n,m={root:["root",l&&"formControl",!c&&"animated",u&&"shrink",i&&i!=="medium"&&`size${de(i)}`,d],asterisk:[p&&"asterisk"]},h=Qe(m,qM,r);return{...r,...h}},YM=pe(LM,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiInputLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[{[`& .${Mi.asterisk}`]:r.asterisk},r.root,l.formControl&&r.formControl,l.size==="small"&&r.sizeSmall,l.shrink&&r.shrink,!l.disableAnimation&&r.animated,l.focused&&r.focused,r[l.variant]]}})(tt(({theme:n})=>({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:({ownerState:r})=>r.formControl,style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:({ownerState:r})=>r.shrink,style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:({ownerState:r})=>!r.disableAnimation,style:{transition:n.transitions.create(["color","transform","max-width"],{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:({variant:r,ownerState:l})=>r==="filled"&&l.shrink,style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:({variant:r,ownerState:l,size:i})=>r==="filled"&&l.shrink&&i==="small",style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:({variant:r,ownerState:l})=>r==="outlined"&&l.shrink,style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}))),GM=M.forwardRef(function(r,l){const i=Je({name:"MuiInputLabel",props:r}),{disableAnimation:u=!1,margin:c,shrink:d,variant:p,className:m,...h}=i,y=Hr();let S=d;typeof S>"u"&&y&&(S=y.filled||y.focused||y.adornedStart);const O=fo({props:i,muiFormControl:y,states:["size","variant","required","focused"]}),w={...i,disableAnimation:u,formControl:y,shrink:S,size:O.size,variant:O.variant,required:O.required,focused:O.focused},R=VM(w);return b.jsx(YM,{"data-shrink":S,ref:l,className:Me(R.root,m),...h,ownerState:w,classes:R})}),XM=M.createContext({});function KM(n){return Xe("MuiList",n)}Ke("MuiList",["root","padding","dense","subheader"]);const QM=n=>{const{classes:r,disablePadding:l,dense:i,subheader:u}=n;return Qe({root:["root",!l&&"padding",i&&"dense",u&&"subheader"]},KM,r)},WM=pe("ul",{name:"MuiList",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,!l.disablePadding&&r.padding,l.dense&&r.dense,l.subheader&&r.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:({ownerState:n})=>!n.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:n})=>n.subheader,style:{paddingTop:0}}]}),FM=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiList"}),{children:u,className:c,component:d="ul",dense:p=!1,disablePadding:m=!1,subheader:h,...y}=i,S=M.useMemo(()=>({dense:p}),[p]),O={...i,component:d,dense:p,disablePadding:m},w=QM(O);return b.jsx(XM.Provider,{value:S,children:b.jsxs(WM,{as:d,className:Me(w.root,c),ref:l,ownerState:O,...y,children:[h,u]})})});function Sd(n,r,l){return n===r?n.firstChild:r&&r.nextElementSibling?r.nextElementSibling:l?null:n.firstChild}function kv(n,r,l){return n===r?l?n.firstChild:n.lastChild:r&&r.previousElementSibling?r.previousElementSibling:l?null:n.lastChild}function db(n,r){if(r===void 0)return!0;let l=n.innerText;return l===void 0&&(l=n.textContent),l=l.trim().toLowerCase(),l.length===0?!1:r.repeating?l[0]===r.keys[0]:l.startsWith(r.keys.join(""))}function yi(n,r,l,i,u,c){let d=!1,p=u(n,r,r?l:!1);for(;p;){if(p===n.firstChild){if(d)return!1;d=!0}const m=i?!1:p.disabled||p.getAttribute("aria-disabled")==="true";if(!p.hasAttribute("tabindex")||!db(p,c)||m)p=u(n,p,l);else return p.focus(),!0}return!1}const ZM=M.forwardRef(function(r,l){const{actions:i,autoFocus:u=!1,autoFocusItem:c=!1,children:d,className:p,disabledItemsFocusable:m=!1,disableListWrap:h=!1,onKeyDown:y,variant:S="selectedMenu",...O}=r,w=M.useRef(null),R=M.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});fa(()=>{u&&w.current.focus()},[u]),M.useImperativeHandle(i,()=>({adjustStyleForScrollbar:(D,{direction:k})=>{const z=!w.current.style.width;if(D.clientHeight<w.current.clientHeight&&z){const N=`${fb(ir(D))}px`;w.current.style[k==="rtl"?"paddingLeft":"paddingRight"]=N,w.current.style.width=`calc(100% + ${N})`}return w.current}}),[]);const E=D=>{const k=w.current,z=D.key;if(D.ctrlKey||D.metaKey||D.altKey){y&&y(D);return}const q=qn(k).activeElement;if(z==="ArrowDown")D.preventDefault(),yi(k,q,h,m,Sd);else if(z==="ArrowUp")D.preventDefault(),yi(k,q,h,m,kv);else if(z==="Home")D.preventDefault(),yi(k,null,h,m,Sd);else if(z==="End")D.preventDefault(),yi(k,null,h,m,kv);else if(z.length===1){const G=R.current,K=z.toLowerCase(),v=performance.now();G.keys.length>0&&(v-G.lastTime>500?(G.keys=[],G.repeating=!0,G.previousKeyMatched=!0):G.repeating&&K!==G.keys[0]&&(G.repeating=!1)),G.lastTime=v,G.keys.push(K);const _=q&&!G.repeating&&db(q,G);G.previousKeyMatched&&(_||yi(k,q,!1,m,Sd,G))?D.preventDefault():G.previousKeyMatched=!1}y&&y(D)},x=fn(w,l);let $=-1;M.Children.forEach(d,(D,k)=>{if(!M.isValidElement(D)){$===k&&($+=1,$>=d.length&&($=-1));return}D.props.disabled||(S==="selectedMenu"&&D.props.selected||$===-1)&&($=k),$===k&&(D.props.disabled||D.props.muiSkipListHighlight||D.type.muiSkipListHighlight)&&($+=1,$>=d.length&&($=-1))});const B=M.Children.map(d,(D,k)=>{if(k===$){const z={};return c&&(z.autoFocus=!0),D.props.tabIndex===void 0&&S==="selectedMenu"&&(z.tabIndex=0),M.cloneElement(D,z)}return D});return b.jsx(FM,{role:"menu",ref:x,className:p,onKeyDown:E,tabIndex:u?0:-1,...O,children:B})});function JM(n){return Xe("MuiPopover",n)}Ke("MuiPopover",["root","paper"]);function $v(n,r){let l=0;return typeof r=="number"?l=r:r==="center"?l=n.height/2:r==="bottom"&&(l=n.height),l}function jv(n,r){let l=0;return typeof r=="number"?l=r:r==="center"?l=n.width/2:r==="right"&&(l=n.width),l}function Nv(n){return[n.horizontal,n.vertical].map(r=>typeof r=="number"?`${r}px`:r).join(" ")}function uu(n){return typeof n=="function"?n():n}const ew=n=>{const{classes:r}=n;return Qe({root:["root"],paper:["paper"]},JM,r)},tw=pe(yM,{name:"MuiPopover",slot:"Root"})({}),pb=pe(un,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),nw=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiPopover"}),{action:u,anchorEl:c,anchorOrigin:d={vertical:"top",horizontal:"left"},anchorPosition:p,anchorReference:m="anchorEl",children:h,className:y,container:S,elevation:O=8,marginThreshold:w=16,open:R,PaperProps:E={},slots:x={},slotProps:$={},transformOrigin:B={vertical:"top",horizontal:"left"},TransitionComponent:D,transitionDuration:k="auto",TransitionProps:z={},disableScrollLock:N=!1,...q}=i,G=M.useRef(),K={...i,anchorOrigin:d,anchorReference:m,elevation:O,marginThreshold:w,transformOrigin:B,TransitionComponent:D,transitionDuration:k,TransitionProps:z},v=ew(K),_=M.useCallback(()=>{if(m==="anchorPosition")return p;const me=uu(c),Ne=(me&&me.nodeType===1?me:qn(G.current).body).getBoundingClientRect();return{top:Ne.top+$v(Ne,d.vertical),left:Ne.left+jv(Ne,d.horizontal)}},[c,d.horizontal,d.vertical,p,m]),V=M.useCallback(me=>({vertical:$v(me,B.vertical),horizontal:jv(me,B.horizontal)}),[B.horizontal,B.vertical]),ae=M.useCallback(me=>{const Re={width:me.offsetWidth,height:me.offsetHeight},Ne=V(Re);if(m==="none")return{top:null,left:null,transformOrigin:Nv(Ne)};const _e=_();let ke=_e.top-Ne.vertical,nt=_e.left-Ne.horizontal;const Ve=ke+Re.height,at=nt+Re.width,he=ir(uu(c)),st=he.innerHeight-w,Te=he.innerWidth-w;if(w!==null&&ke<w){const Le=ke-w;ke-=Le,Ne.vertical+=Le}else if(w!==null&&Ve>st){const Le=Ve-st;ke-=Le,Ne.vertical+=Le}if(w!==null&&nt<w){const Le=nt-w;nt-=Le,Ne.horizontal+=Le}else if(at>Te){const Le=at-Te;nt-=Le,Ne.horizontal+=Le}return{top:`${Math.round(ke)}px`,left:`${Math.round(nt)}px`,transformOrigin:Nv(Ne)}},[c,m,_,V,w]),[ee,L]=M.useState(R),T=M.useCallback(()=>{const me=G.current;if(!me)return;const Re=ae(me);Re.top!==null&&me.style.setProperty("top",Re.top),Re.left!==null&&(me.style.left=Re.left),me.style.transformOrigin=Re.transformOrigin,L(!0)},[ae]);M.useEffect(()=>(N&&window.addEventListener("scroll",T),()=>window.removeEventListener("scroll",T)),[c,N,T]);const H=()=>{T()},Y=()=>{L(!1)};M.useEffect(()=>{R&&T()}),M.useImperativeHandle(u,()=>R?{updatePosition:()=>{T()}}:null,[R,T]),M.useEffect(()=>{if(!R)return;const me=_0(()=>{T()}),Re=ir(uu(c));return Re.addEventListener("resize",me),()=>{me.clear(),Re.removeEventListener("resize",me)}},[c,R,T]);let X=k;const A={slots:{transition:D,...x},slotProps:{transition:z,paper:E,...$}},[U,J]=wt("transition",{elementType:qd,externalForwardedProps:A,ownerState:K,getSlotProps:me=>({...me,onEntering:(Re,Ne)=>{me.onEntering?.(Re,Ne),H()},onExited:Re=>{me.onExited?.(Re),Y()}}),additionalProps:{appear:!0,in:R}});k==="auto"&&!U.muiSupportAuto&&(X=void 0);const ne=S||(c?qn(uu(c)).body:void 0),[fe,{slots:ue,slotProps:le,...ve}]=wt("root",{ref:l,elementType:tw,externalForwardedProps:{...A,...q},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:x.backdrop},slotProps:{backdrop:L0(typeof $.backdrop=="function"?$.backdrop(K):$.backdrop,{invisible:!0})},container:ne,open:R},ownerState:K,className:Me(v.root,y)}),[xe,be]=wt("paper",{ref:G,className:v.paper,elementType:pb,externalForwardedProps:A,shouldForwardComponentProp:!0,additionalProps:{elevation:O,style:ee?void 0:{opacity:0}},ownerState:K});return b.jsx(fe,{...ve,...!Pd(fe)&&{slots:ue,slotProps:le,disableScrollLock:N},children:b.jsx(U,{...J,timeout:X,children:b.jsx(xe,{...be,children:h})})})});function aw(n){return Xe("MuiMenu",n)}Ke("MuiMenu",["root","paper","list"]);const rw={vertical:"top",horizontal:"right"},ow={vertical:"top",horizontal:"left"},lw=n=>{const{classes:r}=n;return Qe({root:["root"],paper:["paper"],list:["list"]},aw,r)},iw=pe(nw,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiMenu",slot:"Root"})({}),sw=pe(pb,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),uw=pe(ZM,{name:"MuiMenu",slot:"List"})({outline:0}),cw=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiMenu"}),{autoFocus:u=!0,children:c,className:d,disableAutoFocusItem:p=!1,MenuListProps:m={},onClose:h,open:y,PaperProps:S={},PopoverClasses:O,transitionDuration:w="auto",TransitionProps:{onEntering:R,...E}={},variant:x="selectedMenu",slots:$={},slotProps:B={},...D}=i,k=O0(),z={...i,autoFocus:u,disableAutoFocusItem:p,MenuListProps:m,onEntering:R,PaperProps:S,transitionDuration:w,TransitionProps:E,variant:x},N=lw(z),q=u&&!p&&y,G=M.useRef(null),K=(X,A)=>{G.current&&G.current.adjustStyleForScrollbar(X,{direction:k?"rtl":"ltr"}),R&&R(X,A)},v=X=>{X.key==="Tab"&&(X.preventDefault(),h&&h(X,"tabKeyDown"))};let _=-1;M.Children.map(c,(X,A)=>{M.isValidElement(X)&&(X.props.disabled||(x==="selectedMenu"&&X.props.selected||_===-1)&&(_=A))});const V={slots:$,slotProps:{list:m,transition:E,paper:S,...B}},ae=ob({elementType:$.root,externalSlotProps:B.root,ownerState:z,className:[N.root,d]}),[ee,L]=wt("paper",{className:N.paper,elementType:sw,externalForwardedProps:V,shouldForwardComponentProp:!0,ownerState:z}),[T,H]=wt("list",{className:Me(N.list,m.className),elementType:uw,shouldForwardComponentProp:!0,externalForwardedProps:V,getSlotProps:X=>({...X,onKeyDown:A=>{v(A),X.onKeyDown?.(A)}}),ownerState:z}),Y=typeof V.slotProps.transition=="function"?V.slotProps.transition(z):V.slotProps.transition;return b.jsx(iw,{onClose:h,anchorOrigin:{vertical:"bottom",horizontal:k?"right":"left"},transformOrigin:k?rw:ow,slots:{root:$.root,paper:ee,backdrop:$.backdrop,...$.transition&&{transition:$.transition}},slotProps:{root:ae,paper:L,backdrop:typeof B.backdrop=="function"?B.backdrop(z):B.backdrop,transition:{...Y,onEntering:(...X)=>{K(...X),Y?.onEntering?.(...X)}}},open:y,ref:l,transitionDuration:w,ownerState:z,...D,classes:O,children:b.jsx(T,{actions:G,autoFocus:u&&(_===-1||p),autoFocusItem:q,variant:x,...H,children:c})})});function fw(n){return Xe("MuiNativeSelect",n)}const Mp=Ke("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),dw=n=>{const{classes:r,variant:l,disabled:i,multiple:u,open:c,error:d}=n,p={select:["select",l,i&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${de(l)}`,c&&"iconOpen",i&&"disabled"]};return Qe(p,fw,r)},mb=pe("select",{name:"MuiNativeSelect"})(({theme:n})=>({MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},[`&.${Mp.disabled}`]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(n.vars||n).palette.background.paper},variants:[{props:({ownerState:r})=>r.variant!=="filled"&&r.variant!=="outlined",style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(n.vars||n).shape.borderRadius,"&:focus":{borderRadius:(n.vars||n).shape.borderRadius},"&&&":{paddingRight:32}}}]})),pw=pe(mb,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:Yn,overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.select,r[l.variant],l.error&&r.error,{[`&.${Mp.multiple}`]:r.multiple}]}})({}),hb=pe("svg",{name:"MuiNativeSelect"})(({theme:n})=>({position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(n.vars||n).palette.action.active,[`&.${Mp.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:({ownerState:r})=>r.open,style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]})),mw=pe(hb,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.icon,l.variant&&r[`icon${de(l.variant)}`],l.open&&r.iconOpen]}})({}),hw=M.forwardRef(function(r,l){const{className:i,disabled:u,error:c,IconComponent:d,inputRef:p,variant:m="standard",...h}=r,y={...r,disabled:u,variant:m,error:c},S=dw(y);return b.jsxs(M.Fragment,{children:[b.jsx(pw,{ownerState:y,className:Me(S.select,i),disabled:u,ref:p||l,...h}),r.multiple?null:b.jsx(mw,{as:d,ownerState:y,className:S.icon})]})});var Bv;const gw=pe("fieldset",{name:"MuiNotchedOutlined",shouldForwardProp:Yn})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),yw=pe("legend",{name:"MuiNotchedOutlined",shouldForwardProp:Yn})(tt(({theme:n})=>({float:"unset",width:"auto",overflow:"hidden",variants:[{props:({ownerState:r})=>!r.withLabel,style:{padding:0,lineHeight:"11px",transition:n.transitions.create("width",{duration:150,easing:n.transitions.easing.easeOut})}},{props:({ownerState:r})=>r.withLabel,style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:n.transitions.create("max-width",{duration:50,easing:n.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:({ownerState:r})=>r.withLabel&&r.notched,style:{maxWidth:"100%",transition:n.transitions.create("max-width",{duration:100,easing:n.transitions.easing.easeOut,delay:50})}}]})));function vw(n){const{children:r,classes:l,className:i,label:u,notched:c,...d}=n,p=u!=null&&u!=="",m={...n,notched:c,withLabel:p};return b.jsx(gw,{"aria-hidden":!0,className:i,ownerState:m,...d,children:b.jsx(yw,{ownerState:m,children:p?b.jsx("span",{children:u}):Bv||(Bv=b.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"}))})})}const bw=n=>{const{classes:r}=n,i=Qe({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},tO,r);return{...r,...i}},Sw=pe(Qu,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiOutlinedInput",slot:"Root",overridesResolver:Xu})(tt(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(n.vars||n).shape.borderRadius,[`&:hover .${ia.notchedOutline}`]:{borderColor:(n.vars||n).palette.text.primary},"@media (hover: none)":{[`&:hover .${ia.notchedOutline}`]:{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}},[`&.${ia.focused} .${ia.notchedOutline}`]:{borderWidth:2},variants:[...Object.entries(n.palette).filter(Sn()).map(([l])=>({props:{color:l},style:{[`&.${ia.focused} .${ia.notchedOutline}`]:{borderColor:(n.vars||n).palette[l].main}}})),{props:{},style:{[`&.${ia.error} .${ia.notchedOutline}`]:{borderColor:(n.vars||n).palette.error.main},[`&.${ia.disabled} .${ia.notchedOutline}`]:{borderColor:(n.vars||n).palette.action.disabled}}},{props:({ownerState:l})=>l.startAdornment,style:{paddingLeft:14}},{props:({ownerState:l})=>l.endAdornment,style:{paddingRight:14}},{props:({ownerState:l})=>l.multiline,style:{padding:"16.5px 14px"}},{props:({ownerState:l,size:i})=>l.multiline&&i==="small",style:{padding:"8.5px 14px"}}]}})),xw=pe(vw,{name:"MuiOutlinedInput",slot:"NotchedOutline"})(tt(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}})),Cw=pe(Wu,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:Ku})(tt(({theme:n})=>({padding:"16.5px 14px",...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:({ownerState:r})=>r.multiline,style:{padding:0}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}}]}))),wp=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiOutlinedInput"}),{components:u={},fullWidth:c=!1,inputComponent:d="input",label:p,multiline:m=!1,notched:h,slots:y={},slotProps:S={},type:O="text",...w}=i,R=bw(i),E=Hr(),x=fo({props:i,muiFormControl:E,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),$={...i,color:x.color||"primary",disabled:x.disabled,error:x.error,focused:x.focused,formControl:E,fullWidth:c,hiddenLabel:x.hiddenLabel,multiline:m,size:x.size,type:O},B=y.root??u.Root??Sw,D=y.input??u.Input??Cw,[k,z]=wt("notchedOutline",{elementType:xw,className:R.notchedOutline,shouldForwardComponentProp:!0,ownerState:$,externalForwardedProps:{slots:y,slotProps:S},additionalProps:{label:p!=null&&p!==""&&x.required?b.jsxs(M.Fragment,{children:[p," ","*"]}):p}});return b.jsx(Ep,{slots:{root:B,input:D},slotProps:S,renderSuffix:N=>b.jsx(k,{...z,notched:typeof h<"u"?h:!!(N.startAdornment||N.filled||N.focused)}),fullWidth:c,inputComponent:d,multiline:m,ref:l,type:O,...w,classes:{...R,notchedOutline:null}})});wp.muiName="Input";function gb(n){return Xe("MuiSelect",n)}const vi=Ke("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]);var _v;const Tw=pe(mb,{name:"MuiSelect",slot:"Select",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[{[`&.${vi.select}`]:r.select},{[`&.${vi.select}`]:r[l.variant]},{[`&.${vi.error}`]:r.error},{[`&.${vi.multiple}`]:r.multiple}]}})({[`&.${vi.select}`]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),Ew=pe(hb,{name:"MuiSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.icon,l.variant&&r[`icon${de(l.variant)}`],l.open&&r.iconOpen]}})({}),Rw=pe("input",{shouldForwardProp:n=>B0(n)&&n!=="classes",name:"MuiSelect",slot:"NativeInput"})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function Lv(n,r){return typeof r=="object"&&r!==null?n===r:String(n)===String(r)}function Ow(n){return n==null||typeof n=="string"&&!n.trim()}const Mw=n=>{const{classes:r,variant:l,disabled:i,multiple:u,open:c,error:d}=n,p={select:["select",l,i&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${de(l)}`,c&&"iconOpen",i&&"disabled"],nativeInput:["nativeInput"]};return Qe(p,gb,r)},ww=M.forwardRef(function(r,l){const{"aria-describedby":i,"aria-label":u,autoFocus:c,autoWidth:d,children:p,className:m,defaultOpen:h,defaultValue:y,disabled:S,displayEmpty:O,error:w=!1,IconComponent:R,inputRef:E,labelId:x,MenuProps:$={},multiple:B,name:D,onBlur:k,onChange:z,onClose:N,onFocus:q,onOpen:G,open:K,readOnly:v,renderValue:_,required:V,SelectDisplayProps:ae={},tabIndex:ee,type:L,value:T,variant:H="standard",...Y}=r,[X,A]=il({controlled:T,default:y,name:"Select"}),[U,J]=il({controlled:K,default:h,name:"Select"}),ne=M.useRef(null),fe=M.useRef(null),[ue,le]=M.useState(null),{current:ve}=M.useRef(K!=null),[xe,be]=M.useState(),me=fn(l,E),Re=M.useCallback(Ce=>{fe.current=Ce,Ce&&le(Ce)},[]),Ne=ue?.parentNode;M.useImperativeHandle(me,()=>({focus:()=>{fe.current.focus()},node:ne.current,value:X}),[X]),M.useEffect(()=>{h&&U&&ue&&!ve&&(be(d?null:Ne.clientWidth),fe.current.focus())},[ue,d]),M.useEffect(()=>{c&&fe.current.focus()},[c]),M.useEffect(()=>{if(!x)return;const Ce=qn(fe.current).getElementById(x);if(Ce){const Pe=()=>{getSelection().isCollapsed&&fe.current.focus()};return Ce.addEventListener("click",Pe),()=>{Ce.removeEventListener("click",Pe)}}},[x]);const _e=(Ce,Pe)=>{Ce?G&&G(Pe):N&&N(Pe),ve||(be(d?null:Ne.clientWidth),J(Ce))},ke=Ce=>{Ce.button===0&&(Ce.preventDefault(),fe.current.focus(),_e(!0,Ce))},nt=Ce=>{_e(!1,Ce)},Ve=M.Children.toArray(p),at=Ce=>{const Pe=Ve.find(ot=>ot.props.value===Ce.target.value);Pe!==void 0&&(A(Pe.props.value),z&&z(Ce,Pe))},he=Ce=>Pe=>{let ot;if(Pe.currentTarget.hasAttribute("tabindex")){if(B){ot=Array.isArray(X)?X.slice():[];const Gn=X.indexOf(Ce.props.value);Gn===-1?ot.push(Ce.props.value):ot.splice(Gn,1)}else ot=Ce.props.value;if(Ce.props.onClick&&Ce.props.onClick(Pe),X!==ot&&(A(ot),z)){const Gn=Pe.nativeEvent||Pe,ha=new Gn.constructor(Gn.type,Gn);Object.defineProperty(ha,"target",{writable:!0,value:{value:ot,name:D}}),z(ha,Ce)}B||_e(!1,Pe)}},st=Ce=>{v||[" ","ArrowUp","ArrowDown","Enter"].includes(Ce.key)&&(Ce.preventDefault(),_e(!0,Ce))},Te=ue!==null&&U,Le=Ce=>{!Te&&k&&(Object.defineProperty(Ce,"target",{writable:!0,value:{value:X,name:D}}),k(Ce))};delete Y["aria-invalid"];let ye,It;const ut=[];let At=!1;(Mu({value:X})||O)&&(_?ye=_(X):At=!0);const rt=Ve.map(Ce=>{if(!M.isValidElement(Ce))return null;let Pe;if(B){if(!Array.isArray(X))throw new Error(or(2));Pe=X.some(ot=>Lv(ot,Ce.props.value)),Pe&&At&&ut.push(Ce.props.children)}else Pe=Lv(X,Ce.props.value),Pe&&At&&(It=Ce.props.children);return M.cloneElement(Ce,{"aria-selected":Pe?"true":"false",onClick:he(Ce),onKeyUp:ot=>{ot.key===" "&&ot.preventDefault(),Ce.props.onKeyUp&&Ce.props.onKeyUp(ot)},role:"option",selected:Pe,value:void 0,"data-value":Ce.props.value})});At&&(B?ut.length===0?ye=null:ye=ut.reduce((Ce,Pe,ot)=>(Ce.push(Pe),ot<ut.length-1&&Ce.push(", "),Ce),[]):ye=It);let je=xe;!d&&ve&&ue&&(je=Ne.clientWidth);let Ye;typeof ee<"u"?Ye=ee:Ye=S?null:0;const et=ae.id||(D?`mui-component-select-${D}`:void 0),vt={...r,variant:H,value:X,open:Te,error:w},Ae=Mw(vt),Gt={...$.PaperProps,...$.slotProps?.paper},xn={...$.MenuListProps,...$.slotProps?.list},Zt=ml();return b.jsxs(M.Fragment,{children:[b.jsx(Tw,{as:"div",ref:Re,tabIndex:Ye,role:"combobox","aria-controls":Te?Zt:void 0,"aria-disabled":S?"true":void 0,"aria-expanded":Te?"true":"false","aria-haspopup":"listbox","aria-label":u,"aria-labelledby":[x,et].filter(Boolean).join(" ")||void 0,"aria-describedby":i,"aria-required":V?"true":void 0,"aria-invalid":w?"true":void 0,onKeyDown:st,onMouseDown:S||v?null:ke,onBlur:Le,onFocus:q,...ae,ownerState:vt,className:Me(ae.className,Ae.select,m),id:et,children:Ow(ye)?_v||(_v=b.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):ye}),b.jsx(Rw,{"aria-invalid":w,value:Array.isArray(X)?X.join(","):X,name:D,ref:ne,"aria-hidden":!0,onChange:at,tabIndex:-1,disabled:S,className:Ae.nativeInput,autoFocus:c,required:V,...Y,ownerState:vt}),b.jsx(Ew,{as:R,className:Ae.icon,ownerState:vt}),b.jsx(cw,{id:`menu-${D||""}`,anchorEl:Ne,open:Te,onClose:nt,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...$,slotProps:{...$.slotProps,list:{"aria-labelledby":x,role:"listbox","aria-multiselectable":B?"true":void 0,disableListWrap:!0,id:Zt,...xn},paper:{...Gt,style:{minWidth:je,...Gt!=null?Gt.style:null}}},children:rt})]})}),Aw=n=>{const{classes:r}=n,i=Qe({root:["root"]},gb,r);return{...r,...i}},Ap={name:"MuiSelect",slot:"Root",shouldForwardProp:n=>Yn(n)&&n!=="variant"},zw=pe(Op,Ap)(""),Dw=pe(wp,Ap)(""),kw=pe(Rp,Ap)(""),yb=M.forwardRef(function(r,l){const i=Je({name:"MuiSelect",props:r}),{autoWidth:u=!1,children:c,classes:d={},className:p,defaultOpen:m=!1,displayEmpty:h=!1,IconComponent:y=sb,id:S,input:O,inputProps:w,label:R,labelId:E,MenuProps:x,multiple:$=!1,native:B=!1,onClose:D,onOpen:k,open:z,renderValue:N,SelectDisplayProps:q,variant:G="outlined",...K}=i,v=B?hw:ww,_=Hr(),V=fo({props:i,muiFormControl:_,states:["variant","error"]}),ae=V.variant||G,ee={...i,variant:ae,classes:d},L=Aw(ee),{root:T,...H}=L,Y=O||{standard:b.jsx(zw,{ownerState:ee}),outlined:b.jsx(Dw,{label:R,ownerState:ee}),filled:b.jsx(kw,{ownerState:ee})}[ae],X=fn(l,Gi(Y));return b.jsx(M.Fragment,{children:M.cloneElement(Y,{inputComponent:v,inputProps:{children:c,error:V.error,IconComponent:y,variant:ae,type:void 0,multiple:$,...B?{id:S}:{autoWidth:u,defaultOpen:m,displayEmpty:h,labelId:E,MenuProps:x,onClose:D,onOpen:k,open:z,renderValue:N,SelectDisplayProps:{id:S,...q}},...w,classes:w?hn(H,w.classes):H,...O?O.props.inputProps:{}},...($&&B||h)&&ae==="outlined"?{notched:!0}:{},ref:X,className:Me(Y.props.className,p,L.root),...!O&&{variant:ae},...K})})});yb.muiName="Select";const vb=M.createContext();function $w(n){return Xe("MuiTable",n)}Ke("MuiTable",["root","stickyHeader"]);const jw=n=>{const{classes:r,stickyHeader:l}=n;return Qe({root:["root",l&&"stickyHeader"]},$w,r)},Nw=pe("table",{name:"MuiTable",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.stickyHeader&&r.stickyHeader]}})(tt(({theme:n})=>({display:"table",width:"100%",borderCollapse:"collapse",borderSpacing:0,"& caption":{...n.typography.body2,padding:n.spacing(2),color:(n.vars||n).palette.text.secondary,textAlign:"left",captionSide:"bottom"},variants:[{props:({ownerState:r})=>r.stickyHeader,style:{borderCollapse:"separate"}}]}))),Hv="table",Bw=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTable"}),{className:u,component:c=Hv,padding:d="normal",size:p="medium",stickyHeader:m=!1,...h}=i,y={...i,component:c,padding:d,size:p,stickyHeader:m},S=jw(y),O=M.useMemo(()=>({padding:d,size:p,stickyHeader:m}),[d,p,m]);return b.jsx(vb.Provider,{value:O,children:b.jsx(Nw,{as:c,role:c===Hv?null:"table",ref:l,className:Me(S.root,u),ownerState:y,...h})})}),Fu=M.createContext();function _w(n){return Xe("MuiTableBody",n)}Ke("MuiTableBody",["root"]);const Lw=n=>{const{classes:r}=n;return Qe({root:["root"]},_w,r)},Hw=pe("tbody",{name:"MuiTableBody",slot:"Root"})({display:"table-row-group"}),Pw={variant:"body"},Pv="tbody",Uw=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableBody"}),{className:u,component:c=Pv,...d}=i,p={...i,component:c},m=Lw(p);return b.jsx(Fu.Provider,{value:Pw,children:b.jsx(Hw,{className:Me(m.root,u),as:c,ref:l,role:c===Pv?null:"rowgroup",ownerState:p,...d})})});function Iw(n){return Xe("MuiTableCell",n)}const qw=Ke("MuiTableCell",["root","head","body","footer","sizeSmall","sizeMedium","paddingCheckbox","paddingNone","alignLeft","alignCenter","alignRight","alignJustify","stickyHeader"]),Vw=n=>{const{classes:r,variant:l,align:i,padding:u,size:c,stickyHeader:d}=n,p={root:["root",l,d&&"stickyHeader",i!=="inherit"&&`align${de(i)}`,u!=="normal"&&`padding${de(u)}`,`size${de(c)}`]};return Qe(p,Iw,r)},Yw=pe("td",{name:"MuiTableCell",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[l.variant],r[`size${de(l.size)}`],l.padding!=="normal"&&r[`padding${de(l.padding)}`],l.align!=="inherit"&&r[`align${de(l.align)}`],l.stickyHeader&&r.stickyHeader]}})(tt(({theme:n})=>({...n.typography.body2,display:"table-cell",verticalAlign:"inherit",borderBottom:n.vars?`1px solid ${n.vars.palette.TableCell.border}`:`1px solid
    ${n.palette.mode==="light"?Iu(yt(n.palette.divider,1),.88):Uu(yt(n.palette.divider,1),.68)}`,textAlign:"left",padding:16,variants:[{props:{variant:"head"},style:{color:(n.vars||n).palette.text.primary,lineHeight:n.typography.pxToRem(24),fontWeight:n.typography.fontWeightMedium}},{props:{variant:"body"},style:{color:(n.vars||n).palette.text.primary}},{props:{variant:"footer"},style:{color:(n.vars||n).palette.text.secondary,lineHeight:n.typography.pxToRem(21),fontSize:n.typography.pxToRem(12)}},{props:{size:"small"},style:{padding:"6px 16px",[`&.${qw.paddingCheckbox}`]:{width:24,padding:"0 12px 0 16px","& > *":{padding:0}}}},{props:{padding:"checkbox"},style:{width:48,padding:"0 0 0 4px"}},{props:{padding:"none"},style:{padding:0}},{props:{align:"left"},style:{textAlign:"left"}},{props:{align:"center"},style:{textAlign:"center"}},{props:{align:"right"},style:{textAlign:"right",flexDirection:"row-reverse"}},{props:{align:"justify"},style:{textAlign:"justify"}},{props:({ownerState:r})=>r.stickyHeader,style:{position:"sticky",top:0,zIndex:2,backgroundColor:(n.vars||n).palette.background.default}}]}))),Da=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableCell"}),{align:u="inherit",className:c,component:d,padding:p,scope:m,size:h,sortDirection:y,variant:S,...O}=i,w=M.useContext(vb),R=M.useContext(Fu),E=R&&R.variant==="head";let x;d?x=d:x=E?"th":"td";let $=m;x==="td"?$=void 0:!$&&E&&($="col");const B=S||R&&R.variant,D={...i,align:u,component:x,padding:p||(w&&w.padding?w.padding:"normal"),size:h||(w&&w.size?w.size:"medium"),sortDirection:y,stickyHeader:B==="head"&&w&&w.stickyHeader,variant:B},k=Vw(D);let z=null;return y&&(z=y==="asc"?"ascending":"descending"),b.jsx(Yw,{as:x,ref:l,className:Me(k.root,c),"aria-sort":z,scope:$,ownerState:D,...O})});function Gw(n){return Xe("MuiTableContainer",n)}Ke("MuiTableContainer",["root"]);const Xw=n=>{const{classes:r}=n;return Qe({root:["root"]},Gw,r)},Kw=pe("div",{name:"MuiTableContainer",slot:"Root"})({width:"100%",overflowX:"auto"}),Qw=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableContainer"}),{className:u,component:c="div",...d}=i,p={...i,component:c},m=Xw(p);return b.jsx(Kw,{ref:l,as:c,className:Me(m.root,u),ownerState:p,...d})});function Ww(n){return Xe("MuiTableHead",n)}Ke("MuiTableHead",["root"]);const Fw=n=>{const{classes:r}=n;return Qe({root:["root"]},Ww,r)},Zw=pe("thead",{name:"MuiTableHead",slot:"Root"})({display:"table-header-group"}),Jw={variant:"head"},Uv="thead",eA=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableHead"}),{className:u,component:c=Uv,...d}=i,p={...i,component:c},m=Fw(p);return b.jsx(Fu.Provider,{value:Jw,children:b.jsx(Zw,{as:c,className:Me(m.root,u),ref:l,role:c===Uv?null:"rowgroup",ownerState:p,...d})})});function tA(n){return Xe("MuiTableRow",n)}const Iv=Ke("MuiTableRow",["root","selected","hover","head","footer"]),nA=n=>{const{classes:r,selected:l,hover:i,head:u,footer:c}=n;return Qe({root:["root",l&&"selected",i&&"hover",u&&"head",c&&"footer"]},tA,r)},aA=pe("tr",{name:"MuiTableRow",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.head&&r.head,l.footer&&r.footer]}})(tt(({theme:n})=>({color:"inherit",display:"table-row",verticalAlign:"middle",outline:0,[`&.${Iv.hover}:hover`]:{backgroundColor:(n.vars||n).palette.action.hover},[`&.${Iv.selected}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:yt(n.palette.primary.main,n.palette.action.selectedOpacity),"&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:yt(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity)}}}))),qv="tr",Vv=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableRow"}),{className:u,component:c=qv,hover:d=!1,selected:p=!1,...m}=i,h=M.useContext(Fu),y={...i,component:c,hover:d,selected:p,head:h&&h.variant==="head",footer:h&&h.variant==="footer"},S=nA(y);return b.jsx(aA,{as:c,ref:l,className:Me(S.root,u),role:c===qv?null:"row",ownerState:y,...m})});function rA(n){return Xe("MuiTextField",n)}Ke("MuiTextField",["root"]);const oA={standard:Op,filled:Rp,outlined:wp},lA=n=>{const{classes:r}=n;return Qe({root:["root"]},rA,r)},iA=pe(EM,{name:"MuiTextField",slot:"Root"})({}),Wt=M.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTextField"}),{autoComplete:u,autoFocus:c=!1,children:d,className:p,color:m="primary",defaultValue:h,disabled:y=!1,error:S=!1,FormHelperTextProps:O,fullWidth:w=!1,helperText:R,id:E,InputLabelProps:x,inputProps:$,InputProps:B,inputRef:D,label:k,maxRows:z,minRows:N,multiline:q=!1,name:G,onBlur:K,onChange:v,onFocus:_,placeholder:V,required:ae=!1,rows:ee,select:L=!1,SelectProps:T,slots:H={},slotProps:Y={},type:X,value:A,variant:U="outlined",...J}=i,ne={...i,autoFocus:c,color:m,disabled:y,error:S,fullWidth:w,multiline:q,required:ae,select:L,variant:U},fe=lA(ne),ue=ml(E),le=R&&ue?`${ue}-helper-text`:void 0,ve=k&&ue?`${ue}-label`:void 0,xe=oA[U],be={slots:H,slotProps:{input:B,inputLabel:x,htmlInput:$,formHelperText:O,select:T,...Y}},me={},Re=be.slotProps.inputLabel;U==="outlined"&&(Re&&typeof Re.shrink<"u"&&(me.notched=Re.shrink),me.label=k),L&&((!T||!T.native)&&(me.id=void 0),me["aria-describedby"]=void 0);const[Ne,_e]=wt("root",{elementType:iA,shouldForwardComponentProp:!0,externalForwardedProps:{...be,...J},ownerState:ne,className:Me(fe.root,p),ref:l,additionalProps:{disabled:y,error:S,fullWidth:w,required:ae,color:m,variant:U}}),[ke,nt]=wt("input",{elementType:xe,externalForwardedProps:be,additionalProps:me,ownerState:ne}),[Ve,at]=wt("inputLabel",{elementType:GM,externalForwardedProps:be,ownerState:ne}),[he,st]=wt("htmlInput",{elementType:"input",externalForwardedProps:be,ownerState:ne}),[Te,Le]=wt("formHelperText",{elementType:$M,externalForwardedProps:be,ownerState:ne}),[ye,It]=wt("select",{elementType:yb,externalForwardedProps:be,ownerState:ne}),ut=b.jsx(ke,{"aria-describedby":le,autoComplete:u,autoFocus:c,defaultValue:h,fullWidth:w,multiline:q,name:G,rows:ee,maxRows:z,minRows:N,type:X,value:A,id:ue,inputRef:D,onBlur:K,onChange:v,onFocus:_,placeholder:V,inputProps:st,slots:{input:H.htmlInput?he:void 0},...nt});return b.jsxs(Ne,{..._e,children:[k!=null&&k!==""&&b.jsx(Ve,{htmlFor:ue,id:ve,...at,children:k}),L?b.jsx(ye,{"aria-describedby":le,id:ue,labelId:ve,value:A,input:ut,...It,children:d}):ut,R&&b.jsx(Te,{id:le,...Le,children:R})]})}),sA=C0({themeId:Ta}),uA=An(b.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),cA=An(b.jsx("path",{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m2 14H7v-2h7zm3-4H7v-2h10zm0-4H7V7h10z"})),fA=An(b.jsx("path",{d:"M4 9h4v11H4zm12 4h4v7h-4zm-6-9h4v16h-4z"})),dA=An(b.jsx("path",{d:"M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 18H4V8h16z"})),pA=An([b.jsx("circle",{cx:"12",cy:"19",r:"2"},"0"),b.jsx("path",{d:"M10 3h4v12h-4z"},"1")]),mA=An(b.jsx("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"})),hA=An(b.jsx("path",{d:"M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"})),gA=An(b.jsx("path",{d:"M1 21h22L12 2zm12-3h-2v-2h2zm0-4h-2v-4h2z"}));var yu={exports:{}},yA=yu.exports,Yv;function vA(){return Yv||(Yv=1,function(n,r){(function(l,i){n.exports=i()})(yA,function(){var l=1e3,i=6e4,u=36e5,c="millisecond",d="second",p="minute",m="hour",h="day",y="week",S="month",O="quarter",w="year",R="date",E="Invalid Date",x=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,$=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,B={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(ee){var L=["th","st","nd","rd"],T=ee%100;return"["+ee+(L[(T-20)%10]||L[T]||L[0])+"]"}},D=function(ee,L,T){var H=String(ee);return!H||H.length>=L?ee:""+Array(L+1-H.length).join(T)+ee},k={s:D,z:function(ee){var L=-ee.utcOffset(),T=Math.abs(L),H=Math.floor(T/60),Y=T%60;return(L<=0?"+":"-")+D(H,2,"0")+":"+D(Y,2,"0")},m:function ee(L,T){if(L.date()<T.date())return-ee(T,L);var H=12*(T.year()-L.year())+(T.month()-L.month()),Y=L.clone().add(H,S),X=T-Y<0,A=L.clone().add(H+(X?-1:1),S);return+(-(H+(T-Y)/(X?Y-A:A-Y))||0)},a:function(ee){return ee<0?Math.ceil(ee)||0:Math.floor(ee)},p:function(ee){return{M:S,y:w,w:y,d:h,D:R,h:m,m:p,s:d,ms:c,Q:O}[ee]||String(ee||"").toLowerCase().replace(/s$/,"")},u:function(ee){return ee===void 0}},z="en",N={};N[z]=B;var q="$isDayjsObject",G=function(ee){return ee instanceof V||!(!ee||!ee[q])},K=function ee(L,T,H){var Y;if(!L)return z;if(typeof L=="string"){var X=L.toLowerCase();N[X]&&(Y=X),T&&(N[X]=T,Y=X);var A=L.split("-");if(!Y&&A.length>1)return ee(A[0])}else{var U=L.name;N[U]=L,Y=U}return!H&&Y&&(z=Y),Y||!H&&z},v=function(ee,L){if(G(ee))return ee.clone();var T=typeof L=="object"?L:{};return T.date=ee,T.args=arguments,new V(T)},_=k;_.l=K,_.i=G,_.w=function(ee,L){return v(ee,{locale:L.$L,utc:L.$u,x:L.$x,$offset:L.$offset})};var V=function(){function ee(T){this.$L=K(T.locale,null,!0),this.parse(T),this.$x=this.$x||T.x||{},this[q]=!0}var L=ee.prototype;return L.parse=function(T){this.$d=function(H){var Y=H.date,X=H.utc;if(Y===null)return new Date(NaN);if(_.u(Y))return new Date;if(Y instanceof Date)return new Date(Y);if(typeof Y=="string"&&!/Z$/i.test(Y)){var A=Y.match(x);if(A){var U=A[2]-1||0,J=(A[7]||"0").substring(0,3);return X?new Date(Date.UTC(A[1],U,A[3]||1,A[4]||0,A[5]||0,A[6]||0,J)):new Date(A[1],U,A[3]||1,A[4]||0,A[5]||0,A[6]||0,J)}}return new Date(Y)}(T),this.init()},L.init=function(){var T=this.$d;this.$y=T.getFullYear(),this.$M=T.getMonth(),this.$D=T.getDate(),this.$W=T.getDay(),this.$H=T.getHours(),this.$m=T.getMinutes(),this.$s=T.getSeconds(),this.$ms=T.getMilliseconds()},L.$utils=function(){return _},L.isValid=function(){return this.$d.toString()!==E},L.isSame=function(T,H){var Y=v(T);return this.startOf(H)<=Y&&Y<=this.endOf(H)},L.isAfter=function(T,H){return v(T)<this.startOf(H)},L.isBefore=function(T,H){return this.endOf(H)<v(T)},L.$g=function(T,H,Y){return _.u(T)?this[H]:this.set(Y,T)},L.unix=function(){return Math.floor(this.valueOf()/1e3)},L.valueOf=function(){return this.$d.getTime()},L.startOf=function(T,H){var Y=this,X=!!_.u(H)||H,A=_.p(T),U=function(be,me){var Re=_.w(Y.$u?Date.UTC(Y.$y,me,be):new Date(Y.$y,me,be),Y);return X?Re:Re.endOf(h)},J=function(be,me){return _.w(Y.toDate()[be].apply(Y.toDate("s"),(X?[0,0,0,0]:[23,59,59,999]).slice(me)),Y)},ne=this.$W,fe=this.$M,ue=this.$D,le="set"+(this.$u?"UTC":"");switch(A){case w:return X?U(1,0):U(31,11);case S:return X?U(1,fe):U(0,fe+1);case y:var ve=this.$locale().weekStart||0,xe=(ne<ve?ne+7:ne)-ve;return U(X?ue-xe:ue+(6-xe),fe);case h:case R:return J(le+"Hours",0);case m:return J(le+"Minutes",1);case p:return J(le+"Seconds",2);case d:return J(le+"Milliseconds",3);default:return this.clone()}},L.endOf=function(T){return this.startOf(T,!1)},L.$set=function(T,H){var Y,X=_.p(T),A="set"+(this.$u?"UTC":""),U=(Y={},Y[h]=A+"Date",Y[R]=A+"Date",Y[S]=A+"Month",Y[w]=A+"FullYear",Y[m]=A+"Hours",Y[p]=A+"Minutes",Y[d]=A+"Seconds",Y[c]=A+"Milliseconds",Y)[X],J=X===h?this.$D+(H-this.$W):H;if(X===S||X===w){var ne=this.clone().set(R,1);ne.$d[U](J),ne.init(),this.$d=ne.set(R,Math.min(this.$D,ne.daysInMonth())).$d}else U&&this.$d[U](J);return this.init(),this},L.set=function(T,H){return this.clone().$set(T,H)},L.get=function(T){return this[_.p(T)]()},L.add=function(T,H){var Y,X=this;T=Number(T);var A=_.p(H),U=function(fe){var ue=v(X);return _.w(ue.date(ue.date()+Math.round(fe*T)),X)};if(A===S)return this.set(S,this.$M+T);if(A===w)return this.set(w,this.$y+T);if(A===h)return U(1);if(A===y)return U(7);var J=(Y={},Y[p]=i,Y[m]=u,Y[d]=l,Y)[A]||1,ne=this.$d.getTime()+T*J;return _.w(ne,this)},L.subtract=function(T,H){return this.add(-1*T,H)},L.format=function(T){var H=this,Y=this.$locale();if(!this.isValid())return Y.invalidDate||E;var X=T||"YYYY-MM-DDTHH:mm:ssZ",A=_.z(this),U=this.$H,J=this.$m,ne=this.$M,fe=Y.weekdays,ue=Y.months,le=Y.meridiem,ve=function(me,Re,Ne,_e){return me&&(me[Re]||me(H,X))||Ne[Re].slice(0,_e)},xe=function(me){return _.s(U%12||12,me,"0")},be=le||function(me,Re,Ne){var _e=me<12?"AM":"PM";return Ne?_e.toLowerCase():_e};return X.replace($,function(me,Re){return Re||function(Ne){switch(Ne){case"YY":return String(H.$y).slice(-2);case"YYYY":return _.s(H.$y,4,"0");case"M":return ne+1;case"MM":return _.s(ne+1,2,"0");case"MMM":return ve(Y.monthsShort,ne,ue,3);case"MMMM":return ve(ue,ne);case"D":return H.$D;case"DD":return _.s(H.$D,2,"0");case"d":return String(H.$W);case"dd":return ve(Y.weekdaysMin,H.$W,fe,2);case"ddd":return ve(Y.weekdaysShort,H.$W,fe,3);case"dddd":return fe[H.$W];case"H":return String(U);case"HH":return _.s(U,2,"0");case"h":return xe(1);case"hh":return xe(2);case"a":return be(U,J,!0);case"A":return be(U,J,!1);case"m":return String(J);case"mm":return _.s(J,2,"0");case"s":return String(H.$s);case"ss":return _.s(H.$s,2,"0");case"SSS":return _.s(H.$ms,3,"0");case"Z":return A}return null}(me)||A.replace(":","")})},L.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},L.diff=function(T,H,Y){var X,A=this,U=_.p(H),J=v(T),ne=(J.utcOffset()-this.utcOffset())*i,fe=this-J,ue=function(){return _.m(A,J)};switch(U){case w:X=ue()/12;break;case S:X=ue();break;case O:X=ue()/3;break;case y:X=(fe-ne)/6048e5;break;case h:X=(fe-ne)/864e5;break;case m:X=fe/u;break;case p:X=fe/i;break;case d:X=fe/l;break;default:X=fe}return Y?X:_.a(X)},L.daysInMonth=function(){return this.endOf(S).$D},L.$locale=function(){return N[this.$L]},L.locale=function(T,H){if(!T)return this.$L;var Y=this.clone(),X=K(T,H,!0);return X&&(Y.$L=X),Y},L.clone=function(){return _.w(this.$d,this)},L.toDate=function(){return new Date(this.valueOf())},L.toJSON=function(){return this.isValid()?this.toISOString():null},L.toISOString=function(){return this.$d.toISOString()},L.toString=function(){return this.$d.toUTCString()},ee}(),ae=V.prototype;return v.prototype=ae,[["$ms",c],["$s",d],["$m",p],["$H",m],["$W",h],["$M",S],["$y",w],["$D",R]].forEach(function(ee){ae[ee[1]]=function(L){return this.$g(L,ee[0],ee[1])}}),v.extend=function(ee,L){return ee.$i||(ee(L,V,v),ee.$i=!0),v},v.locale=K,v.isDayjs=G,v.unix=function(ee){return v(1e3*ee)},v.en=N[z],v.Ls=N,v.p={},v})}(yu)),yu.exports}var bA=vA();const ka=Vd(bA),bb=Vu({palette:{primary:{main:"#2EC0CB"},secondary:{main:"#6c757d"},success:{main:"#28a745",light:"#d4edda",contrastText:"#155724"},info:{main:"#17a2b8"},error:{main:"#dc3545"},background:{default:"#f4f6f8",paper:"#ffffff"}},typography:{fontFamily:'Roobert, "Inter", sans-serif'},components:{MuiCssBaseline:{styleOverrides:`
        body {
          font-family: 'Roobert', "Inter", sans-serif;
          background-color: #f4f6f8; /* Explicitly set a light background for the body */
        }
      `},MuiButton:{styleOverrides:{root:{borderRadius:8}}},MuiPaper:{styleOverrides:{root:{borderRadius:8}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8}}}},MuiAutocomplete:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8}}}}}}),Gv=["IOCS","SWServices","Total-Exp","Tru-North"],SA=["Project Management","Training","Meeting","Documentation"],xA=["Discovery Phase","Not Yet Assigned","On Hold","Waiting for Customer Approval","Waiting for Internal Approval"],CA=["Work in Progress","On Hold","Completed","Request for PCD Change Approval","Request for Closure Approval"],TA=["admin","Bibhash Kanti Roy","Bryce Connors","David Campbell","John Talbert","Nikil Ram D","Niraj kumar Mishra","Prasad H","Ravi Tomar","Sreekrishna Narayana","Stuart Mckay","Thomas Remmel","Vishwanath A.B"],EA=["Daily","Weekly","Monthly","Yearly"],Xv=[{id:1,fileName:"ProjectPlan.pdf",description:"Initial project plan",uploadedBy:"admin",uploadedDate:"2024-06-15"},{id:2,fileName:"RequirementsDoc.docx",description:"Detailed requirements",uploadedBy:"John Talbert",uploadedDate:"2024-06-20"}];function RA(){const n=bb,r=sA(n.breakpoints.down("sm")),[l,i]=M.useState(null),[u,c]=M.useState([{id:1,taskId:"TSK-001",taskName:"Design User Interface",taskDescription:"Create wireframes and mockups for the new dashboard",priority:"High",category:"Design",project:"Dashboard Redesign",division:"IT",customer:"Internal",department:"Development",taskType:"Design",currentStatus:"In Progress",taskStatus:"Active",assignedTo:"John Doe",estimatedHours:"40",actualHours:"25",taskPCD:"2024-01-15",startDate:"2024-01-10",endDate:"2024-01-20",repeatTask:!1,repeatFrequency:"",comments:"Initial design phase",completed:!1},{id:2,taskId:"TSK-002",taskName:"Database Optimization",taskDescription:"Optimize database queries for better performance",priority:"Medium",category:"Development",project:"Performance Enhancement",division:"IT",customer:"Internal",department:"Database",taskType:"Development",currentStatus:"Pending",taskStatus:"Active",assignedTo:"Jane Smith",estimatedHours:"20",actualHours:"0",taskPCD:"2024-01-25",startDate:"2024-01-20",endDate:"2024-01-30",repeatTask:!1,repeatFrequency:"",comments:"Focus on slow queries",completed:!1},{id:3,taskId:"TSK-003",taskName:"Security Audit",taskDescription:"Conduct comprehensive security audit of the application",priority:"High",category:"Security",project:"Security Review",division:"IT",customer:"Compliance Team",department:"Security",taskType:"Audit",currentStatus:"Completed",taskStatus:"Closed",assignedTo:"Mike Johnson",estimatedHours:"30",actualHours:"28",taskPCD:"2024-01-05",startDate:"2024-01-01",endDate:"2024-01-10",repeatTask:!1,repeatFrequency:"",comments:"All vulnerabilities addressed",completed:!0}]),[d,p]=M.useState({id:null,taskId:"",taskName:"",taskDescription:"",project:null,division:"",customer:"",taskType:null,currentStatus:null,taskStatus:null,assignedTo:null,estimatedHours:"",actualHours:"",taskPCD:"",startDate:"",endDate:"",repeatTask:!1,repeatFrequency:null,comments:"",completed:!1}),m=x=>{const{name:$,value:B}=x.target;p(D=>({...D,[$]:B}))},h=(x,$)=>{p(B=>({...B,[x]:$}))},y=x=>{const{name:$,value:B}=x.target;p(D=>({...D,[$]:B}))},S=x=>{const $=x.target.checked;p(B=>({...B,repeatTask:$,repeatFrequency:$?B.repeatFrequency:null}))},O=()=>{if(d.taskName.trim()===""){console.log("Task Name cannot be empty.");return}d.id?c(u.map(x=>x.id===d.id?{...x,...d}:x)):c([...u,{...d,id:Date.now(),completed:!1}]),E()},w=x=>{p({...x,taskPCD:x.taskPCD?ka(x.taskPCD).format("YYYY-MM-DD"):"",startDate:x.startDate?ka(x.startDate).format("YYYY-MM-DD"):"",endDate:x.endDate?ka(x.endDate).format("YYYY-MM-DD"):""})},R=x=>{i(x.id),w(x)},E=()=>{i(null),p({id:null,taskId:"",taskName:"",taskDescription:"",project:null,division:"",customer:"",taskType:null,currentStatus:null,taskStatus:null,assignedTo:null,estimatedHours:"",actualHours:"",taskPCD:"",startDate:"",endDate:"",repeatTask:!1,repeatFrequency:null,comments:"",completed:!1})};return b.jsxs(Ht,{sx:{width:"100vw",height:"100vh",backgroundColor:"#f8f9fa",overflow:"hidden",display:"flex",flexDirection:"column"},children:[b.jsx(Ht,{sx:{px:{xs:1,sm:2,md:3},py:{xs:1,sm:1.5,md:2},borderBottom:"1px solid #e0e0e0",backgroundColor:"white",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:b.jsx(qe,{variant:"h4",component:"h1",align:"center",sx:{color:n.palette.primary.main,fontWeight:700,fontSize:{xs:"1.5rem",sm:"1.8rem",md:"2.2rem",lg:"2.5rem"},fontFamily:"Roobert, sans-serif",margin:0},children:"My Detailed Task Tracker"})}),b.jsx(Ht,{sx:{flex:1,overflow:"hidden",px:{xs:1,sm:2,md:3},py:{xs:1,sm:1.5,md:2}},children:b.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},sx:{width:"100%",height:"100%",overflow:"hidden"},children:[b.jsxs(we,{item:!0,xs:12,lg:8,xl:9,sx:{height:"100%",overflow:"auto","&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#c1c1c1",borderRadius:"3px"}},children:[b.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[l&&b.jsx(we,{item:!0,xs:12,children:b.jsxs(un,{elevation:1,sx:{p:2,backgroundColor:"#e8f5e8",borderLeft:"4px solid #4caf50"},children:[b.jsxs(qe,{variant:"body1",sx:{fontWeight:600,color:"#2e7d32"},children:["📝 Editing Task: ",u.find(x=>x.id===l)?.taskName||"Unknown Task"]}),b.jsx(qe,{variant:"body2",sx:{color:"#2e7d32",mt:.5},children:'Click "Add Task" to save changes or "Clear Form" to cancel editing.'})]})}),b.jsx(we,{item:!0,xs:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:2,sm:2.5,md:3}},children:[b.jsx(qe,{variant:"h6",component:"h2",sx:{mb:2,color:n.palette.primary.dark,fontSize:{xs:"1.1rem",md:"1.2rem"},fontWeight:600},children:"Basic Task Information"}),b.jsxs(we,{container:!0,spacing:{xs:2,sm:2.5,md:3},alignItems:"flex-start",children:[b.jsx(we,{item:!0,xs:12,sm:6,children:b.jsx(Wt,{fullWidth:!0,label:"Task ID",name:"taskId",value:d.taskId,onChange:m,variant:"outlined",size:"small",sx:{"& .MuiInputLabel-root":{fontWeight:500}}})}),b.jsx(we,{item:!0,xs:12,sm:6,children:b.jsx(Wt,{fullWidth:!0,label:"Task Name *",name:"taskName",value:d.taskName,onChange:m,variant:"outlined",size:"small",sx:{"& .MuiInputLabel-root":{fontWeight:500}}})}),b.jsx(we,{item:!0,xs:12,sm:6,children:b.jsx(Wt,{fullWidth:!0,label:"Priority",name:"priority",value:d.priority||"",onChange:m,variant:"outlined",placeholder:"High/Medium/Low",size:"small",sx:{"& .MuiInputLabel-root":{fontWeight:500}}})}),b.jsx(we,{item:!0,xs:12,sm:6,children:b.jsx(Wt,{fullWidth:!0,label:"Category",name:"category",value:d.category||"",onChange:m,variant:"outlined",placeholder:"Enter category",size:"small",sx:{"& .MuiInputLabel-root":{fontWeight:500}}})}),b.jsx(we,{item:!0,xs:12,children:b.jsx(Wt,{fullWidth:!0,label:"Task Description *",name:"taskDescription",value:d.taskDescription,onChange:m,variant:"outlined",multiline:!0,rows:3,sx:{"& .MuiInputLabel-root":{fontWeight:500}}})})]})]})}),b.jsx(we,{item:!0,xs:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:2,sm:2.5,md:3}},children:[b.jsx(qe,{variant:"h6",component:"h2",sx:{mb:2,color:n.palette.primary.dark,fontSize:{xs:"1.1rem",md:"1.2rem"},fontWeight:600},children:"Project & Organizational Details"}),b.jsxs(we,{container:!0,spacing:{xs:2,sm:2.5,md:3},alignItems:"flex-start",children:[b.jsx(we,{item:!0,xs:12,sm:6,children:b.jsx(nl,{fullWidth:!0,options:Gv,value:d.project,onChange:(x,$)=>h("project",$),renderInput:x=>b.jsx(Wt,{...x,label:"Project *",variant:"outlined",size:"small",sx:{"& .MuiInputLabel-root":{fontWeight:500}}})})}),b.jsx(we,{item:!0,xs:12,sm:6,children:b.jsx(Wt,{fullWidth:!0,label:"Division",name:"division",value:d.division,onChange:m,variant:"outlined",size:"small",sx:{"& .MuiInputLabel-root":{fontWeight:500}}})}),b.jsx(we,{item:!0,xs:12,sm:6,children:b.jsx(Wt,{fullWidth:!0,label:"Customer",name:"customer",value:d.customer,onChange:m,variant:"outlined",size:"small",sx:{"& .MuiInputLabel-root":{fontWeight:500}}})}),b.jsx(we,{item:!0,xs:12,sm:6,children:b.jsx(Wt,{fullWidth:!0,label:"Department",name:"department",value:d.department||"",onChange:m,variant:"outlined",placeholder:"Enter department",size:"small",sx:{"& .MuiInputLabel-root":{fontWeight:500}}})})]})]})}),b.jsx(we,{item:!0,xs:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5}},children:[b.jsx(qe,{variant:"h6",component:"h2",sx:{mb:1.5,color:n.palette.primary.dark,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Task Classification & Assignment"}),b.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(nl,{fullWidth:!0,options:SA,value:d.taskType,onChange:(x,$)=>h("taskType",$),renderInput:x=>b.jsx(Wt,{...x,label:"Task Type *",variant:"outlined"})})}),b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(nl,{fullWidth:!0,options:xA,value:d.currentStatus,onChange:(x,$)=>h("currentStatus",$),renderInput:x=>b.jsx(Wt,{...x,label:"Current Status *",variant:"outlined"})})}),b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(nl,{fullWidth:!0,options:CA,value:d.taskStatus,onChange:(x,$)=>h("taskStatus",$),renderInput:x=>b.jsx(Wt,{...x,label:"Task Status",variant:"outlined"})})}),b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(nl,{fullWidth:!0,options:TA,value:d.assignedTo,onChange:(x,$)=>h("assignedTo",$),renderInput:x=>b.jsx(Wt,{...x,label:"Assigned To",variant:"outlined"})})})]})]})}),b.jsx(we,{item:!0,xs:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5}},children:[b.jsx(qe,{variant:"h6",component:"h2",sx:{mb:1.5,color:n.palette.primary.dark,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Scheduling & Effort"}),b.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(Wt,{fullWidth:!0,label:"Estimated Hours (hh:mm) *",name:"estimatedHours",value:d.estimatedHours,onChange:m,variant:"outlined",placeholder:"HH:MM"})}),b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(Wt,{fullWidth:!0,label:"Actual Hours",name:"actualHours",value:d.actualHours,onChange:m,variant:"outlined",placeholder:"HH:MM"})}),b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(Wt,{fullWidth:!0,label:"Task PCD *",name:"taskPCD",type:"date",value:d.taskPCD,onChange:y,variant:"outlined",InputLabelProps:{shrink:!0}})}),b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(Wt,{fullWidth:!0,label:"Start Date",name:"startDate",type:"date",value:d.startDate,onChange:y,variant:"outlined",InputLabelProps:{shrink:!0}})}),b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(Wt,{fullWidth:!0,label:"End Date",name:"endDate",type:"date",value:d.endDate,onChange:y,variant:"outlined",InputLabelProps:{shrink:!0}})}),b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(AM,{control:b.jsx(GO,{checked:d.repeatTask,onChange:S,name:"repeatTask",color:"primary"}),label:"Repeat Task"})}),d.repeatTask&&b.jsx(we,{item:!0,xs:12,sm:6,md:3,children:b.jsx(nl,{fullWidth:!0,options:EA,value:d.repeatFrequency,onChange:(x,$)=>h("repeatFrequency",$),renderInput:x=>b.jsx(Wt,{...x,label:"Repeat Frequency",variant:"outlined"})})})]})]})}),b.jsx(we,{item:!0,xs:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5}},children:[b.jsx(qe,{variant:"h6",component:"h2",sx:{mb:1.5,color:n.palette.primary.dark,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Comments"}),b.jsx(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:b.jsx(we,{item:!0,xs:12,children:b.jsx(Wt,{fullWidth:!0,label:"Comments",name:"comments",value:d.comments,onChange:m,variant:"outlined",multiline:!0,rows:2,size:r?"small":"medium"})})})]})}),b.jsx(we,{item:!0,xs:12,children:b.jsxs(Ht,{sx:{display:"flex",gap:2,flexDirection:r?"column":"row",justifyContent:"flex-end",mt:2},children:[b.jsx(Ov,{variant:"contained",color:"primary",startIcon:b.jsx(uA,{}),onClick:O,fullWidth:r,children:d.id?"Update Task":"Add Task"}),b.jsx(Ov,{variant:"outlined",color:"secondary",startIcon:b.jsx(mA,{}),onClick:E,fullWidth:r,children:"Clear Form"})]})})]})," "]})," ",b.jsx(we,{item:!0,xs:12,lg:4,xl:3,sx:{height:"100%",overflow:"auto","&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#c1c1c1",borderRadius:"3px"}},children:b.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[b.jsx(we,{item:!0,xs:12,sm:6,lg:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[b.jsxs(qe,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:n.palette.primary.dark,display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[b.jsx(dA,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Today's Tasks"]}),u.filter(x=>{const $=ka().format("YYYY-MM-DD");return x.startDate===$||x.taskPCD===$}).length===0?b.jsx(qe,{variant:"body2",color:"textSecondary",children:"No tasks scheduled for today"}):b.jsx(Ht,{children:u.filter(x=>{const $=ka().format("YYYY-MM-DD");return x.startDate===$||x.taskPCD===$}).slice(0,3).map(x=>b.jsxs(Ht,{sx:{mb:1,p:1,backgroundColor:"#f5f5f5",borderRadius:1},children:[b.jsx(qe,{variant:"body2",fontWeight:"bold",children:x.taskName}),b.jsx(qe,{variant:"caption",color:"textSecondary",children:x.project?.label||"No Project"})]},x.id))})]})}),b.jsx(we,{item:!0,xs:12,sm:6,lg:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[b.jsxs(qe,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:n.palette.primary.dark,display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[b.jsx(pA,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Priority Breakdown"]}),b.jsx(Ht,{children:["High","Medium","Low"].map(x=>{const $=u.filter(D=>D.priority===x&&!D.completed).length,B=x==="High"?"#f44336":x==="Medium"?"#ff9800":"#4caf50";return b.jsxs(Ht,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[b.jsxs(Ht,{sx:{display:"flex",alignItems:"center"},children:[b.jsx(Ht,{sx:{width:12,height:12,backgroundColor:B,borderRadius:"50%",mr:1}}),b.jsxs(qe,{variant:"body2",children:[x," Priority"]})]}),b.jsx(qe,{variant:"body2",fontWeight:"bold",children:$})]},x)})})]})}),b.jsx(we,{item:!0,xs:12,sm:6,lg:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[b.jsxs(qe,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:n.palette.primary.dark,display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[b.jsx(cA,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Pending by Project"]}),b.jsx(Ht,{children:Gv.slice(0,4).map(x=>{const $=u.filter(B=>B.project?.value===x.value&&!B.completed).length;return b.jsxs(Ht,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[b.jsx(qe,{variant:"body2",sx:{flex:1,overflow:"hidden",textOverflow:"ellipsis"},children:x.label}),b.jsx(qe,{variant:"body2",fontWeight:"bold",color:$>0?"error.main":"success.main",children:$})]},x.value)})})]})}),b.jsx(we,{item:!0,xs:12,sm:6,lg:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[b.jsxs(qe,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:"#f44336",display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[b.jsx(gA,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Urgent Tasks"]}),u.filter(x=>{const $=ka(x.taskPCD),B=ka();return!x.completed&&$.isValid()&&$.diff(B,"days")<=2}).length===0?b.jsx(qe,{variant:"body2",color:"textSecondary",children:"No urgent tasks"}):b.jsx(Ht,{children:u.filter(x=>{const $=ka(x.taskPCD),B=ka();return!x.completed&&$.isValid()&&$.diff(B,"days")<=2}).slice(0,3).map(x=>b.jsxs(Ht,{sx:{mb:1,p:1,backgroundColor:"#ffebee",borderRadius:1,border:"1px solid #ffcdd2"},children:[b.jsx(qe,{variant:"body2",fontWeight:"bold",color:"error.main",children:x.taskName}),b.jsxs(qe,{variant:"caption",color:"textSecondary",children:["Due: ",ka(x.taskPCD).format("MMM DD, YYYY")]})]},x.id))})]})}),b.jsx(we,{item:!0,xs:12,sm:12,lg:12,children:b.jsxs(un,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[b.jsxs(qe,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:n.palette.primary.dark,display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[b.jsx(fA,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Quick Stats"]}),b.jsxs(we,{container:!0,spacing:2,children:[b.jsx(we,{item:!0,xs:6,children:b.jsxs(Ht,{sx:{textAlign:"center",p:1,backgroundColor:"#e3f2fd",borderRadius:1},children:[b.jsx(qe,{variant:"h4",fontWeight:"bold",color:"primary.main",children:u.length}),b.jsx(qe,{variant:"caption",color:"textSecondary",children:"Total Tasks"})]})}),b.jsx(we,{item:!0,xs:6,children:b.jsxs(Ht,{sx:{textAlign:"center",p:1,backgroundColor:"#e8f5e8",borderRadius:1},children:[b.jsx(qe,{variant:"h4",fontWeight:"bold",color:"success.main",children:u.filter(x=>x.completed).length}),b.jsx(qe,{variant:"caption",color:"textSecondary",children:"Completed"})]})}),b.jsx(we,{item:!0,xs:6,children:b.jsxs(Ht,{sx:{textAlign:"center",p:1,backgroundColor:"#fff3e0",borderRadius:1},children:[b.jsx(qe,{variant:"h4",fontWeight:"bold",color:"warning.main",children:u.filter(x=>!x.completed).length}),b.jsx(qe,{variant:"caption",color:"textSecondary",children:"Pending"})]})}),b.jsx(we,{item:!0,xs:6,children:b.jsxs(Ht,{sx:{textAlign:"center",p:1,backgroundColor:"#fce4ec",borderRadius:1},children:[b.jsxs(qe,{variant:"h4",fontWeight:"bold",color:"error.main",children:[Math.round(u.filter(x=>x.completed).length/Math.max(u.length,1)*100),"%"]}),b.jsx(qe,{variant:"caption",color:"textSecondary",children:"Progress"})]})})]})]})})]})})," ",b.jsx(we,{item:!0,xs:12,children:b.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[b.jsx(we,{item:!0,xs:12,md:6,children:b.jsxs(un,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[b.jsx(qe,{variant:"h6",component:"h3",sx:{mb:1.5,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Attachment Details"}),Xv.length===0?b.jsx(qe,{variant:"body2",color:"textSecondary",children:"No attachments available."}):b.jsx(Qw,{sx:{maxHeight:200},children:b.jsxs(Bw,{size:"small",children:[b.jsx(eA,{children:b.jsxs(Vv,{children:[b.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"Sl."}),b.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"Select"}),b.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"View"}),b.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"File Name"}),b.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"Description"})]})}),b.jsx(Uw,{children:Xv.slice(0,3).map((x,$)=>b.jsxs(Vv,{children:[b.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:$+1}),b.jsx(Da,{sx:{py:.5},children:b.jsx("input",{type:"checkbox"})}),b.jsx(Da,{sx:{py:.5},children:b.jsx(hp,{size:"small","aria-label":"view attachment",children:b.jsx(hA,{})})}),b.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:x.fileName}),b.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:x.description})]},x.id))})]})})]})}),b.jsx(we,{item:!0,xs:12,md:6,children:b.jsxs(un,{elevation:2,sx:{p:{xs:2,sm:2.5,md:3},height:"fit-content"},children:[b.jsx(qe,{variant:"h6",component:"h3",sx:{mb:2,fontSize:{xs:"1.1rem",md:"1.2rem"},fontWeight:600,color:n.palette.primary.dark},children:"Your Tasks"}),u.length===0?b.jsx(qe,{variant:"body2",color:"textSecondary",sx:{textAlign:"center",py:3},children:"No tasks yet! Add a new task above to get started."}):b.jsx(Ht,{sx:{maxHeight:400,overflow:"auto"},children:b.jsx(we,{container:!0,spacing:2,children:u.slice(0,3).map(x=>b.jsx(we,{item:!0,xs:12,children:b.jsx(un,{elevation:l===x.id?3:1,onClick:()=>R(x),sx:{p:2,cursor:"pointer",borderLeft:`4px solid ${x.priority==="High"?"#f44336":x.priority==="Medium"?"#ff9800":x.priority==="Low"?"#4caf50":"#9e9e9e"}`,backgroundColor:l===x.id?"#e3f2fd":"white",transition:"all 0.2s ease-in-out","&:hover":{transform:"translateX(4px)",boxShadow:n.shadows[4],backgroundColor:l===x.id?"#e3f2fd":"#f8f9fa"}},children:b.jsx(Ht,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:b.jsxs(Ht,{sx:{flex:1},children:[b.jsxs(qe,{variant:"subtitle1",sx:{fontWeight:600,mb:.5,textDecoration:x.completed?"line-through":"none",color:x.completed?"text.secondary":"text.primary"},children:[x.taskName,x.taskId&&b.jsxs(qe,{component:"span",variant:"body2",sx:{ml:1,color:"text.secondary"},children:["#",x.taskId]})]}),b.jsxs(Ht,{sx:{display:"flex",alignItems:"center",gap:1},children:[b.jsx(Hd,{label:x.priority||"No Priority",size:"small",color:x.priority==="High"?"error":x.priority==="Medium"?"warning":x.priority==="Low"?"success":"default",sx:{fontSize:"0.75rem"}}),x.completed&&b.jsx(Hd,{label:"Completed",size:"small",color:"success",variant:"outlined",sx:{fontSize:"0.75rem"}})]})]})})})},x.id))})})]})})]})})]})})]})}function OA(){return b.jsxs(ST,{theme:bb,children:[b.jsx(FO,{}),b.jsx(RA,{})]})}NS.createRoot(document.getElementById("root")).render(b.jsx(M.StrictMode,{children:b.jsx(OA,{})}));
