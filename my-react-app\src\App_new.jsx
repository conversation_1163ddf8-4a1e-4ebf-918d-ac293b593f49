import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  TextField,
  Button,
  Grid,
  Paper,
  IconButton,
  useMediaQuery,
  CssBaseline,
  FormControlLabel,
  FormControl,
  FormLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  CalendarToday as CalendarTodayIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
  UploadFile as UploadFileIcon,
  PriorityHigh as PriorityHighIcon,
  Assignment as AssignmentIcon,
  Warning as WarningIcon,
  BarChart as BarChartIcon,
} from '@mui/icons-material';
import Autocomplete from '@mui/material/Autocomplete';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import dayjs from 'dayjs';

// Create a custom theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#2196f3',
      dark: '#1976d2',
    },
    secondary: {
      main: '#f50057',
    },
  },
  typography: {
    fontFamily: 'Roobert, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
});

// Sample data for dropdowns
const projects = [
  { value: 'project1', label: 'Dashboard Redesign' },
  { value: 'project2', label: 'Mobile App Development' },
  { value: 'project3', label: 'API Integration' },
  { value: 'project4', label: 'Database Optimization' },
];

const taskTypes = [
  { value: 'development', label: 'Development' },
  { value: 'design', label: 'Design' },
  { value: 'testing', label: 'Testing' },
  { value: 'documentation', label: 'Documentation' },
];

const currentStatuses = [
  { value: 'not_started', label: 'Not Started' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'on_hold', label: 'On Hold' },
];

const assignedToUsers = [
  { value: 'john_doe', label: 'John Doe' },
  { value: 'jane_smith', label: 'Jane Smith' },
  { value: 'mike_johnson', label: 'Mike Johnson' },
  { value: 'sarah_wilson', label: 'Sarah Wilson' },
];

// Main TaskPage component
function TaskPage() {
  const currentTheme = theme;
  const isMobile = useMediaQuery(currentTheme.breakpoints.down('sm'));

  const [selectedTaskId, setSelectedTaskId] = useState(null);
  const [tasks, setTasks] = useState([
    {
      id: 1,
      taskId: 'TSK-001',
      taskName: 'Design User Interface',
      taskDescription: 'Create wireframes and mockups for the new dashboard',
      priority: 'High',
      category: 'Design',
      project: { value: 'project1', label: 'Dashboard Redesign' },
      division: 'IT',
      customer: 'Internal',
      department: 'Development',
      taskType: { value: 'design', label: 'Design' },
      currentStatus: { value: 'in_progress', label: 'In Progress' },
      assignedTo: { value: 'john_doe', label: 'John Doe' },
      estimatedHours: '40',
      actualHours: '25',
      taskPCD: '2024-01-15',
      startDate: '2024-01-10',
      endDate: '2024-01-20',
      repeatTask: false,
      repeatFrequency: '',
      comments: 'Initial design phase',
      completed: false
    },
    {
      id: 2,
      taskId: 'TSK-002',
      taskName: 'Database Optimization',
      taskDescription: 'Optimize database queries for better performance',
      priority: 'Medium',
      category: 'Development',
      project: { value: 'project4', label: 'Database Optimization' },
      division: 'IT',
      customer: 'Internal',
      department: 'Database',
      taskType: { value: 'development', label: 'Development' },
      currentStatus: { value: 'not_started', label: 'Not Started' },
      assignedTo: { value: 'jane_smith', label: 'Jane Smith' },
      estimatedHours: '20',
      actualHours: '0',
      taskPCD: '2024-01-25',
      startDate: '2024-01-20',
      endDate: '2024-01-30',
      repeatTask: false,
      repeatFrequency: '',
      comments: 'Focus on slow queries',
      completed: false
    },
    {
      id: 3,
      taskId: 'TSK-003',
      taskName: 'Security Audit',
      taskDescription: 'Conduct comprehensive security audit of the application',
      priority: 'High',
      category: 'Security',
      project: { value: 'project3', label: 'API Integration' },
      division: 'IT',
      customer: 'Compliance Team',
      department: 'Security',
      taskType: { value: 'testing', label: 'Testing' },
      currentStatus: { value: 'completed', label: 'Completed' },
      assignedTo: { value: 'mike_johnson', label: 'Mike Johnson' },
      estimatedHours: '30',
      actualHours: '28',
      taskPCD: '2024-01-05',
      startDate: '2024-01-01',
      endDate: '2024-01-10',
      repeatTask: false,
      repeatFrequency: '',
      comments: 'All vulnerabilities addressed',
      completed: true
    }
  ]);

  const [newTask, setNewTask] = useState({
    id: null,
    taskId: '',
    taskName: '',
    taskDescription: '',
    priority: '',
    category: '',
    project: null,
    division: '',
    customer: '',
    department: '',
    taskType: null,
    currentStatus: null,
    assignedTo: null,
    estimatedHours: '',
    actualHours: '',
    taskPCD: '',
    startDate: '',
    endDate: '',
    repeatTask: false,
    repeatFrequency: '',
    comments: '',
    completed: false
  });

  // Handler for input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewTask(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handler for autocomplete changes
  const handleAutocompleteChange = (field, value) => {
    setNewTask(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handler for adding a new task or updating an existing one
  const handleAddOrUpdateTask = () => {
    if (newTask.taskName.trim() === '') {
      console.log("Task Name cannot be empty.");
      return;
    }

    if (newTask.id) {
      // Update existing task
      setTasks(
        tasks.map((task) =>
          task.id === newTask.id ? { ...task, ...newTask } : task
        )
      );
    } else {
      // Add new task
      setTasks([
        ...tasks,
        { ...newTask, id: Date.now(), completed: false },
      ]);
    }

    // Clear form after adding/updating
    handleClearForm();
  };

  // Handler for deleting a task
  const handleDeleteTask = (id) => {
    setTasks(tasks.filter((task) => task.id !== id));
  };

  // Handler for toggling the completion status of a task
  const handleToggleComplete = (id) => {
    setTasks(
      tasks.map((task) =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  // Handler for editing a task: populates the form with task details
  const handleEditTask = (task) => {
    setNewTask({
      ...task,
      taskPCD: task.taskPCD ? dayjs(task.taskPCD).format('YYYY-MM-DD') : '',
      startDate: task.startDate ? dayjs(task.startDate).format('YYYY-MM-DD') : '',
      endDate: task.endDate ? dayjs(task.endDate).format('YYYY-MM-DD') : '',
    });
  };

  // Handler for selecting a task from the task list
  const handleSelectTask = (task) => {
    setSelectedTaskId(task.id);
    handleEditTask(task);
  };

  // Handler to clear the input form
  const handleClearForm = () => {
    setSelectedTaskId(null);
    setNewTask({
      id: null,
      taskId: '',
      taskName: '',
      taskDescription: '',
      priority: '',
      category: '',
      project: null,
      division: '',
      customer: '',
      department: '',
      taskType: null,
      currentStatus: null,
      assignedTo: null,
      estimatedHours: '',
      actualHours: '',
      taskPCD: '',
      startDate: '',
      endDate: '',
      repeatTask: false,
      repeatFrequency: '',
      comments: '',
      completed: false
    });
  };

  return (
    <Box sx={{
      width: '100vw',
      height: '100vh',
      backgroundColor: '#f8f9fa',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <Box sx={{
        px: { xs: 1, sm: 2, md: 3 },
        py: { xs: 1, sm: 1.5, md: 2 },
        borderBottom: '1px solid #e0e0e0',
        backgroundColor: 'white',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <Typography
          variant="h4"
          component="h1"
          align="center"
          sx={{
            color: currentTheme.palette.primary.main,
            fontWeight: 700,
            fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.2rem', lg: '2.5rem' },
            fontFamily: 'Roobert, sans-serif',
            margin: 0
          }}
        >
          My Task Management Dashboard
        </Typography>
      </Box>

      {/* Main Content Area */}
      <Box sx={{
        flex: 1,
        overflow: 'hidden',
        px: { xs: 1, sm: 2, md: 3 },
        py: { xs: 1, sm: 1.5, md: 2 }
      }}>
        {/* Double Column Layout */}
        <Grid container spacing={3} sx={{
          width: '100%',
          height: '100%',
          overflow: 'hidden'
        }}>
          {/* Left Column - Task Creation Form */}
          <Grid item xs={12} md={6} sx={{
            height: '100%',
            overflow: 'auto',
            '&::-webkit-scrollbar': { width: '6px' },
            '&::-webkit-scrollbar-thumb': { backgroundColor: '#c1c1c1', borderRadius: '3px' }
          }}>
            {/* Task Creation Header */}
            <Paper elevation={2} sx={{ p: 3, mb: 3, backgroundColor: '#f8f9fa', borderLeft: '4px solid #2196f3' }}>
              <Typography variant="h5" sx={{ fontWeight: 700, color: '#1976d2', mb: 1 }}>
                📝 Task Creation
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                Create new tasks or edit existing ones by selecting from the right panel
              </Typography>
            </Paper>

            {/* Task Editing Indicator */}
            {selectedTaskId && (
              <Paper elevation={1} sx={{ p: 2, mb: 3, backgroundColor: '#e8f5e8', borderLeft: '4px solid #4caf50' }}>
                <Typography variant="body1" sx={{ fontWeight: 600, color: '#2e7d32' }}>
                  ✏️ Editing: {tasks.find(t => t.id === selectedTaskId)?.taskName || 'Unknown Task'}
                </Typography>
                <Typography variant="body2" sx={{ color: '#2e7d32', mt: 0.5 }}>
                  Make changes and click "Save Task" to update
                </Typography>
              </Paper>
            )}

            {/* Basic Information Form */}
            <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#1976d2', fontWeight: 600 }}>
                Basic Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Task Name *"
                    name="taskName"
                    value={newTask.taskName}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Task ID"
                    name="taskId"
                    value={newTask.taskId}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Priority"
                    name="priority"
                    value={newTask.priority || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    placeholder="High/Medium/Low"
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Task Description *"
                    name="taskDescription"
                    value={newTask.taskDescription}
                    onChange={handleInputChange}
                    variant="outlined"
                    multiline
                    rows={3}
                    size="small"
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Project Details */}
            <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#1976d2', fontWeight: 600 }}>
                Project Details
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Autocomplete
                    fullWidth
                    options={projects}
                    value={newTask.project}
                    onChange={(event, newValue) => handleAutocompleteChange('project', newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Project *" variant="outlined" size="small" />
                    )}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Division"
                    name="division"
                    value={newTask.division}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Department"
                    name="department"
                    value={newTask.department || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Assignment & Status */}
            <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#1976d2', fontWeight: 600 }}>
                Assignment & Status
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Autocomplete
                    fullWidth
                    options={taskTypes}
                    value={newTask.taskType}
                    onChange={(event, newValue) => handleAutocompleteChange('taskType', newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Task Type" variant="outlined" size="small" />
                    )}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Autocomplete
                    fullWidth
                    options={currentStatuses}
                    value={newTask.currentStatus}
                    onChange={(event, newValue) => handleAutocompleteChange('currentStatus', newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Status" variant="outlined" size="small" />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Autocomplete
                    fullWidth
                    options={assignedToUsers}
                    value={newTask.assignedTo}
                    onChange={(event, newValue) => handleAutocompleteChange('assignedTo', newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Assigned To" variant="outlined" size="small" />
                    )}
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Action Buttons */}
            <Paper elevation={2} sx={{ p: 3 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    color="primary"
                    onClick={handleAddOrUpdateTask}
                    startIcon={<AddIcon />}
                    sx={{ py: 1.5 }}
                  >
                    {selectedTaskId ? 'Update Task' : 'Create Task'}
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="secondary"
                    onClick={handleClearForm}
                    startIcon={<ClearIcon />}
                    sx={{ py: 1.5 }}
                  >
                    Clear Form
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Right Column - Available Tasks Today */}
          <Grid item xs={12} md={6} sx={{
            height: '100%',
            overflow: 'auto',
            '&::-webkit-scrollbar': { width: '6px' },
            '&::-webkit-scrollbar-thumb': { backgroundColor: '#c1c1c1', borderRadius: '3px' }
          }}>
            {/* Available Tasks Header */}
            <Paper elevation={2} sx={{ p: 3, mb: 3, backgroundColor: '#f8f9fa', borderLeft: '4px solid #ff9800' }}>
              <Typography variant="h5" sx={{ fontWeight: 700, color: '#f57c00', mb: 1 }}>
                📋 Available Tasks Today
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                Click on any task to edit it in the left panel
              </Typography>
            </Paper>

            {/* Tasks List */}
            {tasks.length === 0 ? (
              <Paper elevation={1} sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="body1" color="textSecondary">
                  No tasks available. Create your first task!
                </Typography>
              </Paper>
            ) : (
              <Grid container spacing={2}>
                {tasks.map((task) => (
                  <Grid item xs={12} key={task.id}>
                    <Paper
                      elevation={selectedTaskId === task.id ? 4 : 2}
                      onClick={() => handleSelectTask(task)}
                      sx={{
                        p: 3,
                        cursor: 'pointer',
                        borderLeft: `5px solid ${
                          task.priority === 'High' ? '#f44336' :
                          task.priority === 'Medium' ? '#ff9800' :
                          task.priority === 'Low' ? '#4caf50' : '#9e9e9e'
                        }`,
                        backgroundColor: selectedTaskId === task.id ? '#e3f2fd' : 'white',
                        transition: 'all 0.3s ease-in-out',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: currentTheme.shadows[6],
                          backgroundColor: selectedTaskId === task.id ? '#e3f2fd' : '#f8f9fa',
                        },
                      }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 600,
                              mb: 1,
                              textDecoration: task.completed ? 'line-through' : 'none',
                              color: task.completed ? 'text.secondary' : 'text.primary'
                            }}
                          >
                            {task.taskName}
                          </Typography>
                          {task.taskId && (
                            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1 }}>
                              ID: {task.taskId}
                            </Typography>
                          )}
                        </Box>
                        <Chip
                          label={task.priority || 'No Priority'}
                          size="small"
                          color={
                            task.priority === 'High' ? 'error' :
                            task.priority === 'Medium' ? 'warning' :
                            task.priority === 'Low' ? 'success' : 'default'
                          }
                          sx={{ fontWeight: 600 }}
                        />
                      </Box>

                      <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                        {task.taskDescription}
                      </Typography>

                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        {task.project && (
                          <Chip label={`📁 ${task.project.label}`} size="small" variant="outlined" />
                        )}
                        {task.assignedTo && (
                          <Chip label={`👤 ${task.assignedTo.label}`} size="small" variant="outlined" />
                        )}
                        {task.completed && (
                          <Chip label="✅ Completed" size="small" color="success" variant="outlined" />
                        )}
                      </Box>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            )}
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

// The main App component that wraps the TaskPage with the ThemeProvider
export default function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <TaskPage />
    </ThemeProvider>
  );
}
