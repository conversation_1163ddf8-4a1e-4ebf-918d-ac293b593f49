import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Grid,
  Paper,
  IconButton,
  useMediaQuery,
  CssBaseline,
  FormControlLabel,
  FormControl,
  FormLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  CalendarToday as CalendarTodayIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
  UploadFile as UploadFileIcon,
  PriorityHigh as PriorityHighIcon,
  Assignment as AssignmentIcon,
  Warning as WarningIcon,
  Bar<PERSON>hart as BarChartIcon,
} from '@mui/icons-material';
import Autocomplete from '@mui/material/Autocomplete';
import { createTheme, ThemeProvider, styled } from '@mui/material/styles';
import dayjs from 'dayjs';

// Define the custom theme outside the component to avoid re-creation on re-renders
const theme = createTheme({
  palette: {
    primary: {
      main: '#2EC0CB', // Your specified teal theme color
    },
    secondary: {
      main: '#6c757d', // A neutral grey for contrast
    },
    success: {
      main: '#28a745', // Green for completed tasks
      light: '#d4edda',
      contrastText: '#155724',
    },
    info: {
      main: '#17a2b8', // Blue for edit button
    },
    error: {
      main: '#dc3545', // Red for delete button
    },
    background: {
      default: '#f4f6f8', // Light background color for the page
      paper: '#ffffff', // Default background for Paper components
    },
  },
  typography: {
    fontFamily: 'Roobert, "Inter", sans-serif',
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        body {
          font-family: 'Roobert', "Inter", sans-serif;
          background-color: #f4f6f8; /* Explicitly set a light background for the body */
        }
      `,
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
          },
        },
      },
    },
    MuiAutocomplete: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
          },
        },
      },
    },
  },
});

// Mock data for dropdowns and tasks
const projects = ['IOCS', 'SWServices', 'Total-Exp', 'Tru-North'];
const taskTypes = ['Project Management', 'Training', 'Meeting', 'Documentation'];
const currentStatuses = ['Discovery Phase', 'Not Yet Assigned', 'On Hold', 'Waiting for Customer Approval', 'Waiting for Internal Approval'];
const taskStatuses = ['Work in Progress', 'On Hold', 'Completed', 'Request for PCD Change Approval', 'Request for Closure Approval'];
const assignedToUsers = [
  'admin', 'Bibhash Kanti Roy', 'Bryce Connors', 'David Campbell',
  'John Talbert', 'Nikil Ram D', 'Niraj kumar Mishra', 'Prasad H',
  'Ravi Tomar', 'Sreekrishna Narayana', 'Stuart Mckay', 'Thomas Remmel',
  'Vishwanath A.B',
];
const repeatFrequencies = ['Daily', 'Weekly', 'Monthly', 'Yearly'];

// Mock attachment data (for display purposes)
const mockAttachments = [
  { id: 1, fileName: 'ProjectPlan.pdf', description: 'Initial project plan', uploadedBy: 'admin', uploadedDate: '2024-06-15' },
  { id: 2, fileName: 'RequirementsDoc.docx', description: 'Detailed requirements', uploadedBy: 'John Talbert', uploadedDate: '2024-06-20' },
];


// Main TaskPage component
function TaskPage() {
  const currentTheme = theme;
  const isMobile = useMediaQuery(currentTheme.breakpoints.down('sm'));

  const [selectedTaskId, setSelectedTaskId] = useState(null);
  const [tasks, setTasks] = useState([
    {
      id: 1,
      taskId: 'TSK-001',
      taskName: 'Design User Interface',
      taskDescription: 'Create wireframes and mockups for the new dashboard',
      priority: 'High',
      category: 'Design',
      project: 'Dashboard Redesign',
      division: 'IT',
      customer: 'Internal',
      department: 'Development',
      taskType: 'Design',
      currentStatus: 'In Progress',
      taskStatus: 'Active',
      assignedTo: 'John Doe',
      estimatedHours: '40',
      actualHours: '25',
      taskPCD: '2024-01-15',
      startDate: '2024-01-10',
      endDate: '2024-01-20',
      repeatTask: false,
      repeatFrequency: '',
      comments: 'Initial design phase',
      completed: false
    },
    {
      id: 2,
      taskId: 'TSK-002',
      taskName: 'Database Optimization',
      taskDescription: 'Optimize database queries for better performance',
      priority: 'Medium',
      category: 'Development',
      project: 'Performance Enhancement',
      division: 'IT',
      customer: 'Internal',
      department: 'Database',
      taskType: 'Development',
      currentStatus: 'Pending',
      taskStatus: 'Active',
      assignedTo: 'Jane Smith',
      estimatedHours: '20',
      actualHours: '0',
      taskPCD: '2024-01-25',
      startDate: '2024-01-20',
      endDate: '2024-01-30',
      repeatTask: false,
      repeatFrequency: '',
      comments: 'Focus on slow queries',
      completed: false
    },
    {
      id: 3,
      taskId: 'TSK-003',
      taskName: 'Security Audit',
      taskDescription: 'Conduct comprehensive security audit of the application',
      priority: 'High',
      category: 'Security',
      project: 'Security Review',
      division: 'IT',
      customer: 'Compliance Team',
      department: 'Security',
      taskType: 'Audit',
      currentStatus: 'Completed',
      taskStatus: 'Closed',
      assignedTo: 'Mike Johnson',
      estimatedHours: '30',
      actualHours: '28',
      taskPCD: '2024-01-05',
      startDate: '2024-01-01',
      endDate: '2024-01-10',
      repeatTask: false,
      repeatFrequency: '',
      comments: 'All vulnerabilities addressed',
      completed: true
    }
  ]);
  const [newTask, setNewTask] = useState({
    id: null,
    taskId: '',
    taskName: '',
    taskDescription: '',
    project: null,
    division: '',
    customer: '',
    taskType: null,
    currentStatus: null,
    taskStatus: null,
    assignedTo: null,
    estimatedHours: '',
    actualHours: '',
    taskPCD: '',
    startDate: '',
    endDate: '',
    repeatTask: false,
    repeatFrequency: null,
    comments: '',
    completed: false,
  });

  // Handler for input changes in TextFields
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewTask((prev) => ({ ...prev, [name]: value }));
  };

  // Handler for Autocomplete (dropdown) changes
  const handleAutocompleteChange = (name, value) => {
    setNewTask((prev) => ({ ...prev, [name]: value }));
  };

  // Handler for Date TextField changes
  const handleDateChange = (e) => {
    const { name, value } = e.target;
    setNewTask((prev) => ({ ...prev, [name]: value }));
  };

  // Handler for Checkbox changes (for Repeat Task)
  const handleRepeatTaskChange = (e) => {
    const isChecked = e.target.checked;
    setNewTask((prev) => ({
      ...prev,
      repeatTask: isChecked,
      repeatFrequency: isChecked ? prev.repeatFrequency : null, // Clear frequency if unchecked
    }));
  };

  // Handler for adding a new task or updating an existing one
  const handleAddOrUpdateTask = () => {
    if (newTask.taskName.trim() === '') {
      console.log("Task Name cannot be empty.");
      return;
    }

    if (newTask.id) {
      // Update existing task
      setTasks(
        tasks.map((task) =>
          task.id === newTask.id ? { ...task, ...newTask } : task
        )
      );
    } else {
      // Add new task
      setTasks([
        ...tasks,
        { ...newTask, id: Date.now(), completed: false },
      ]);
    }
    handleClearForm(); // Clear the form after adding/updating
  };

  // Handler for deleting a task
  const handleDeleteTask = (id) => {
    setTasks(tasks.filter((task) => task.id !== id));
  };

  // Handler for toggling the completion status of a task
  const handleToggleComplete = (id) => {
    setTasks(
      tasks.map((task) =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  // Handler for editing a task: populates the form with task details
  const handleEditTask = (task) => {
    // Ensure dates are in 'YYYY-MM-DD' format for TextField type="date"
    setNewTask({
      ...task,
      taskPCD: task.taskPCD ? dayjs(task.taskPCD).format('YYYY-MM-DD') : '',
      startDate: task.startDate ? dayjs(task.startDate).format('YYYY-MM-DD') : '',
      endDate: task.endDate ? dayjs(task.endDate).format('YYYY-MM-DD') : '',
    });
  };

  // Handler for selecting a task from the task list
  const handleSelectTask = (task) => {
    setSelectedTaskId(task.id);
    handleEditTask(task);
  };

  // Handler to clear the input form
  const handleClearForm = () => {
    setNewTask({
      id: null,
      taskId: '',
      taskName: '',
      taskDescription: '',
      project: null,
      division: '',
      customer: '',
      taskType: null,
      currentStatus: null,
      taskStatus: null,
      assignedTo: null,
      estimatedHours: '',
      actualHours: '',
      taskPCD: '',
      startDate: '',
      endDate: '',
      repeatTask: false,
      repeatFrequency: null,
      comments: '',
      completed: false,
    });
  };

  return (
    <Box sx={{
      width: '100vw',
      height: '100vh',
      backgroundColor: '#f8f9fa',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Compact Header */}
      <Box sx={{
        px: { xs: 1, sm: 2, md: 3 },
        py: { xs: 1, sm: 1.5, md: 2 },
        borderBottom: '1px solid #e0e0e0',
        backgroundColor: 'white',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <Typography
          variant="h4"
          component="h1"
          align="center"
          sx={{
            color: currentTheme.palette.primary.main,
            fontWeight: 700,
            fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.2rem', lg: '2.5rem' },
            fontFamily: 'Roobert, sans-serif',
            margin: 0
          }}
        >
          My Detailed Task Tracker
        </Typography>
      </Box>

      {/* Main Content Area */}
      <Box sx={{
        flex: 1,
        overflow: 'hidden',
        px: { xs: 1, sm: 2, md: 3 },
        py: { xs: 1, sm: 1.5, md: 2 }
      }}>

        {/* Main Layout Grid - Compact for full screen view */}
        <Grid container spacing={{ xs: 1, sm: 1.5, md: 2 }} sx={{
          width: '100%',
          height: '100%',
          overflow: 'hidden'
        }}>

          {/* Left Column - Task Creation Form */}
          <Grid item xs={12} lg={8} xl={9} sx={{
            height: '100%',
            overflow: 'auto',
            '&::-webkit-scrollbar': { width: '6px' },
            '&::-webkit-scrollbar-thumb': { backgroundColor: '#c1c1c1', borderRadius: '3px' }
          }}>
            <Grid container spacing={{ xs: 1, sm: 1.5, md: 2 }}>

        {/* Task Editing Indicator */}
        {selectedTaskId && (
          <Grid item xs={12}>
            <Paper elevation={1} sx={{ p: 2, backgroundColor: '#e8f5e8', borderLeft: '4px solid #4caf50' }}>
              <Typography variant="body1" sx={{ fontWeight: 600, color: '#2e7d32' }}>
                📝 Editing Task: {tasks.find(t => t.id === selectedTaskId)?.taskName || 'Unknown Task'}
              </Typography>
              <Typography variant="body2" sx={{ color: '#2e7d32', mt: 0.5 }}>
                Click "Add Task" to save changes or "Clear Form" to cancel editing.
              </Typography>
            </Paper>
          </Grid>
        )}

        {/* Section 1: Basic Task Information Card */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: { xs: 2, sm: 2.5, md: 3 } }}>
            <Typography variant="h6" component="h2" sx={{ mb: 2, color: currentTheme.palette.primary.dark, fontSize: { xs: '1.1rem', md: '1.2rem' }, fontWeight: 600 }}>
              Basic Task Information
            </Typography>
            <Grid container spacing={{ xs: 2, sm: 2.5, md: 3 }} alignItems="flex-start">
              {/* Row 1: Task ID and Task Name */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Task ID"
                  name="taskId"
                  value={newTask.taskId}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  sx={{ '& .MuiInputLabel-root': { fontWeight: 500 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Task Name *"
                  name="taskName"
                  value={newTask.taskName}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  sx={{ '& .MuiInputLabel-root': { fontWeight: 500 } }}
                />
              </Grid>

              {/* Row 2: Priority and Category */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Priority"
                  name="priority"
                  value={newTask.priority || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="High/Medium/Low"
                  size="small"
                  sx={{ '& .MuiInputLabel-root': { fontWeight: 500 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Category"
                  name="category"
                  value={newTask.category || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="Enter category"
                  size="small"
                  sx={{ '& .MuiInputLabel-root': { fontWeight: 500 } }}
                />
              </Grid>

              {/* Row 3: Task Description (Full Width) */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Task Description *"
                  name="taskDescription"
                  value={newTask.taskDescription}
                  onChange={handleInputChange}
                  variant="outlined"
                  multiline
                  rows={3}
                  sx={{ '& .MuiInputLabel-root': { fontWeight: 500 } }}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Section 2: Project & Organizational Details Card */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: { xs: 2, sm: 2.5, md: 3 } }}>
            <Typography variant="h6" component="h2" sx={{ mb: 2, color: currentTheme.palette.primary.dark, fontSize: { xs: '1.1rem', md: '1.2rem' }, fontWeight: 600 }}>
              Project & Organizational Details
            </Typography>
            <Grid container spacing={{ xs: 2, sm: 2.5, md: 3 }} alignItems="flex-start">
              {/* Row 1: Project and Division */}
              <Grid item xs={12} sm={6}>
                <Autocomplete
                  fullWidth
                  options={projects}
                  value={newTask.project}
                  onChange={(event, newValue) => handleAutocompleteChange('project', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Project *" variant="outlined" size="small" sx={{ '& .MuiInputLabel-root': { fontWeight: 500 } }} />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Division"
                  name="division"
                  value={newTask.division}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  sx={{ '& .MuiInputLabel-root': { fontWeight: 500 } }}
                />
              </Grid>

              {/* Row 2: Customer and Department */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Customer"
                  name="customer"
                  value={newTask.customer}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  sx={{ '& .MuiInputLabel-root': { fontWeight: 500 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Department"
                  name="department"
                  value={newTask.department || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="Enter department"
                  size="small"
                  sx={{ '& .MuiInputLabel-root': { fontWeight: 500 } }}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Section 3: Task Classification & Assignment Card */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2, md: 2.5 } }}>
            <Typography variant="h6" component="h2" sx={{ mb: 1.5, color: currentTheme.palette.primary.dark, fontSize: { xs: '1rem', md: '1.1rem' } }}>
              Task Classification & Assignment
            </Typography>
            <Grid container spacing={{ xs: 1, sm: 1.5, md: 2 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Autocomplete
                  fullWidth
                  options={taskTypes}
                  value={newTask.taskType}
                  onChange={(event, newValue) => handleAutocompleteChange('taskType', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Task Type *" variant="outlined" />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Autocomplete
                  fullWidth
                  options={currentStatuses}
                  value={newTask.currentStatus}
                  onChange={(event, newValue) => handleAutocompleteChange('currentStatus', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Current Status *" variant="outlined" />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Autocomplete
                  fullWidth
                  options={taskStatuses}
                  value={newTask.taskStatus}
                  onChange={(event, newValue) => handleAutocompleteChange('taskStatus', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Task Status" variant="outlined" />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Autocomplete
                  fullWidth
                  options={assignedToUsers}
                  value={newTask.assignedTo}
                  onChange={(event, newValue) => handleAutocompleteChange('assignedTo', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Assigned To" variant="outlined" />
                  )}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Section 4: Scheduling & Effort Card */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2, md: 2.5 } }}>
            <Typography variant="h6" component="h2" sx={{ mb: 1.5, color: currentTheme.palette.primary.dark, fontSize: { xs: '1rem', md: '1.1rem' } }}>
              Scheduling & Effort
            </Typography>
            <Grid container spacing={{ xs: 1, sm: 1.5, md: 2 }}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Estimated Hours (hh:mm) *"
                  name="estimatedHours"
                  value={newTask.estimatedHours}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="HH:MM"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Actual Hours"
                  name="actualHours"
                  value={newTask.actualHours}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="HH:MM"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Task PCD *"
                  name="taskPCD"
                  type="date"
                  value={newTask.taskPCD}
                  onChange={handleDateChange}
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Start Date"
                  name="startDate"
                  type="date"
                  value={newTask.startDate}
                  onChange={handleDateChange}
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="End Date"
                  name="endDate"
                  type="date"
                  value={newTask.endDate}
                  onChange={handleDateChange}
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={newTask.repeatTask}
                      onChange={handleRepeatTaskChange}
                      name="repeatTask"
                      color="primary"
                    />
                  }
                  label="Repeat Task"
                />
              </Grid>
              {newTask.repeatTask && ( // Conditionally render Repeat Frequency
                <Grid item xs={12} sm={6} md={3}>
                  <Autocomplete
                    fullWidth
                    options={repeatFrequencies}
                    value={newTask.repeatFrequency}
                    onChange={(event, newValue) => handleAutocompleteChange('repeatFrequency', newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Repeat Frequency" variant="outlined" />
                    )}
                  />
                </Grid>
              )}
            </Grid>
          </Paper>
        </Grid>

        {/* Comments Card - Full Width */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2, md: 2.5 } }}>
            <Typography variant="h6" component="h2" sx={{ mb: 1.5, color: currentTheme.palette.primary.dark, fontSize: { xs: '1rem', md: '1.1rem' } }}>
              Comments
            </Typography>
            <Grid container spacing={{ xs: 1, sm: 1.5, md: 2 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Comments"
                  name="comments"
                  value={newTask.comments}
                  onChange={handleInputChange}
                  variant="outlined"
                  multiline
                  rows={2}
                  size={isMobile ? "small" : "medium"}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Action Buttons - Placed after all section cards */}
        <Grid item xs={12}>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              flexDirection: isMobile ? 'column' : 'row',
              justifyContent: 'flex-end',
              mt: 2,
            }}
          >
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddOrUpdateTask}
              fullWidth={isMobile}
            >
              {newTask.id ? 'Update Task' : 'Add Task'}
            </Button>
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<RefreshIcon />}
              onClick={handleClearForm}
              fullWidth={isMobile}
            >
              Clear Form
            </Button>
          </Box>
        </Grid>
          </Grid> {/* End of form sections grid */}
        </Grid> {/* End of left column */}

        {/* Right Column - Dashboard Cards */}
        <Grid item xs={12} lg={4} xl={3} sx={{
          height: '100%',
          overflow: 'auto',
          '&::-webkit-scrollbar': { width: '6px' },
          '&::-webkit-scrollbar-thumb': { backgroundColor: '#c1c1c1', borderRadius: '3px' }
        }}>
          <Grid container spacing={{ xs: 1, sm: 1.5, md: 2 }}>

            {/* Today's Tasks Card */}
            <Grid item xs={12} sm={6} lg={12}>
              <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2, md: 2.5 }, height: 'fit-content' }}>
                <Typography variant="subtitle1" component="h3" sx={{ mb: 1.5, color: currentTheme.palette.primary.dark, display: 'flex', alignItems: 'center', fontSize: { xs: '0.9rem', md: '1rem' }, fontWeight: 600 }}>
                  <CalendarTodayIcon sx={{ mr: 1, fontSize: { xs: '1rem', md: '1.2rem' } }} />
                  Today's Tasks
                </Typography>
                {tasks.filter(task => {
                  const today = dayjs().format('YYYY-MM-DD');
                  return task.startDate === today || task.taskPCD === today;
                }).length === 0 ? (
                  <Typography variant="body2" color="textSecondary">
                    No tasks scheduled for today
                  </Typography>
                ) : (
                  <Box>
                    {tasks.filter(task => {
                      const today = dayjs().format('YYYY-MM-DD');
                      return task.startDate === today || task.taskPCD === today;
                    }).slice(0, 3).map((task) => (
                      <Box key={task.id} sx={{ mb: 1, p: 1, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                        <Typography variant="body2" fontWeight="bold">{task.taskName}</Typography>
                        <Typography variant="caption" color="textSecondary">{task.project?.label || 'No Project'}</Typography>
                      </Box>
                    ))}
                  </Box>
                )}
              </Paper>
            </Grid>

            {/* Priority Breakdown Card */}
            <Grid item xs={12} sm={6} lg={12}>
              <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2, md: 2.5 }, height: 'fit-content' }}>
                <Typography variant="subtitle1" component="h3" sx={{ mb: 1.5, color: currentTheme.palette.primary.dark, display: 'flex', alignItems: 'center', fontSize: { xs: '0.9rem', md: '1rem' }, fontWeight: 600 }}>
                  <PriorityHighIcon sx={{ mr: 1, fontSize: { xs: '1rem', md: '1.2rem' } }} />
                  Priority Breakdown
                </Typography>
                <Box>
                  {['High', 'Medium', 'Low'].map((priority) => {
                    const count = tasks.filter(task => task.priority === priority && !task.completed).length;
                    const color = priority === 'High' ? '#f44336' : priority === 'Medium' ? '#ff9800' : '#4caf50';
                    return (
                      <Box key={priority} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box sx={{ width: 12, height: 12, backgroundColor: color, borderRadius: '50%', mr: 1 }} />
                          <Typography variant="body2">{priority} Priority</Typography>
                        </Box>
                        <Typography variant="body2" fontWeight="bold">{count}</Typography>
                      </Box>
                    );
                  })}
                </Box>
              </Paper>
            </Grid>

            {/* Pending Tasks by Project Card */}
            <Grid item xs={12} sm={6} lg={12}>
              <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2, md: 2.5 }, height: 'fit-content' }}>
                <Typography variant="subtitle1" component="h3" sx={{ mb: 1.5, color: currentTheme.palette.primary.dark, display: 'flex', alignItems: 'center', fontSize: { xs: '0.9rem', md: '1rem' }, fontWeight: 600 }}>
                  <AssignmentIcon sx={{ mr: 1, fontSize: { xs: '1rem', md: '1.2rem' } }} />
                  Pending by Project
                </Typography>
                <Box>
                  {projects.slice(0, 4).map((project) => {
                    const pendingCount = tasks.filter(task => task.project?.value === project.value && !task.completed).length;
                    return (
                      <Box key={project.value} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {project.label}
                        </Typography>
                        <Typography variant="body2" fontWeight="bold" color={pendingCount > 0 ? 'error.main' : 'success.main'}>
                          {pendingCount}
                        </Typography>
                      </Box>
                    );
                  })}
                </Box>
              </Paper>
            </Grid>

            {/* Urgent Tasks Card */}
            <Grid item xs={12} sm={6} lg={12}>
              <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2, md: 2.5 }, height: 'fit-content' }}>
                <Typography variant="subtitle1" component="h3" sx={{ mb: 1.5, color: '#f44336', display: 'flex', alignItems: 'center', fontSize: { xs: '0.9rem', md: '1rem' }, fontWeight: 600 }}>
                  <WarningIcon sx={{ mr: 1, fontSize: { xs: '1rem', md: '1.2rem' } }} />
                  Urgent Tasks
                </Typography>
                {tasks.filter(task => {
                  const dueDate = dayjs(task.taskPCD);
                  const today = dayjs();
                  return !task.completed && dueDate.isValid() && dueDate.diff(today, 'days') <= 2;
                }).length === 0 ? (
                  <Typography variant="body2" color="textSecondary">
                    No urgent tasks
                  </Typography>
                ) : (
                  <Box>
                    {tasks.filter(task => {
                      const dueDate = dayjs(task.taskPCD);
                      const today = dayjs();
                      return !task.completed && dueDate.isValid() && dueDate.diff(today, 'days') <= 2;
                    }).slice(0, 3).map((task) => (
                      <Box key={task.id} sx={{ mb: 1, p: 1, backgroundColor: '#ffebee', borderRadius: 1, border: '1px solid #ffcdd2' }}>
                        <Typography variant="body2" fontWeight="bold" color="error.main">{task.taskName}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          Due: {dayjs(task.taskPCD).format('MMM DD, YYYY')}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                )}
              </Paper>
            </Grid>

            {/* Quick Stats Card */}
            <Grid item xs={12} sm={12} lg={12}>
              <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2, md: 2.5 }, height: 'fit-content' }}>
                <Typography variant="subtitle1" component="h3" sx={{ mb: 1.5, color: currentTheme.palette.primary.dark, display: 'flex', alignItems: 'center', fontSize: { xs: '0.9rem', md: '1rem' }, fontWeight: 600 }}>
                  <BarChartIcon sx={{ mr: 1, fontSize: { xs: '1rem', md: '1.2rem' } }} />
                  Quick Stats
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 1, backgroundColor: '#e3f2fd', borderRadius: 1 }}>
                      <Typography variant="h4" fontWeight="bold" color="primary.main">
                        {tasks.length}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">Total Tasks</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 1, backgroundColor: '#e8f5e8', borderRadius: 1 }}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {tasks.filter(task => task.completed).length}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">Completed</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 1, backgroundColor: '#fff3e0', borderRadius: 1 }}>
                      <Typography variant="h4" fontWeight="bold" color="warning.main">
                        {tasks.filter(task => !task.completed).length}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">Pending</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 1, backgroundColor: '#fce4ec', borderRadius: 1 }}>
                      <Typography variant="h4" fontWeight="bold" color="error.main">
                        {Math.round(tasks.filter(task => task.completed).length / Math.max(tasks.length, 1) * 100)}%
                      </Typography>
                      <Typography variant="caption" color="textSecondary">Progress</Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

          </Grid>
        </Grid> {/* End of right column */}

        {/* Bottom Section - Attachments and Tasks */}
        <Grid item xs={12}>
          <Grid container spacing={{ xs: 1, sm: 1.5, md: 2 }}>

            {/* Attachment Details Section */}
            <Grid item xs={12} md={6}>
              <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2, md: 2.5 }, height: 'fit-content' }}>
                <Typography variant="h6" component="h3" sx={{ mb: 1.5, fontSize: { xs: '1rem', md: '1.1rem' } }}>
                  Attachment Details
                </Typography>
                {mockAttachments.length === 0 ? (
                  <Typography variant="body2" color="textSecondary">
                    No attachments available.
                  </Typography>
                ) : (
                  <TableContainer sx={{ maxHeight: 200 }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ py: 0.5, fontSize: '0.75rem' }}>Sl.</TableCell>
                          <TableCell sx={{ py: 0.5, fontSize: '0.75rem' }}>Select</TableCell>
                          <TableCell sx={{ py: 0.5, fontSize: '0.75rem' }}>View</TableCell>
                          <TableCell sx={{ py: 0.5, fontSize: '0.75rem' }}>File Name</TableCell>
                          <TableCell sx={{ py: 0.5, fontSize: '0.75rem' }}>Description</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {mockAttachments.slice(0, 3).map((attachment, index) => (
                          <TableRow key={attachment.id}>
                            <TableCell sx={{ py: 0.5, fontSize: '0.75rem' }}>{index + 1}</TableCell>
                            <TableCell sx={{ py: 0.5 }}>
                              <input type="checkbox" />
                            </TableCell>
                            <TableCell sx={{ py: 0.5 }}>
                              <IconButton size="small" aria-label="view attachment">
                                <ViewIcon />
                              </IconButton>
                            </TableCell>
                            <TableCell sx={{ py: 0.5, fontSize: '0.75rem' }}>{attachment.fileName}</TableCell>
                            <TableCell sx={{ py: 0.5, fontSize: '0.75rem' }}>{attachment.description}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </Paper>
            </Grid>

            {/* Task List Section */}
            <Grid item xs={12} md={6}>
              <Paper elevation={2} sx={{ p: { xs: 2, sm: 2.5, md: 3 }, height: 'fit-content' }}>
                <Typography variant="h6" component="h3" sx={{ mb: 2, fontSize: { xs: '1.1rem', md: '1.2rem' }, fontWeight: 600, color: currentTheme.palette.primary.dark }}>
                  Your Tasks
                </Typography>
                {tasks.length === 0 ? (
                  <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', py: 3 }}>
                    No tasks yet! Add a new task above to get started.
                  </Typography>
                ) : (
                  <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                    <Grid container spacing={2}>
                      {tasks.slice(0, 3).map((task) => (
                        <Grid item xs={12} key={task.id}>
                          <Paper
                            elevation={selectedTaskId === task.id ? 3 : 1}
                            onClick={() => handleSelectTask(task)}
                            sx={{
                              p: 2,
                              cursor: 'pointer',
                              borderLeft: `4px solid ${
                                task.priority === 'High' ? '#f44336' :
                                task.priority === 'Medium' ? '#ff9800' :
                                task.priority === 'Low' ? '#4caf50' : '#9e9e9e'
                              }`,
                              backgroundColor: selectedTaskId === task.id ? '#e3f2fd' : 'white',
                              transition: 'all 0.2s ease-in-out',
                              '&:hover': {
                                transform: 'translateX(4px)',
                                boxShadow: currentTheme.shadows[4],
                                backgroundColor: selectedTaskId === task.id ? '#e3f2fd' : '#f8f9fa',
                              },
                            }}
                          >
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Box sx={{ flex: 1 }}>
                                <Typography
                                  variant="subtitle1"
                                  sx={{
                                    fontWeight: 600,
                                    mb: 0.5,
                                    textDecoration: task.completed ? 'line-through' : 'none',
                                    color: task.completed ? 'text.secondary' : 'text.primary'
                                  }}
                                >
                                  {task.taskName}
                                  {task.taskId && (
                                    <Typography component="span" variant="body2" sx={{ ml: 1, color: 'text.secondary' }}>
                                      #{task.taskId}
                                    </Typography>
                                  )}
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Chip
                                    label={task.priority || 'No Priority'}
                                    size="small"
                                    color={
                                      task.priority === 'High' ? 'error' :
                                      task.priority === 'Medium' ? 'warning' :
                                      task.priority === 'Low' ? 'success' : 'default'
                                    }
                                    sx={{ fontSize: '0.75rem' }}
                                  />
                                  {task.completed && (
                                    <Chip
                                      label="Completed"
                                      size="small"
                                      color="success"
                                      variant="outlined"
                                      sx={{ fontSize: '0.75rem' }}
                                    />
                                  )}
                                </Box>
                              </Box>
                            </Box>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                )}
              </Paper>
            </Grid>
          </Grid>
        </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

// The main App component that wraps the TaskPage with the ThemeProvider
export default function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <TaskPage />
    </ThemeProvider>
  );
}
