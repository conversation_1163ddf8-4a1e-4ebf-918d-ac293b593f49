import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  TextField,
  Button,
  Grid,
  Paper,
  IconButton,
  useMediaQuery,
  CssBaseline,
  FormControlLabel,
  FormControl,
  FormLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  CalendarToday as CalendarTodayIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
  UploadFile as UploadFileIcon,
  PriorityHigh as PriorityHighIcon,
  Assignment as AssignmentIcon,
  Warning as WarningIcon,
  BarChart as BarChartIcon,
} from '@mui/icons-material';
import Autocomplete from '@mui/material/Autocomplete';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import dayjs from 'dayjs';

// Create a custom theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#2EC0CB',
      dark: '#26A8B3',
    },
    secondary: {
      main: '#2EC0CB',
    },
  },
  typography: {
    fontFamily: 'Roobert, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
});

// Sample data for dropdowns
const projects = [
  { value: 'project1', label: 'Dashboard Redesign' },
  { value: 'project2', label: 'Mobile App Development' },
  { value: 'project3', label: 'API Integration' },
  { value: 'project4', label: 'Database Optimization' },
];

const taskTypes = [
  { value: 'development', label: 'Development' },
  { value: 'design', label: 'Design' },
  { value: 'testing', label: 'Testing' },
  { value: 'documentation', label: 'Documentation' },
];

const currentStatuses = [
  { value: 'not_started', label: 'Not Started' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'on_hold', label: 'On Hold' },
];

const assignedToUsers = [
  { value: 'john_doe', label: 'John Doe' },
  { value: 'jane_smith', label: 'Jane Smith' },
  { value: 'mike_johnson', label: 'Mike Johnson' },
  { value: 'sarah_wilson', label: 'Sarah Wilson' },
];

// Main TaskPage component
function TaskPage() {
  const currentTheme = theme;
  const isMobile = useMediaQuery(currentTheme.breakpoints.down('sm'));

  const [selectedTaskId, setSelectedTaskId] = useState(null);
  const [tasks, setTasks] = useState([
    {
      id: 1,
      taskId: 'TSK-001',
      taskName: 'Design User Interface',
      taskDescription: 'Create wireframes and mockups for the new dashboard',
      priority: 'High',
      category: 'Design',
      project: { value: 'project1', label: 'Dashboard Redesign' },
      division: 'IT',
      customer: 'Internal',
      department: 'Development',
      taskType: { value: 'design', label: 'Design' },
      currentStatus: { value: 'in_progress', label: 'In Progress' },
      assignedTo: { value: 'john_doe', label: 'John Doe' },
      estimatedHours: '40',
      actualHours: '25',
      taskPCD: '2024-01-15',
      startDate: '2024-01-10',
      endDate: '2024-01-20',
      repeatTask: false,
      repeatFrequency: '',
      comments: 'Initial design phase',
      completed: false
    },
    {
      id: 2,
      taskId: 'TSK-002',
      taskName: 'Database Optimization',
      taskDescription: 'Optimize database queries for better performance',
      priority: 'Medium',
      category: 'Development',
      project: { value: 'project4', label: 'Database Optimization' },
      division: 'IT',
      customer: 'Internal',
      department: 'Database',
      taskType: { value: 'development', label: 'Development' },
      currentStatus: { value: 'not_started', label: 'Not Started' },
      assignedTo: { value: 'jane_smith', label: 'Jane Smith' },
      estimatedHours: '20',
      actualHours: '0',
      taskPCD: '2024-01-25',
      startDate: '2024-01-20',
      endDate: '2024-01-30',
      repeatTask: false,
      repeatFrequency: '',
      comments: 'Focus on slow queries',
      completed: false
    },
    {
      id: 3,
      taskId: 'TSK-003',
      taskName: 'Security Audit',
      taskDescription: 'Conduct comprehensive security audit of the application',
      priority: 'High',
      category: 'Security',
      project: { value: 'project3', label: 'API Integration' },
      division: 'IT',
      customer: 'Compliance Team',
      department: 'Security',
      taskType: { value: 'testing', label: 'Testing' },
      currentStatus: { value: 'completed', label: 'Completed' },
      assignedTo: { value: 'mike_johnson', label: 'Mike Johnson' },
      estimatedHours: '30',
      actualHours: '28',
      taskPCD: '2024-01-05',
      startDate: '2024-01-01',
      endDate: '2024-01-10',
      repeatTask: false,
      repeatFrequency: '',
      comments: 'All vulnerabilities addressed',
      completed: true
    }
  ]);

  const [newTask, setNewTask] = useState({
    id: null,
    taskId: '',
    taskName: '',
    taskDescription: '',
    priority: '',
    category: '',
    project: null,
    division: '',
    customer: '',
    department: '',
    taskType: null,
    currentStatus: null,
    assignedTo: null,
    estimatedHours: '',
    actualHours: '',
    taskPCD: '',
    startDate: '',
    endDate: '',
    repeatTask: false,
    repeatFrequency: '',
    comments: '',
    completed: false
  });

  // Handler for input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewTask(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handler for autocomplete changes
  const handleAutocompleteChange = (field, value) => {
    setNewTask(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handler for adding a new task or updating an existing one
  const handleAddOrUpdateTask = () => {
    if (newTask.taskName.trim() === '') {
      console.log("Task Name cannot be empty.");
      return;
    }

    if (newTask.id) {
      // Update existing task
      setTasks(
        tasks.map((task) =>
          task.id === newTask.id ? { ...task, ...newTask } : task
        )
      );
    } else {
      // Add new task
      setTasks([
        ...tasks,
        { ...newTask, id: Date.now(), completed: false },
      ]);
    }

    // Clear form after adding/updating
    handleClearForm();
  };

  // Handler for deleting a task
  const handleDeleteTask = (id) => {
    setTasks(tasks.filter((task) => task.id !== id));
  };

  // Handler for toggling the completion status of a task
  const handleToggleComplete = (id) => {
    setTasks(
      tasks.map((task) =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  // Handler for editing a task: populates the form with task details
  const handleEditTask = (task) => {
    setNewTask({
      ...task,
      taskPCD: task.taskPCD ? dayjs(task.taskPCD).format('YYYY-MM-DD') : '',
      startDate: task.startDate ? dayjs(task.startDate).format('YYYY-MM-DD') : '',
      endDate: task.endDate ? dayjs(task.endDate).format('YYYY-MM-DD') : '',
    });
  };

  // Handler for selecting a task from the task list
  const handleSelectTask = (task) => {
    setSelectedTaskId(task.id);
    handleEditTask(task);
  };

  // Handler to clear the input form
  const handleClearForm = () => {
    setSelectedTaskId(null);
    setNewTask({
      id: null,
      taskId: '',
      taskName: '',
      taskDescription: '',
      priority: '',
      category: '',
      project: null,
      division: '',
      customer: '',
      department: '',
      taskType: null,
      currentStatus: null,
      assignedTo: null,
      estimatedHours: '',
      actualHours: '',
      taskPCD: '',
      startDate: '',
      endDate: '',
      repeatTask: false,
      repeatFrequency: '',
      comments: '',
      completed: false
    });
  };

  return (
    <Box sx={{
      width: '100vw',
      height: '100vh',
      backgroundColor: '#f8f9fa',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <Box sx={{
        px: { xs: 1, sm: 2, md: 3 },
        py: { xs: 1, sm: 1.5, md: 2 },
        borderBottom: '1px solid #e0e0e0',
        backgroundColor: 'white',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <Typography
          variant="h4"
          component="h1"
          align="center"
          sx={{
            color: currentTheme.palette.primary.main,
            fontWeight: 700,
            fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.2rem', lg: '2.5rem' },
            fontFamily: 'Roobert, sans-serif',
            margin: 0
          }}
        >
          My Task Management Dashboard
        </Typography>
      </Box>

      {/* Main Content Area */}
      <Box sx={{
        flex: 1,
        overflow: 'hidden',
        px: { xs: 1, sm: 2, md: 3 },
        py: { xs: 1, sm: 1.5, md: 2 }
      }}>
        {/* Double Column Layout */}
        <Grid container spacing={3} sx={{
          width: '100%',
          height: '100%',
          overflow: 'hidden',
          minHeight: '600px'
        }}>
          {/* Left Column - Task Creation Form */}
          <Grid item xs={6} sx={{
            height: '100%',
            overflow: 'auto',
            '&::-webkit-scrollbar': { width: '6px' },
            '&::-webkit-scrollbar-thumb': { backgroundColor: '#c1c1c1', borderRadius: '3px' }
          }}>
            {/* Task Creation Header */}
            <Paper elevation={2} sx={{ p: 3, mb: 3, backgroundColor: '#f8f9fa', borderLeft: '4px solid #2EC0CB' }}>
              <Typography variant="h5" sx={{ fontWeight: 700, color: '#2EC0CB', mb: 1 }}>
                📝 Task Creation
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                Create new tasks or edit existing ones by selecting from the right panel
              </Typography>
            </Paper>

            {/* Task Editing Indicator */}
            {selectedTaskId && (
              <Paper elevation={1} sx={{ p: 2, mb: 3, backgroundColor: '#e8f5e8', borderLeft: '4px solid #4caf50' }}>
                <Typography variant="body1" sx={{ fontWeight: 600, color: '#2e7d32' }}>
                  ✏️ Editing: {tasks.find(t => t.id === selectedTaskId)?.taskName || 'Unknown Task'}
                </Typography>
                <Typography variant="body2" sx={{ color: '#2e7d32', mt: 0.5 }}>
                  Make changes and click "Save Task" to update
                </Typography>
              </Paper>
            )}

            {/* Basic Information Form */}
            <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#2EC0CB', fontWeight: 600 }}>
                Basic Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Task Name *"
                    name="taskName"
                    value={newTask.taskName}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Task ID"
                    name="taskId"
                    value={newTask.taskId}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Priority"
                    name="priority"
                    value={newTask.priority || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    placeholder="High/Medium/Low"
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Task Description *"
                    name="taskDescription"
                    value={newTask.taskDescription}
                    onChange={handleInputChange}
                    variant="outlined"
                    multiline
                    rows={3}
                    size="small"
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Project & Organizational Details */}
            <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#2EC0CB', fontWeight: 600 }}>
                Project & Organizational Details
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Autocomplete
                    fullWidth
                    options={projects}
                    value={newTask.project}
                    onChange={(event, newValue) => handleAutocompleteChange('project', newValue)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Project *"
                        variant="outlined"
                        size="medium"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#f8f9fa',
                            '&:hover': {
                              backgroundColor: '#e9ecef',
                            },
                            '&.Mui-focused': {
                              backgroundColor: 'white',
                            }
                          },
                          '& .MuiInputLabel-root': {
                            fontWeight: 600,
                            color: '#2EC0CB'
                          }
                        }}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Division"
                    name="division"
                    value={newTask.division}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Customer"
                    name="customer"
                    value={newTask.customer || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Department"
                    name="department"
                    value={newTask.department || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Assignment & Status */}
            <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#2EC0CB', fontWeight: 600 }}>
                Assignment & Status
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Autocomplete
                    fullWidth
                    options={taskTypes}
                    value={newTask.taskType}
                    onChange={(event, newValue) => handleAutocompleteChange('taskType', newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Task Type" variant="outlined" size="small" />
                    )}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Autocomplete
                    fullWidth
                    options={currentStatuses}
                    value={newTask.currentStatus}
                    onChange={(event, newValue) => handleAutocompleteChange('currentStatus', newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Status" variant="outlined" size="small" />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Autocomplete
                    fullWidth
                    options={assignedToUsers}
                    value={newTask.assignedTo}
                    onChange={(event, newValue) => handleAutocompleteChange('assignedTo', newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Assigned To" variant="outlined" size="small" />
                    )}
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Scheduling & Effort */}
            <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#2EC0CB', fontWeight: 600 }}>
                Scheduling & Effort
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Estimated Hours (hrs min)"
                    name="estimatedHours"
                    value={newTask.estimatedHours || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                    placeholder="e.g., 40"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Actual Hours"
                    name="actualHours"
                    value={newTask.actualHours || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                    placeholder="e.g., 35"
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Task PCD"
                    name="taskPCD"
                    type="date"
                    value={newTask.taskPCD || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Start Date"
                    name="startDate"
                    type="date"
                    value={newTask.startDate || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="End Date"
                    name="endDate"
                    type="date"
                    value={newTask.endDate || ''}
                    onChange={handleInputChange}
                    variant="outlined"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={newTask.repeatTask || false}
                          onChange={handleInputChange}
                          name="repeatTask"
                          color="primary"
                        />
                      }
                      label="Repeat Task"
                    />
                    {newTask.repeatTask && (
                      <TextField
                        label="Repeat Frequency"
                        name="repeatFrequency"
                        value={newTask.repeatFrequency || ''}
                        onChange={handleInputChange}
                        variant="outlined"
                        size="small"
                        placeholder="e.g., Weekly, Monthly"
                        sx={{ minWidth: 200 }}
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            {/* Comments */}
            <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#2EC0CB', fontWeight: 600 }}>
                Comments
              </Typography>
              <TextField
                fullWidth
                label="Comments"
                name="comments"
                value={newTask.comments || ''}
                onChange={handleInputChange}
                variant="outlined"
                multiline
                rows={4}
                size="small"
                placeholder="Add any additional notes or comments about this task..."
              />
            </Paper>

            {/* Action Buttons */}
            <Paper elevation={2} sx={{ p: 3 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    color="primary"
                    onClick={handleAddOrUpdateTask}
                    startIcon={<AddIcon />}
                    sx={{ py: 1.5 }}
                  >
                    {selectedTaskId ? 'Update Task' : 'Create Task'}
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="secondary"
                    onClick={handleClearForm}
                    startIcon={<ClearIcon />}
                    sx={{ py: 1.5 }}
                  >
                    Clear Form
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Right Column - Pending Tasks & To Do */}
          <Grid item xs={6} sx={{
            height: '100%',
            overflow: 'auto',
            '&::-webkit-scrollbar': { width: '6px' },
            '&::-webkit-scrollbar-thumb': { backgroundColor: '#c1c1c1', borderRadius: '3px' }
          }}>
            {/* Pending Tasks Header */}
            <Paper elevation={2} sx={{ p: 3, mb: 3, backgroundColor: '#f8f9fa', borderLeft: '4px solid #2EC0CB' }}>
              <Typography variant="h5" sx={{ fontWeight: 700, color: '#2EC0CB', mb: 1 }}>
                📋 Pending Tasks & To Do
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                Tasks that need your attention - Click to edit
              </Typography>
            </Paper>

            {/* Pending Tasks List */}
            {tasks.filter(task => !task.completed).length === 0 ? (
              <Paper elevation={1} sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
                  🎉 Great job! No pending tasks.
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  All tasks are completed or you can create a new task!
                </Typography>
              </Paper>
            ) : (
              <>
                {/* Summary Stats */}
                <Paper elevation={1} sx={{ p: 2, mb: 3, backgroundColor: '#e0f7f8' }}>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" sx={{ fontWeight: 700, color: '#2EC0CB' }}>
                          {tasks.filter(task => !task.completed).length}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#666' }}>
                          Pending
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" sx={{ fontWeight: 700, color: '#f44336' }}>
                          {tasks.filter(task => !task.completed && task.priority === 'High').length}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#666' }}>
                          High Priority
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" sx={{ fontWeight: 700, color: '#ff9800' }}>
                          {tasks.filter(task => !task.completed && task.taskPCD && dayjs(task.taskPCD).diff(dayjs(), 'days') <= 3).length}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#666' }}>
                          Due Soon
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>

                <Grid container spacing={2}>
                  {tasks
                    .filter(task => !task.completed)
                    .sort((a, b) => {
                      // Sort by priority: High > Medium > Low > No Priority
                      const priorityOrder = { 'High': 3, 'Medium': 2, 'Low': 1, '': 0 };
                      const aPriority = priorityOrder[a.priority] || 0;
                      const bPriority = priorityOrder[b.priority] || 0;
                      if (aPriority !== bPriority) return bPriority - aPriority;

                      // Then sort by due date (earliest first)
                      if (a.taskPCD && b.taskPCD) {
                        return dayjs(a.taskPCD).diff(dayjs(b.taskPCD));
                      }
                      return 0;
                    })
                    .map((task) => (
                  <Grid item xs={12} key={task.id}>
                    <Paper
                      elevation={selectedTaskId === task.id ? 4 : 2}
                      onClick={() => handleSelectTask(task)}
                      sx={{
                        p: 3,
                        cursor: 'pointer',
                        borderLeft: `5px solid ${
                          task.priority === 'High' ? '#2EC0CB' :
                          task.priority === 'Medium' ? '#2EC0CB' :
                          task.priority === 'Low' ? '#2EC0CB' : '#2EC0CB'
                        }`,
                        backgroundColor: selectedTaskId === task.id ? '#e0f7f8' : 'white',
                        transition: 'all 0.3s ease-in-out',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: currentTheme.shadows[6],
                          backgroundColor: selectedTaskId === task.id ? '#e0f7f8' : '#f8f9fa',
                        },
                      }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Box sx={{ flex: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Typography
                              variant="h6"
                              sx={{
                                fontWeight: 600,
                                color: 'text.primary'
                              }}
                            >
                              {task.taskName}
                            </Typography>
                            {task.taskPCD && dayjs(task.taskPCD).diff(dayjs(), 'days') <= 3 && (
                              <Chip
                                label="⚡ URGENT"
                                size="small"
                                sx={{
                                  backgroundColor: '#ff4444',
                                  color: 'white',
                                  fontWeight: 700,
                                  fontSize: '0.7rem'
                                }}
                              />
                            )}
                          </Box>
                          {task.taskId && (
                            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1 }}>
                              ID: {task.taskId}
                            </Typography>
                          )}
                          {task.taskPCD && (
                            <Typography variant="body2" sx={{
                              color: dayjs(task.taskPCD).diff(dayjs(), 'days') <= 3 ? '#ff4444' : 'text.secondary',
                              fontWeight: dayjs(task.taskPCD).diff(dayjs(), 'days') <= 3 ? 600 : 400
                            }}>
                              📅 Due: {dayjs(task.taskPCD).format('MMM DD, YYYY')}
                              ({dayjs(task.taskPCD).diff(dayjs(), 'days')} days)
                            </Typography>
                          )}
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, alignItems: 'flex-end' }}>
                          <Chip
                            label={task.priority || 'No Priority'}
                            size="small"
                            sx={{
                              fontWeight: 600,
                              backgroundColor: task.priority === 'High' ? '#ff4444' :
                                             task.priority === 'Medium' ? '#ff9800' :
                                             task.priority === 'Low' ? '#4caf50' : '#2EC0CB',
                              color: 'white',
                              '&:hover': {
                                opacity: 0.8
                              }
                            }}
                          />
                          {task.currentStatus && (
                            <Chip
                              label={task.currentStatus.label}
                              size="small"
                              variant="outlined"
                              sx={{
                                borderColor: '#2EC0CB',
                                color: '#2EC0CB',
                                fontSize: '0.7rem'
                              }}
                            />
                          )}
                        </Box>
                      </Box>

                      <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2, lineHeight: 1.4 }}>
                        {task.taskDescription}
                      </Typography>

                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                        {task.project && (
                          <Chip
                            label={`📁 ${task.project.label}`}
                            size="small"
                            variant="outlined"
                            sx={{
                              borderColor: '#2EC0CB',
                              color: '#2EC0CB',
                              '&:hover': {
                                backgroundColor: '#e0f7f8'
                              }
                            }}
                          />
                        )}
                        {task.assignedTo && (
                          <Chip
                            label={`👤 ${task.assignedTo.label}`}
                            size="small"
                            variant="outlined"
                            sx={{
                              borderColor: '#2EC0CB',
                              color: '#2EC0CB',
                              '&:hover': {
                                backgroundColor: '#e0f7f8'
                              }
                            }}
                          />
                        )}
                        {task.estimatedHours && (
                          <Chip
                            label={`⏱️ ${task.estimatedHours}h estimated`}
                            size="small"
                            variant="outlined"
                            sx={{
                              borderColor: '#ff9800',
                              color: '#ff9800',
                              fontSize: '0.7rem'
                            }}
                          />
                        )}
                      </Box>

                      {/* Action Indicators */}
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        pt: 1,
                        borderTop: '1px solid #e0e0e0'
                      }}>
                        <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                          Click to edit and update
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          {task.priority === 'High' && (
                            <Typography variant="caption" sx={{ color: '#ff4444', fontWeight: 600 }}>
                              🔥 HIGH PRIORITY
                            </Typography>
                          )}
                          {!task.taskPCD && (
                            <Typography variant="caption" sx={{ color: '#ff9800', fontWeight: 600 }}>
                              📅 NO DUE DATE
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
              </>
            )}
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

// The main App component that wraps the TaskPage with the ThemeProvider
export default function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <TaskPage />
    </ThemeProvider>
  );
}
