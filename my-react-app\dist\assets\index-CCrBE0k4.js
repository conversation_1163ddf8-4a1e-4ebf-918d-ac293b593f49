function RS(n,r){for(var l=0;l<r.length;l++){const i=r[l];if(typeof i!="string"&&!Array.isArray(i)){for(const u in i)if(u!=="default"&&!(u in n)){const c=Object.getOwnPropertyDescriptor(i,u);c&&Object.defineProperty(n,u,c.get?c:{enumerable:!0,get:()=>i[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))i(u);new MutationObserver(u=>{for(const c of u)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&i(d)}).observe(document,{childList:!0,subtree:!0});function l(u){const c={};return u.integrity&&(c.integrity=u.integrity),u.referrerPolicy&&(c.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?c.credentials="include":u.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function i(u){if(u.ep)return;u.ep=!0;const c=l(u);fetch(u.href,c)}})();function Vd(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var ad={exports:{}},pi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var iy;function OS(){if(iy)return pi;iy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function l(i,u,c){var d=null;if(c!==void 0&&(d=""+c),u.key!==void 0&&(d=""+u.key),"key"in u){c={};for(var p in u)p!=="key"&&(c[p]=u[p])}else c=u;return u=c.ref,{$$typeof:n,type:i,key:d,ref:u!==void 0?u:null,props:c}}return pi.Fragment=r,pi.jsx=l,pi.jsxs=l,pi}var sy;function MS(){return sy||(sy=1,ad.exports=OS()),ad.exports}var S=MS(),rd={exports:{}},Pe={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var uy;function wS(){if(uy)return Pe;uy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),x=Symbol.iterator;function R(A){return A===null||typeof A!="object"?null:(A=x&&A[x]||A["@@iterator"],typeof A=="function"?A:null)}var M={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},E=Object.assign,v={};function w(A,U,J){this.props=A,this.context=U,this.refs=v,this.updater=J||M}w.prototype.isReactComponent={},w.prototype.setState=function(A,U){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,U,"setState")},w.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function j(){}j.prototype=w.prototype;function P(A,U,J){this.props=A,this.context=U,this.refs=v,this.updater=J||M}var D=P.prototype=new j;D.constructor=P,E(D,w.prototype),D.isPureReactComponent=!0;var $=Array.isArray,z={H:null,A:null,T:null,S:null,V:null},N=Object.prototype.hasOwnProperty;function I(A,U,J,ne,fe,ue){return J=ue.ref,{$$typeof:n,type:A,key:U,ref:J!==void 0?J:null,props:ue}}function G(A,U){return I(A.type,U,void 0,void 0,void 0,A.props)}function K(A){return typeof A=="object"&&A!==null&&A.$$typeof===n}function b(A){var U={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function(J){return U[J]})}var B=/\/+/g;function V(A,U){return typeof A=="object"&&A!==null&&A.key!=null?b(""+A.key):U.toString(36)}function ae(){}function ee(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then(ae,ae):(A.status="pending",A.then(function(U){A.status==="pending"&&(A.status="fulfilled",A.value=U)},function(U){A.status==="pending"&&(A.status="rejected",A.reason=U)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function _(A,U,J,ne,fe){var ue=typeof A;(ue==="undefined"||ue==="boolean")&&(A=null);var le=!1;if(A===null)le=!0;else switch(ue){case"bigint":case"string":case"number":le=!0;break;case"object":switch(A.$$typeof){case n:case r:le=!0;break;case y:return le=A._init,_(le(A._payload),U,J,ne,fe)}}if(le)return fe=fe(A),le=ne===""?"."+V(A,0):ne,$(fe)?(J="",le!=null&&(J=le.replace(B,"$&/")+"/"),_(fe,U,J,"",function(be){return be})):fe!=null&&(K(fe)&&(fe=G(fe,J+(fe.key==null||A&&A.key===fe.key?"":(""+fe.key).replace(B,"$&/")+"/")+le)),U.push(fe)),1;le=0;var ve=ne===""?".":ne+":";if($(A))for(var xe=0;xe<A.length;xe++)ne=A[xe],ue=ve+V(ne,xe),le+=_(ne,U,J,ue,fe);else if(xe=R(A),typeof xe=="function")for(A=xe.call(A),xe=0;!(ne=A.next()).done;)ne=ne.value,ue=ve+V(ne,xe++),le+=_(ne,U,J,ue,fe);else if(ue==="object"){if(typeof A.then=="function")return _(ee(A),U,J,ne,fe);throw U=String(A),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return le}function T(A,U,J){if(A==null)return A;var ne=[],fe=0;return _(A,ne,"","",function(ue){return U.call(J,ue,fe++)}),ne}function L(A){if(A._status===-1){var U=A._result;U=U(),U.then(function(J){(A._status===0||A._status===-1)&&(A._status=1,A._result=J)},function(J){(A._status===0||A._status===-1)&&(A._status=2,A._result=J)}),A._status===-1&&(A._status=0,A._result=U)}if(A._status===1)return A._result.default;throw A._result}var Y=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function X(){}return Pe.Children={map:T,forEach:function(A,U,J){T(A,function(){U.apply(this,arguments)},J)},count:function(A){var U=0;return T(A,function(){U++}),U},toArray:function(A){return T(A,function(U){return U})||[]},only:function(A){if(!K(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},Pe.Component=w,Pe.Fragment=l,Pe.Profiler=u,Pe.PureComponent=P,Pe.StrictMode=i,Pe.Suspense=m,Pe.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=z,Pe.__COMPILER_RUNTIME={__proto__:null,c:function(A){return z.H.useMemoCache(A)}},Pe.cache=function(A){return function(){return A.apply(null,arguments)}},Pe.cloneElement=function(A,U,J){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var ne=E({},A.props),fe=A.key,ue=void 0;if(U!=null)for(le in U.ref!==void 0&&(ue=void 0),U.key!==void 0&&(fe=""+U.key),U)!N.call(U,le)||le==="key"||le==="__self"||le==="__source"||le==="ref"&&U.ref===void 0||(ne[le]=U[le]);var le=arguments.length-2;if(le===1)ne.children=J;else if(1<le){for(var ve=Array(le),xe=0;xe<le;xe++)ve[xe]=arguments[xe+2];ne.children=ve}return I(A.type,fe,void 0,void 0,ue,ne)},Pe.createContext=function(A){return A={$$typeof:d,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:c,_context:A},A},Pe.createElement=function(A,U,J){var ne,fe={},ue=null;if(U!=null)for(ne in U.key!==void 0&&(ue=""+U.key),U)N.call(U,ne)&&ne!=="key"&&ne!=="__self"&&ne!=="__source"&&(fe[ne]=U[ne]);var le=arguments.length-2;if(le===1)fe.children=J;else if(1<le){for(var ve=Array(le),xe=0;xe<le;xe++)ve[xe]=arguments[xe+2];fe.children=ve}if(A&&A.defaultProps)for(ne in le=A.defaultProps,le)fe[ne]===void 0&&(fe[ne]=le[ne]);return I(A,ue,void 0,void 0,null,fe)},Pe.createRef=function(){return{current:null}},Pe.forwardRef=function(A){return{$$typeof:p,render:A}},Pe.isValidElement=K,Pe.lazy=function(A){return{$$typeof:y,_payload:{_status:-1,_result:A},_init:L}},Pe.memo=function(A,U){return{$$typeof:h,type:A,compare:U===void 0?null:U}},Pe.startTransition=function(A){var U=z.T,J={};z.T=J;try{var ne=A(),fe=z.S;fe!==null&&fe(J,ne),typeof ne=="object"&&ne!==null&&typeof ne.then=="function"&&ne.then(X,Y)}catch(ue){Y(ue)}finally{z.T=U}},Pe.unstable_useCacheRefresh=function(){return z.H.useCacheRefresh()},Pe.use=function(A){return z.H.use(A)},Pe.useActionState=function(A,U,J){return z.H.useActionState(A,U,J)},Pe.useCallback=function(A,U){return z.H.useCallback(A,U)},Pe.useContext=function(A){return z.H.useContext(A)},Pe.useDebugValue=function(){},Pe.useDeferredValue=function(A,U){return z.H.useDeferredValue(A,U)},Pe.useEffect=function(A,U,J){var ne=z.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return ne.useEffect(A,U)},Pe.useId=function(){return z.H.useId()},Pe.useImperativeHandle=function(A,U,J){return z.H.useImperativeHandle(A,U,J)},Pe.useInsertionEffect=function(A,U){return z.H.useInsertionEffect(A,U)},Pe.useLayoutEffect=function(A,U){return z.H.useLayoutEffect(A,U)},Pe.useMemo=function(A,U){return z.H.useMemo(A,U)},Pe.useOptimistic=function(A,U){return z.H.useOptimistic(A,U)},Pe.useReducer=function(A,U,J){return z.H.useReducer(A,U,J)},Pe.useRef=function(A){return z.H.useRef(A)},Pe.useState=function(A){return z.H.useState(A)},Pe.useSyncExternalStore=function(A,U,J){return z.H.useSyncExternalStore(A,U,J)},Pe.useTransition=function(){return z.H.useTransition()},Pe.version="19.1.0",Pe}var cy;function Yd(){return cy||(cy=1,rd.exports=wS()),rd.exports}var O=Yd();const Ba=Vd(O),bu=RS({__proto__:null,default:Ba},[O]);var od={exports:{}},mi={},ld={exports:{}},id={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fy;function AS(){return fy||(fy=1,function(n){function r(T,L){var Y=T.length;T.push(L);e:for(;0<Y;){var X=Y-1>>>1,A=T[X];if(0<u(A,L))T[X]=L,T[Y]=A,Y=X;else break e}}function l(T){return T.length===0?null:T[0]}function i(T){if(T.length===0)return null;var L=T[0],Y=T.pop();if(Y!==L){T[0]=Y;e:for(var X=0,A=T.length,U=A>>>1;X<U;){var J=2*(X+1)-1,ne=T[J],fe=J+1,ue=T[fe];if(0>u(ne,Y))fe<A&&0>u(ue,ne)?(T[X]=ue,T[fe]=Y,X=fe):(T[X]=ne,T[J]=Y,X=J);else if(fe<A&&0>u(ue,Y))T[X]=ue,T[fe]=Y,X=fe;else break e}}return L}function u(T,L){var Y=T.sortIndex-L.sortIndex;return Y!==0?Y:T.id-L.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;n.unstable_now=function(){return c.now()}}else{var d=Date,p=d.now();n.unstable_now=function(){return d.now()-p}}var m=[],h=[],y=1,x=null,R=3,M=!1,E=!1,v=!1,w=!1,j=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,D=typeof setImmediate<"u"?setImmediate:null;function $(T){for(var L=l(h);L!==null;){if(L.callback===null)i(h);else if(L.startTime<=T)i(h),L.sortIndex=L.expirationTime,r(m,L);else break;L=l(h)}}function z(T){if(v=!1,$(T),!E)if(l(m)!==null)E=!0,N||(N=!0,V());else{var L=l(h);L!==null&&_(z,L.startTime-T)}}var N=!1,I=-1,G=5,K=-1;function b(){return w?!0:!(n.unstable_now()-K<G)}function B(){if(w=!1,N){var T=n.unstable_now();K=T;var L=!0;try{e:{E=!1,v&&(v=!1,P(I),I=-1),M=!0;var Y=R;try{t:{for($(T),x=l(m);x!==null&&!(x.expirationTime>T&&b());){var X=x.callback;if(typeof X=="function"){x.callback=null,R=x.priorityLevel;var A=X(x.expirationTime<=T);if(T=n.unstable_now(),typeof A=="function"){x.callback=A,$(T),L=!0;break t}x===l(m)&&i(m),$(T)}else i(m);x=l(m)}if(x!==null)L=!0;else{var U=l(h);U!==null&&_(z,U.startTime-T),L=!1}}break e}finally{x=null,R=Y,M=!1}L=void 0}}finally{L?V():N=!1}}}var V;if(typeof D=="function")V=function(){D(B)};else if(typeof MessageChannel<"u"){var ae=new MessageChannel,ee=ae.port2;ae.port1.onmessage=B,V=function(){ee.postMessage(null)}}else V=function(){j(B,0)};function _(T,L){I=j(function(){T(n.unstable_now())},L)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(T){T.callback=null},n.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<T?Math.floor(1e3/T):5},n.unstable_getCurrentPriorityLevel=function(){return R},n.unstable_next=function(T){switch(R){case 1:case 2:case 3:var L=3;break;default:L=R}var Y=R;R=L;try{return T()}finally{R=Y}},n.unstable_requestPaint=function(){w=!0},n.unstable_runWithPriority=function(T,L){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var Y=R;R=T;try{return L()}finally{R=Y}},n.unstable_scheduleCallback=function(T,L,Y){var X=n.unstable_now();switch(typeof Y=="object"&&Y!==null?(Y=Y.delay,Y=typeof Y=="number"&&0<Y?X+Y:X):Y=X,T){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=Y+A,T={id:y++,callback:L,priorityLevel:T,startTime:Y,expirationTime:A,sortIndex:-1},Y>X?(T.sortIndex=Y,r(h,T),l(m)===null&&T===l(h)&&(v?(P(I),I=-1):v=!0,_(z,Y-X))):(T.sortIndex=A,r(m,T),E||M||(E=!0,N||(N=!0,V()))),T},n.unstable_shouldYield=b,n.unstable_wrapCallback=function(T){var L=R;return function(){var Y=R;R=L;try{return T.apply(this,arguments)}finally{R=Y}}}}(id)),id}var dy;function zS(){return dy||(dy=1,ld.exports=AS()),ld.exports}var sd={exports:{}},pn={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var py;function DS(){if(py)return pn;py=1;var n=Yd();function r(m){var h="https://react.dev/errors/"+m;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)h+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+m+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(){}var i={d:{f:l,r:function(){throw Error(r(522))},D:l,C:l,L:l,m:l,X:l,S:l,M:l},p:0,findDOMNode:null},u=Symbol.for("react.portal");function c(m,h,y){var x=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:x==null?null:""+x,children:m,containerInfo:h,implementation:y}}var d=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(m,h){if(m==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return pn.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,pn.createPortal=function(m,h){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(r(299));return c(m,h,null,y)},pn.flushSync=function(m){var h=d.T,y=i.p;try{if(d.T=null,i.p=2,m)return m()}finally{d.T=h,i.p=y,i.d.f()}},pn.preconnect=function(m,h){typeof m=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,i.d.C(m,h))},pn.prefetchDNS=function(m){typeof m=="string"&&i.d.D(m)},pn.preinit=function(m,h){if(typeof m=="string"&&h&&typeof h.as=="string"){var y=h.as,x=p(y,h.crossOrigin),R=typeof h.integrity=="string"?h.integrity:void 0,M=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;y==="style"?i.d.S(m,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:x,integrity:R,fetchPriority:M}):y==="script"&&i.d.X(m,{crossOrigin:x,integrity:R,fetchPriority:M,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},pn.preinitModule=function(m,h){if(typeof m=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var y=p(h.as,h.crossOrigin);i.d.M(m,{crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&i.d.M(m)},pn.preload=function(m,h){if(typeof m=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var y=h.as,x=p(y,h.crossOrigin);i.d.L(m,y,{crossOrigin:x,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},pn.preloadModule=function(m,h){if(typeof m=="string")if(h){var y=p(h.as,h.crossOrigin);i.d.m(m,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else i.d.m(m)},pn.requestFormReset=function(m){i.d.r(m)},pn.unstable_batchedUpdates=function(m,h){return m(h)},pn.useFormState=function(m,h,y){return d.H.useFormState(m,h,y)},pn.useFormStatus=function(){return d.H.useHostTransitionStatus()},pn.version="19.1.0",pn}var my;function Xv(){if(my)return sd.exports;my=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),sd.exports=DS(),sd.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hy;function $S(){if(hy)return mi;hy=1;var n=zS(),r=Yd(),l=Xv();function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function c(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function p(e){if(c(e)!==e)throw Error(i(188))}function m(e){var t=e.alternate;if(!t){if(t=c(e),t===null)throw Error(i(188));return t!==e?null:e}for(var a=e,o=t;;){var s=a.return;if(s===null)break;var f=s.alternate;if(f===null){if(o=s.return,o!==null){a=o;continue}break}if(s.child===f.child){for(f=s.child;f;){if(f===a)return p(s),e;if(f===o)return p(s),t;f=f.sibling}throw Error(i(188))}if(a.return!==o.return)a=s,o=f;else{for(var g=!1,C=s.child;C;){if(C===a){g=!0,a=s,o=f;break}if(C===o){g=!0,o=s,a=f;break}C=C.sibling}if(!g){for(C=f.child;C;){if(C===a){g=!0,a=f,o=s;break}if(C===o){g=!0,o=f,a=s;break}C=C.sibling}if(!g)throw Error(i(189))}}if(a.alternate!==o)throw Error(i(190))}if(a.tag!==3)throw Error(i(188));return a.stateNode.current===a?e:t}function h(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=h(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,x=Symbol.for("react.element"),R=Symbol.for("react.transitional.element"),M=Symbol.for("react.portal"),E=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),w=Symbol.for("react.profiler"),j=Symbol.for("react.provider"),P=Symbol.for("react.consumer"),D=Symbol.for("react.context"),$=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),N=Symbol.for("react.suspense_list"),I=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),K=Symbol.for("react.activity"),b=Symbol.for("react.memo_cache_sentinel"),B=Symbol.iterator;function V(e){return e===null||typeof e!="object"?null:(e=B&&e[B]||e["@@iterator"],typeof e=="function"?e:null)}var ae=Symbol.for("react.client.reference");function ee(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ae?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case E:return"Fragment";case w:return"Profiler";case v:return"StrictMode";case z:return"Suspense";case N:return"SuspenseList";case K:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case M:return"Portal";case D:return(e.displayName||"Context")+".Provider";case P:return(e._context.displayName||"Context")+".Consumer";case $:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case I:return t=e.displayName||null,t!==null?t:ee(e.type)||"Memo";case G:t=e._payload,e=e._init;try{return ee(e(t))}catch{}}return null}var _=Array.isArray,T=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,L=l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y={pending:!1,data:null,method:null,action:null},X=[],A=-1;function U(e){return{current:e}}function J(e){0>A||(e.current=X[A],X[A]=null,A--)}function ne(e,t){A++,X[A]=e.current,e.current=t}var fe=U(null),ue=U(null),le=U(null),ve=U(null);function xe(e,t){switch(ne(le,t),ne(ue,e),ne(fe,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Ng(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Ng(t),e=Bg(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}J(fe),ne(fe,e)}function be(){J(fe),J(ue),J(le)}function me(e){e.memoizedState!==null&&ne(ve,e);var t=fe.current,a=Bg(t,e.type);t!==a&&(ne(ue,e),ne(fe,a))}function Re(e){ue.current===e&&(J(fe),J(ue)),ve.current===e&&(J(ve),si._currentValue=Y)}var Ne=Object.prototype.hasOwnProperty,Le=n.unstable_scheduleCallback,$e=n.unstable_cancelCallback,nt=n.unstable_shouldYield,Ve=n.unstable_requestPaint,at=n.unstable_now,he=n.unstable_getCurrentPriorityLevel,st=n.unstable_ImmediatePriority,Te=n.unstable_UserBlockingPriority,He=n.unstable_NormalPriority,ye=n.unstable_LowPriority,qt=n.unstable_IdlePriority,ut=n.log,At=n.unstable_setDisableYieldValue,rt=null,ke=null;function Ye(e){if(typeof ut=="function"&&At(e),ke&&typeof ke.setStrictMode=="function")try{ke.setStrictMode(rt,e)}catch{}}var et=Math.clz32?Math.clz32:Gt,vt=Math.log,Ae=Math.LN2;function Gt(e){return e>>>=0,e===0?32:31-(vt(e)/Ae|0)|0}var Cn=256,Zt=4194304;function Ce(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Ue(e,t,a){var o=e.pendingLanes;if(o===0)return 0;var s=0,f=e.suspendedLanes,g=e.pingedLanes;e=e.warmLanes;var C=o&134217727;return C!==0?(o=C&~f,o!==0?s=Ce(o):(g&=C,g!==0?s=Ce(g):a||(a=C&~e,a!==0&&(s=Ce(a))))):(C=o&~f,C!==0?s=Ce(C):g!==0?s=Ce(g):a||(a=o&~e,a!==0&&(s=Ce(a)))),s===0?0:t!==0&&t!==s&&(t&f)===0&&(f=s&-s,a=t&-t,f>=a||f===32&&(a&4194048)!==0)?t:s}function ot(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Gn(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ha(){var e=Cn;return Cn<<=1,(Cn&4194048)===0&&(Cn=256),e}function po(){var e=Zt;return Zt<<=1,(Zt&62914560)===0&&(Zt=4194304),e}function tn(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function zn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function ga(e,t,a,o,s,f){var g=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var C=e.entanglements,k=e.expirationTimes,F=e.hiddenUpdates;for(a=g&~a;0<a;){var ie=31-et(a),ce=1<<ie;C[ie]=0,k[ie]=-1;var W=F[ie];if(W!==null)for(F[ie]=null,ie=0;ie<W.length;ie++){var Z=W[ie];Z!==null&&(Z.lane&=-536870913)}a&=~ce}o!==0&&Ua(e,o,0),f!==0&&s===0&&e.tag!==0&&(e.suspendedLanes|=f&~(g&~t))}function Ua(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var o=31-et(t);e.entangledLanes|=t,e.entanglements[o]=e.entanglements[o]|1073741824|a&4194090}function Dn(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var o=31-et(a),s=1<<o;s&t|e[o]&t&&(e[o]|=t),a&=~s}}function ur(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function cr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Ea(){var e=L.p;return e!==0?e:(e=window.event,e===void 0?32:ty(e.type))}function yl(e,t){var a=L.p;try{return L.p=e,t()}finally{L.p=a}}var Xn=Math.random().toString(36).slice(2),zt="__reactFiber$"+Xn,Xt="__reactProps$"+Xn,Kn="__reactContainer$"+Xn,Pr="__reactEvents$"+Xn,vl="__reactListeners$"+Xn,mo="__reactHandles$"+Xn,bl="__reactResources$"+Xn,Qn="__reactMarker$"+Xn;function Bt(e){delete e[zt],delete e[Xt],delete e[Pr],delete e[vl],delete e[mo]}function Mt(e){var t=e[zt];if(t)return t;for(var a=e.parentNode;a;){if(t=a[Kn]||a[zt]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=Pg(e);e!==null;){if(a=e[zt])return a;e=Pg(e)}return t}e=a,a=e.parentNode}return null}function yn(e){if(e=e[zt]||e[Kn]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ya(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(i(33))}function va(e){var t=e[bl];return t||(t=e[bl]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function te(e){e[Qn]=!0}var re=new Set,ge={};function Be(e,t){Ee(e,t),Ee(e+"Capture",t)}function Ee(e,t){for(ge[e]=t,e=0;e<t.length;e++)re.add(t[e])}var Dt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),kt={},vn={};function Sl(e){return Ne.call(vn,e)?!0:Ne.call(kt,e)?!1:Dt.test(e)?vn[e]=!0:(kt[e]=!0,!1)}function ho(e,t,a){if(Sl(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var o=t.toLowerCase().slice(0,5);if(o!=="data-"&&o!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Ur(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Fn(e,t,a,o){if(o===null)e.removeAttribute(a);else{switch(typeof o){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+o)}}var go,lt;function bn(e){if(go===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);go=t&&t[1]||"",lt=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+go+e+lt}var Wn=!1;function qa(e,t){if(!e||Wn)return"";Wn=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var o={DetermineComponentFrameRoot:function(){try{if(t){var ce=function(){throw Error()};if(Object.defineProperty(ce.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(ce,[])}catch(Z){var W=Z}Reflect.construct(e,[],ce)}else{try{ce.call()}catch(Z){W=Z}e.call(ce.prototype)}}else{try{throw Error()}catch(Z){W=Z}(ce=e())&&typeof ce.catch=="function"&&ce.catch(function(){})}}catch(Z){if(Z&&W&&typeof Z.stack=="string")return[Z.stack,W.stack]}return[null,null]}};o.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(o.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(o.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=o.DetermineComponentFrameRoot(),g=f[0],C=f[1];if(g&&C){var k=g.split(`
`),F=C.split(`
`);for(s=o=0;o<k.length&&!k[o].includes("DetermineComponentFrameRoot");)o++;for(;s<F.length&&!F[s].includes("DetermineComponentFrameRoot");)s++;if(o===k.length||s===F.length)for(o=k.length-1,s=F.length-1;1<=o&&0<=s&&k[o]!==F[s];)s--;for(;1<=o&&0<=s;o--,s--)if(k[o]!==F[s]){if(o!==1||s!==1)do if(o--,s--,0>s||k[o]!==F[s]){var ie=`
`+k[o].replace(" at new "," at ");return e.displayName&&ie.includes("<anonymous>")&&(ie=ie.replace("<anonymous>",e.displayName)),ie}while(1<=o&&0<=s);break}}}finally{Wn=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?bn(a):""}function bb(e){switch(e.tag){case 26:case 27:case 5:return bn(e.type);case 16:return bn("Lazy");case 13:return bn("Suspense");case 19:return bn("SuspenseList");case 0:case 15:return qa(e.type,!1);case 11:return qa(e.type.render,!1);case 1:return qa(e.type,!0);case 31:return bn("Activity");default:return""}}function Ap(e){try{var t="";do t+=bb(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Zn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function zp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Sb(e){var t=zp(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),o=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var s=a.get,f=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(g){o=""+g,f.call(this,g)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return o},setValue:function(g){o=""+g},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ki(e){e._valueTracker||(e._valueTracker=Sb(e))}function Dp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),o="";return e&&(o=zp(e)?e.checked?"true":"false":e.value),e=o,e!==a?(t.setValue(e),!0):!1}function Qi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var xb=/[\n"\\]/g;function Jn(e){return e.replace(xb,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Ju(e,t,a,o,s,f,g,C){e.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.type=g:e.removeAttribute("type"),t!=null?g==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Zn(t)):e.value!==""+Zn(t)&&(e.value=""+Zn(t)):g!=="submit"&&g!=="reset"||e.removeAttribute("value"),t!=null?ec(e,g,Zn(t)):a!=null?ec(e,g,Zn(a)):o!=null&&e.removeAttribute("value"),s==null&&f!=null&&(e.defaultChecked=!!f),s!=null&&(e.checked=s&&typeof s!="function"&&typeof s!="symbol"),C!=null&&typeof C!="function"&&typeof C!="symbol"&&typeof C!="boolean"?e.name=""+Zn(C):e.removeAttribute("name")}function $p(e,t,a,o,s,f,g,C){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.type=f),t!=null||a!=null){if(!(f!=="submit"&&f!=="reset"||t!=null))return;a=a!=null?""+Zn(a):"",t=t!=null?""+Zn(t):a,C||t===e.value||(e.value=t),e.defaultValue=t}o=o??s,o=typeof o!="function"&&typeof o!="symbol"&&!!o,e.checked=C?e.checked:!!o,e.defaultChecked=!!o,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(e.name=g)}function ec(e,t,a){t==="number"&&Qi(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function yo(e,t,a,o){if(e=e.options,t){t={};for(var s=0;s<a.length;s++)t["$"+a[s]]=!0;for(a=0;a<e.length;a++)s=t.hasOwnProperty("$"+e[a].value),e[a].selected!==s&&(e[a].selected=s),s&&o&&(e[a].defaultSelected=!0)}else{for(a=""+Zn(a),t=null,s=0;s<e.length;s++){if(e[s].value===a){e[s].selected=!0,o&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function jp(e,t,a){if(t!=null&&(t=""+Zn(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Zn(a):""}function kp(e,t,a,o){if(t==null){if(o!=null){if(a!=null)throw Error(i(92));if(_(o)){if(1<o.length)throw Error(i(93));o=o[0]}a=o}a==null&&(a=""),t=a}a=Zn(t),e.defaultValue=a,o=e.textContent,o===a&&o!==""&&o!==null&&(e.value=o)}function vo(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Cb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Np(e,t,a){var o=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?o?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":o?e.setProperty(t,a):typeof a!="number"||a===0||Cb.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function Bp(e,t,a){if(t!=null&&typeof t!="object")throw Error(i(62));if(e=e.style,a!=null){for(var o in a)!a.hasOwnProperty(o)||t!=null&&t.hasOwnProperty(o)||(o.indexOf("--")===0?e.setProperty(o,""):o==="float"?e.cssFloat="":e[o]="");for(var s in t)o=t[s],t.hasOwnProperty(s)&&a[s]!==o&&Np(e,s,o)}else for(var f in t)t.hasOwnProperty(f)&&Np(e,f,t[f])}function tc(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tb=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Eb=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Fi(e){return Eb.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var nc=null;function ac(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bo=null,So=null;function _p(e){var t=yn(e);if(t&&(e=t.stateNode)){var a=e[Xt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Ju(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Jn(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var o=a[t];if(o!==e&&o.form===e.form){var s=o[Xt]||null;if(!s)throw Error(i(90));Ju(o,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(t=0;t<a.length;t++)o=a[t],o.form===e.form&&Dp(o)}break e;case"textarea":jp(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&yo(e,!!a.multiple,t,!1)}}}var rc=!1;function Lp(e,t,a){if(rc)return e(t,a);rc=!0;try{var o=e(t);return o}finally{if(rc=!1,(bo!==null||So!==null)&&(Ns(),bo&&(t=bo,e=So,So=bo=null,_p(t),e)))for(t=0;t<e.length;t++)_p(e[t])}}function xl(e,t){var a=e.stateNode;if(a===null)return null;var o=a[Xt]||null;if(o===null)return null;a=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(e=e.type,o=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!o;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(i(231,t,typeof a));return a}var Ia=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),oc=!1;if(Ia)try{var Cl={};Object.defineProperty(Cl,"passive",{get:function(){oc=!0}}),window.addEventListener("test",Cl,Cl),window.removeEventListener("test",Cl,Cl)}catch{oc=!1}var fr=null,lc=null,Wi=null;function Hp(){if(Wi)return Wi;var e,t=lc,a=t.length,o,s="value"in fr?fr.value:fr.textContent,f=s.length;for(e=0;e<a&&t[e]===s[e];e++);var g=a-e;for(o=1;o<=g&&t[a-o]===s[f-o];o++);return Wi=s.slice(e,1<o?1-o:void 0)}function Zi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ji(){return!0}function Pp(){return!1}function Tn(e){function t(a,o,s,f,g){this._reactName=a,this._targetInst=s,this.type=o,this.nativeEvent=f,this.target=g,this.currentTarget=null;for(var C in e)e.hasOwnProperty(C)&&(a=e[C],this[C]=a?a(f):f[C]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?Ji:Pp,this.isPropagationStopped=Pp,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Ji)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Ji)},persist:function(){},isPersistent:Ji}),t}var qr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},es=Tn(qr),Tl=y({},qr,{view:0,detail:0}),Rb=Tn(Tl),ic,sc,El,ts=y({},Tl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:cc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==El&&(El&&e.type==="mousemove"?(ic=e.screenX-El.screenX,sc=e.screenY-El.screenY):sc=ic=0,El=e),ic)},movementY:function(e){return"movementY"in e?e.movementY:sc}}),Up=Tn(ts),Ob=y({},ts,{dataTransfer:0}),Mb=Tn(Ob),wb=y({},Tl,{relatedTarget:0}),uc=Tn(wb),Ab=y({},qr,{animationName:0,elapsedTime:0,pseudoElement:0}),zb=Tn(Ab),Db=y({},qr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$b=Tn(Db),jb=y({},qr,{data:0}),qp=Tn(jb),kb={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Nb={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Bb={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function _b(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Bb[e])?!!t[e]:!1}function cc(){return _b}var Lb=y({},Tl,{key:function(e){if(e.key){var t=kb[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Zi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Nb[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:cc,charCode:function(e){return e.type==="keypress"?Zi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Zi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Hb=Tn(Lb),Pb=y({},ts,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ip=Tn(Pb),Ub=y({},Tl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:cc}),qb=Tn(Ub),Ib=y({},qr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Vb=Tn(Ib),Yb=y({},ts,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Gb=Tn(Yb),Xb=y({},qr,{newState:0,oldState:0}),Kb=Tn(Xb),Qb=[9,13,27,32],fc=Ia&&"CompositionEvent"in window,Rl=null;Ia&&"documentMode"in document&&(Rl=document.documentMode);var Fb=Ia&&"TextEvent"in window&&!Rl,Vp=Ia&&(!fc||Rl&&8<Rl&&11>=Rl),Yp=" ",Gp=!1;function Xp(e,t){switch(e){case"keyup":return Qb.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Kp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var xo=!1;function Wb(e,t){switch(e){case"compositionend":return Kp(t);case"keypress":return t.which!==32?null:(Gp=!0,Yp);case"textInput":return e=t.data,e===Yp&&Gp?null:e;default:return null}}function Zb(e,t){if(xo)return e==="compositionend"||!fc&&Xp(e,t)?(e=Hp(),Wi=lc=fr=null,xo=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Vp&&t.locale!=="ko"?null:t.data;default:return null}}var Jb={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qp(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Jb[e.type]:t==="textarea"}function Fp(e,t,a,o){bo?So?So.push(o):So=[o]:bo=o,t=Us(t,"onChange"),0<t.length&&(a=new es("onChange","change",null,a,o),e.push({event:a,listeners:t}))}var Ol=null,Ml=null;function e1(e){zg(e,0)}function ns(e){var t=ya(e);if(Dp(t))return e}function Wp(e,t){if(e==="change")return t}var Zp=!1;if(Ia){var dc;if(Ia){var pc="oninput"in document;if(!pc){var Jp=document.createElement("div");Jp.setAttribute("oninput","return;"),pc=typeof Jp.oninput=="function"}dc=pc}else dc=!1;Zp=dc&&(!document.documentMode||9<document.documentMode)}function em(){Ol&&(Ol.detachEvent("onpropertychange",tm),Ml=Ol=null)}function tm(e){if(e.propertyName==="value"&&ns(Ml)){var t=[];Fp(t,Ml,e,ac(e)),Lp(e1,t)}}function t1(e,t,a){e==="focusin"?(em(),Ol=t,Ml=a,Ol.attachEvent("onpropertychange",tm)):e==="focusout"&&em()}function n1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ns(Ml)}function a1(e,t){if(e==="click")return ns(t)}function r1(e,t){if(e==="input"||e==="change")return ns(t)}function o1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var $n=typeof Object.is=="function"?Object.is:o1;function wl(e,t){if($n(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),o=Object.keys(t);if(a.length!==o.length)return!1;for(o=0;o<a.length;o++){var s=a[o];if(!Ne.call(t,s)||!$n(e[s],t[s]))return!1}return!0}function nm(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function am(e,t){var a=nm(e);e=0;for(var o;a;){if(a.nodeType===3){if(o=e+a.textContent.length,e<=t&&o>=t)return{node:a,offset:t-e};e=o}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=nm(a)}}function rm(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?rm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function om(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Qi(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Qi(e.document)}return t}function mc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var l1=Ia&&"documentMode"in document&&11>=document.documentMode,Co=null,hc=null,Al=null,gc=!1;function lm(e,t,a){var o=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;gc||Co==null||Co!==Qi(o)||(o=Co,"selectionStart"in o&&mc(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),Al&&wl(Al,o)||(Al=o,o=Us(hc,"onSelect"),0<o.length&&(t=new es("onSelect","select",null,t,a),e.push({event:t,listeners:o}),t.target=Co)))}function Ir(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var To={animationend:Ir("Animation","AnimationEnd"),animationiteration:Ir("Animation","AnimationIteration"),animationstart:Ir("Animation","AnimationStart"),transitionrun:Ir("Transition","TransitionRun"),transitionstart:Ir("Transition","TransitionStart"),transitioncancel:Ir("Transition","TransitionCancel"),transitionend:Ir("Transition","TransitionEnd")},yc={},im={};Ia&&(im=document.createElement("div").style,"AnimationEvent"in window||(delete To.animationend.animation,delete To.animationiteration.animation,delete To.animationstart.animation),"TransitionEvent"in window||delete To.transitionend.transition);function Vr(e){if(yc[e])return yc[e];if(!To[e])return e;var t=To[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in im)return yc[e]=t[a];return e}var sm=Vr("animationend"),um=Vr("animationiteration"),cm=Vr("animationstart"),i1=Vr("transitionrun"),s1=Vr("transitionstart"),u1=Vr("transitioncancel"),fm=Vr("transitionend"),dm=new Map,vc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");vc.push("scrollEnd");function ba(e,t){dm.set(e,t),Be(t,[e])}var pm=new WeakMap;function ea(e,t){if(typeof e=="object"&&e!==null){var a=pm.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Ap(t)},pm.set(e,t),t)}return{value:e,source:t,stack:Ap(t)}}var ta=[],Eo=0,bc=0;function as(){for(var e=Eo,t=bc=Eo=0;t<e;){var a=ta[t];ta[t++]=null;var o=ta[t];ta[t++]=null;var s=ta[t];ta[t++]=null;var f=ta[t];if(ta[t++]=null,o!==null&&s!==null){var g=o.pending;g===null?s.next=s:(s.next=g.next,g.next=s),o.pending=s}f!==0&&mm(a,s,f)}}function rs(e,t,a,o){ta[Eo++]=e,ta[Eo++]=t,ta[Eo++]=a,ta[Eo++]=o,bc|=o,e.lanes|=o,e=e.alternate,e!==null&&(e.lanes|=o)}function Sc(e,t,a,o){return rs(e,t,a,o),os(e)}function Ro(e,t){return rs(e,null,null,t),os(e)}function mm(e,t,a){e.lanes|=a;var o=e.alternate;o!==null&&(o.lanes|=a);for(var s=!1,f=e.return;f!==null;)f.childLanes|=a,o=f.alternate,o!==null&&(o.childLanes|=a),f.tag===22&&(e=f.stateNode,e===null||e._visibility&1||(s=!0)),e=f,f=f.return;return e.tag===3?(f=e.stateNode,s&&t!==null&&(s=31-et(a),e=f.hiddenUpdates,o=e[s],o===null?e[s]=[t]:o.push(t),t.lane=a|536870912),f):null}function os(e){if(50<ei)throw ei=0,Mf=null,Error(i(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Oo={};function c1(e,t,a,o){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jn(e,t,a,o){return new c1(e,t,a,o)}function xc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Va(e,t){var a=e.alternate;return a===null?(a=jn(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function hm(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ls(e,t,a,o,s,f){var g=0;if(o=e,typeof e=="function")xc(e)&&(g=1);else if(typeof e=="string")g=dS(e,a,fe.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case K:return e=jn(31,a,t,s),e.elementType=K,e.lanes=f,e;case E:return Yr(a.children,s,f,t);case v:g=8,s|=24;break;case w:return e=jn(12,a,t,s|2),e.elementType=w,e.lanes=f,e;case z:return e=jn(13,a,t,s),e.elementType=z,e.lanes=f,e;case N:return e=jn(19,a,t,s),e.elementType=N,e.lanes=f,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case j:case D:g=10;break e;case P:g=9;break e;case $:g=11;break e;case I:g=14;break e;case G:g=16,o=null;break e}g=29,a=Error(i(130,e===null?"null":typeof e,"")),o=null}return t=jn(g,a,t,s),t.elementType=e,t.type=o,t.lanes=f,t}function Yr(e,t,a,o){return e=jn(7,e,o,t),e.lanes=a,e}function Cc(e,t,a){return e=jn(6,e,null,t),e.lanes=a,e}function Tc(e,t,a){return t=jn(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Mo=[],wo=0,is=null,ss=0,na=[],aa=0,Gr=null,Ya=1,Ga="";function Xr(e,t){Mo[wo++]=ss,Mo[wo++]=is,is=e,ss=t}function gm(e,t,a){na[aa++]=Ya,na[aa++]=Ga,na[aa++]=Gr,Gr=e;var o=Ya;e=Ga;var s=32-et(o)-1;o&=~(1<<s),a+=1;var f=32-et(t)+s;if(30<f){var g=s-s%5;f=(o&(1<<g)-1).toString(32),o>>=g,s-=g,Ya=1<<32-et(t)+s|a<<s|o,Ga=f+e}else Ya=1<<f|a<<s|o,Ga=e}function Ec(e){e.return!==null&&(Xr(e,1),gm(e,1,0))}function Rc(e){for(;e===is;)is=Mo[--wo],Mo[wo]=null,ss=Mo[--wo],Mo[wo]=null;for(;e===Gr;)Gr=na[--aa],na[aa]=null,Ga=na[--aa],na[aa]=null,Ya=na[--aa],na[aa]=null}var Sn=null,_t=null,ft=!1,Kr=null,Ra=!1,Oc=Error(i(519));function Qr(e){var t=Error(i(418,""));throw $l(ea(t,e)),Oc}function ym(e){var t=e.stateNode,a=e.type,o=e.memoizedProps;switch(t[zt]=e,t[Xt]=o,a){case"dialog":We("cancel",t),We("close",t);break;case"iframe":case"object":case"embed":We("load",t);break;case"video":case"audio":for(a=0;a<ni.length;a++)We(ni[a],t);break;case"source":We("error",t);break;case"img":case"image":case"link":We("error",t),We("load",t);break;case"details":We("toggle",t);break;case"input":We("invalid",t),$p(t,o.value,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name,!0),Ki(t);break;case"select":We("invalid",t);break;case"textarea":We("invalid",t),kp(t,o.value,o.defaultValue,o.children),Ki(t)}a=o.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||o.suppressHydrationWarning===!0||kg(t.textContent,a)?(o.popover!=null&&(We("beforetoggle",t),We("toggle",t)),o.onScroll!=null&&We("scroll",t),o.onScrollEnd!=null&&We("scrollend",t),o.onClick!=null&&(t.onclick=qs),t=!0):t=!1,t||Qr(e)}function vm(e){for(Sn=e.return;Sn;)switch(Sn.tag){case 5:case 13:Ra=!1;return;case 27:case 3:Ra=!0;return;default:Sn=Sn.return}}function zl(e){if(e!==Sn)return!1;if(!ft)return vm(e),ft=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||If(e.type,e.memoizedProps)),a=!a),a&&_t&&Qr(e),vm(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){_t=xa(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}_t=null}}else t===27?(t=_t,Mr(e.type)?(e=Xf,Xf=null,_t=e):_t=t):_t=Sn?xa(e.stateNode.nextSibling):null;return!0}function Dl(){_t=Sn=null,ft=!1}function bm(){var e=Kr;return e!==null&&(On===null?On=e:On.push.apply(On,e),Kr=null),e}function $l(e){Kr===null?Kr=[e]:Kr.push(e)}var Mc=U(null),Fr=null,Xa=null;function dr(e,t,a){ne(Mc,t._currentValue),t._currentValue=a}function Ka(e){e._currentValue=Mc.current,J(Mc)}function wc(e,t,a){for(;e!==null;){var o=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,o!==null&&(o.childLanes|=t)):o!==null&&(o.childLanes&t)!==t&&(o.childLanes|=t),e===a)break;e=e.return}}function Ac(e,t,a,o){var s=e.child;for(s!==null&&(s.return=e);s!==null;){var f=s.dependencies;if(f!==null){var g=s.child;f=f.firstContext;e:for(;f!==null;){var C=f;f=s;for(var k=0;k<t.length;k++)if(C.context===t[k]){f.lanes|=a,C=f.alternate,C!==null&&(C.lanes|=a),wc(f.return,a,e),o||(g=null);break e}f=C.next}}else if(s.tag===18){if(g=s.return,g===null)throw Error(i(341));g.lanes|=a,f=g.alternate,f!==null&&(f.lanes|=a),wc(g,a,e),g=null}else g=s.child;if(g!==null)g.return=s;else for(g=s;g!==null;){if(g===e){g=null;break}if(s=g.sibling,s!==null){s.return=g.return,g=s;break}g=g.return}s=g}}function jl(e,t,a,o){e=null;for(var s=t,f=!1;s!==null;){if(!f){if((s.flags&524288)!==0)f=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var g=s.alternate;if(g===null)throw Error(i(387));if(g=g.memoizedProps,g!==null){var C=s.type;$n(s.pendingProps.value,g.value)||(e!==null?e.push(C):e=[C])}}else if(s===ve.current){if(g=s.alternate,g===null)throw Error(i(387));g.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(e!==null?e.push(si):e=[si])}s=s.return}e!==null&&Ac(t,e,a,o),t.flags|=262144}function us(e){for(e=e.firstContext;e!==null;){if(!$n(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Wr(e){Fr=e,Xa=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function dn(e){return Sm(Fr,e)}function cs(e,t){return Fr===null&&Wr(e),Sm(e,t)}function Sm(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Xa===null){if(e===null)throw Error(i(308));Xa=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Xa=Xa.next=t;return a}var f1=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,o){e.push(o)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},d1=n.unstable_scheduleCallback,p1=n.unstable_NormalPriority,Jt={$$typeof:D,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function zc(){return{controller:new f1,data:new Map,refCount:0}}function kl(e){e.refCount--,e.refCount===0&&d1(p1,function(){e.controller.abort()})}var Nl=null,Dc=0,Ao=0,zo=null;function m1(e,t){if(Nl===null){var a=Nl=[];Dc=0,Ao=kf(),zo={status:"pending",value:void 0,then:function(o){a.push(o)}}}return Dc++,t.then(xm,xm),t}function xm(){if(--Dc===0&&Nl!==null){zo!==null&&(zo.status="fulfilled");var e=Nl;Nl=null,Ao=0,zo=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function h1(e,t){var a=[],o={status:"pending",value:null,reason:null,then:function(s){a.push(s)}};return e.then(function(){o.status="fulfilled",o.value=t;for(var s=0;s<a.length;s++)(0,a[s])(t)},function(s){for(o.status="rejected",o.reason=s,s=0;s<a.length;s++)(0,a[s])(void 0)}),o}var Cm=T.S;T.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&m1(e,t),Cm!==null&&Cm(e,t)};var Zr=U(null);function $c(){var e=Zr.current;return e!==null?e:Ot.pooledCache}function fs(e,t){t===null?ne(Zr,Zr.current):ne(Zr,t.pool)}function Tm(){var e=$c();return e===null?null:{parent:Jt._currentValue,pool:e}}var Bl=Error(i(460)),Em=Error(i(474)),ds=Error(i(542)),jc={then:function(){}};function Rm(e){return e=e.status,e==="fulfilled"||e==="rejected"}function ps(){}function Om(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(ps,ps),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,wm(e),e;default:if(typeof t.status=="string")t.then(ps,ps);else{if(e=Ot,e!==null&&100<e.shellSuspendCounter)throw Error(i(482));e=t,e.status="pending",e.then(function(o){if(t.status==="pending"){var s=t;s.status="fulfilled",s.value=o}},function(o){if(t.status==="pending"){var s=t;s.status="rejected",s.reason=o}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,wm(e),e}throw _l=t,Bl}}var _l=null;function Mm(){if(_l===null)throw Error(i(459));var e=_l;return _l=null,e}function wm(e){if(e===Bl||e===ds)throw Error(i(483))}var pr=!1;function kc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Nc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function mr(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function hr(e,t,a){var o=e.updateQueue;if(o===null)return null;if(o=o.shared,(ht&2)!==0){var s=o.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),o.pending=t,t=os(e),mm(e,null,a),t}return rs(e,o,t,a),os(e)}function Ll(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var o=t.lanes;o&=e.pendingLanes,a|=o,t.lanes=a,Dn(e,a)}}function Bc(e,t){var a=e.updateQueue,o=e.alternate;if(o!==null&&(o=o.updateQueue,a===o)){var s=null,f=null;if(a=a.firstBaseUpdate,a!==null){do{var g={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};f===null?s=f=g:f=f.next=g,a=a.next}while(a!==null);f===null?s=f=t:f=f.next=t}else s=f=t;a={baseState:o.baseState,firstBaseUpdate:s,lastBaseUpdate:f,shared:o.shared,callbacks:o.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var _c=!1;function Hl(){if(_c){var e=zo;if(e!==null)throw e}}function Pl(e,t,a,o){_c=!1;var s=e.updateQueue;pr=!1;var f=s.firstBaseUpdate,g=s.lastBaseUpdate,C=s.shared.pending;if(C!==null){s.shared.pending=null;var k=C,F=k.next;k.next=null,g===null?f=F:g.next=F,g=k;var ie=e.alternate;ie!==null&&(ie=ie.updateQueue,C=ie.lastBaseUpdate,C!==g&&(C===null?ie.firstBaseUpdate=F:C.next=F,ie.lastBaseUpdate=k))}if(f!==null){var ce=s.baseState;g=0,ie=F=k=null,C=f;do{var W=C.lane&-536870913,Z=W!==C.lane;if(Z?(it&W)===W:(o&W)===W){W!==0&&W===Ao&&(_c=!0),ie!==null&&(ie=ie.next={lane:0,tag:C.tag,payload:C.payload,callback:null,next:null});e:{var je=e,ze=C;W=t;var xt=a;switch(ze.tag){case 1:if(je=ze.payload,typeof je=="function"){ce=je.call(xt,ce,W);break e}ce=je;break e;case 3:je.flags=je.flags&-65537|128;case 0:if(je=ze.payload,W=typeof je=="function"?je.call(xt,ce,W):je,W==null)break e;ce=y({},ce,W);break e;case 2:pr=!0}}W=C.callback,W!==null&&(e.flags|=64,Z&&(e.flags|=8192),Z=s.callbacks,Z===null?s.callbacks=[W]:Z.push(W))}else Z={lane:W,tag:C.tag,payload:C.payload,callback:C.callback,next:null},ie===null?(F=ie=Z,k=ce):ie=ie.next=Z,g|=W;if(C=C.next,C===null){if(C=s.shared.pending,C===null)break;Z=C,C=Z.next,Z.next=null,s.lastBaseUpdate=Z,s.shared.pending=null}}while(!0);ie===null&&(k=ce),s.baseState=k,s.firstBaseUpdate=F,s.lastBaseUpdate=ie,f===null&&(s.shared.lanes=0),Tr|=g,e.lanes=g,e.memoizedState=ce}}function Am(e,t){if(typeof e!="function")throw Error(i(191,e));e.call(t)}function zm(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Am(a[e],t)}var Do=U(null),ms=U(0);function Dm(e,t){e=tr,ne(ms,e),ne(Do,t),tr=e|t.baseLanes}function Lc(){ne(ms,tr),ne(Do,Do.current)}function Hc(){tr=ms.current,J(Do),J(ms)}var gr=0,Ie=null,bt=null,Kt=null,hs=!1,$o=!1,Jr=!1,gs=0,Ul=0,jo=null,g1=0;function It(){throw Error(i(321))}function Pc(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!$n(e[a],t[a]))return!1;return!0}function Uc(e,t,a,o,s,f){return gr=f,Ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,T.H=e===null||e.memoizedState===null?mh:hh,Jr=!1,f=a(o,s),Jr=!1,$o&&(f=jm(t,a,o,s)),$m(e),f}function $m(e){T.H=Cs;var t=bt!==null&&bt.next!==null;if(gr=0,Kt=bt=Ie=null,hs=!1,Ul=0,jo=null,t)throw Error(i(300));e===null||nn||(e=e.dependencies,e!==null&&us(e)&&(nn=!0))}function jm(e,t,a,o){Ie=e;var s=0;do{if($o&&(jo=null),Ul=0,$o=!1,25<=s)throw Error(i(301));if(s+=1,Kt=bt=null,e.updateQueue!=null){var f=e.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}T.H=T1,f=t(a,o)}while($o);return f}function y1(){var e=T.H,t=e.useState()[0];return t=typeof t.then=="function"?ql(t):t,e=e.useState()[0],(bt!==null?bt.memoizedState:null)!==e&&(Ie.flags|=1024),t}function qc(){var e=gs!==0;return gs=0,e}function Ic(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Vc(e){if(hs){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}hs=!1}gr=0,Kt=bt=Ie=null,$o=!1,Ul=gs=0,jo=null}function En(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Kt===null?Ie.memoizedState=Kt=e:Kt=Kt.next=e,Kt}function Qt(){if(bt===null){var e=Ie.alternate;e=e!==null?e.memoizedState:null}else e=bt.next;var t=Kt===null?Ie.memoizedState:Kt.next;if(t!==null)Kt=t,bt=e;else{if(e===null)throw Ie.alternate===null?Error(i(467)):Error(i(310));bt=e,e={memoizedState:bt.memoizedState,baseState:bt.baseState,baseQueue:bt.baseQueue,queue:bt.queue,next:null},Kt===null?Ie.memoizedState=Kt=e:Kt=Kt.next=e}return Kt}function Yc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ql(e){var t=Ul;return Ul+=1,jo===null&&(jo=[]),e=Om(jo,e,t),t=Ie,(Kt===null?t.memoizedState:Kt.next)===null&&(t=t.alternate,T.H=t===null||t.memoizedState===null?mh:hh),e}function ys(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ql(e);if(e.$$typeof===D)return dn(e)}throw Error(i(438,String(e)))}function Gc(e){var t=null,a=Ie.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var o=Ie.alternate;o!==null&&(o=o.updateQueue,o!==null&&(o=o.memoCache,o!=null&&(t={data:o.data.map(function(s){return s.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Yc(),Ie.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),o=0;o<e;o++)a[o]=b;return t.index++,a}function Qa(e,t){return typeof t=="function"?t(e):t}function vs(e){var t=Qt();return Xc(t,bt,e)}function Xc(e,t,a){var o=e.queue;if(o===null)throw Error(i(311));o.lastRenderedReducer=a;var s=e.baseQueue,f=o.pending;if(f!==null){if(s!==null){var g=s.next;s.next=f.next,f.next=g}t.baseQueue=s=f,o.pending=null}if(f=e.baseState,s===null)e.memoizedState=f;else{t=s.next;var C=g=null,k=null,F=t,ie=!1;do{var ce=F.lane&-536870913;if(ce!==F.lane?(it&ce)===ce:(gr&ce)===ce){var W=F.revertLane;if(W===0)k!==null&&(k=k.next={lane:0,revertLane:0,action:F.action,hasEagerState:F.hasEagerState,eagerState:F.eagerState,next:null}),ce===Ao&&(ie=!0);else if((gr&W)===W){F=F.next,W===Ao&&(ie=!0);continue}else ce={lane:0,revertLane:F.revertLane,action:F.action,hasEagerState:F.hasEagerState,eagerState:F.eagerState,next:null},k===null?(C=k=ce,g=f):k=k.next=ce,Ie.lanes|=W,Tr|=W;ce=F.action,Jr&&a(f,ce),f=F.hasEagerState?F.eagerState:a(f,ce)}else W={lane:ce,revertLane:F.revertLane,action:F.action,hasEagerState:F.hasEagerState,eagerState:F.eagerState,next:null},k===null?(C=k=W,g=f):k=k.next=W,Ie.lanes|=ce,Tr|=ce;F=F.next}while(F!==null&&F!==t);if(k===null?g=f:k.next=C,!$n(f,e.memoizedState)&&(nn=!0,ie&&(a=zo,a!==null)))throw a;e.memoizedState=f,e.baseState=g,e.baseQueue=k,o.lastRenderedState=f}return s===null&&(o.lanes=0),[e.memoizedState,o.dispatch]}function Kc(e){var t=Qt(),a=t.queue;if(a===null)throw Error(i(311));a.lastRenderedReducer=e;var o=a.dispatch,s=a.pending,f=t.memoizedState;if(s!==null){a.pending=null;var g=s=s.next;do f=e(f,g.action),g=g.next;while(g!==s);$n(f,t.memoizedState)||(nn=!0),t.memoizedState=f,t.baseQueue===null&&(t.baseState=f),a.lastRenderedState=f}return[f,o]}function km(e,t,a){var o=Ie,s=Qt(),f=ft;if(f){if(a===void 0)throw Error(i(407));a=a()}else a=t();var g=!$n((bt||s).memoizedState,a);g&&(s.memoizedState=a,nn=!0),s=s.queue;var C=_m.bind(null,o,s,e);if(Il(2048,8,C,[e]),s.getSnapshot!==t||g||Kt!==null&&Kt.memoizedState.tag&1){if(o.flags|=2048,ko(9,bs(),Bm.bind(null,o,s,a,t),null),Ot===null)throw Error(i(349));f||(gr&124)!==0||Nm(o,t,a)}return a}function Nm(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=Ie.updateQueue,t===null?(t=Yc(),Ie.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function Bm(e,t,a,o){t.value=a,t.getSnapshot=o,Lm(t)&&Hm(e)}function _m(e,t,a){return a(function(){Lm(t)&&Hm(e)})}function Lm(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!$n(e,a)}catch{return!0}}function Hm(e){var t=Ro(e,2);t!==null&&Ln(t,e,2)}function Qc(e){var t=En();if(typeof e=="function"){var a=e;if(e=a(),Jr){Ye(!0);try{a()}finally{Ye(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qa,lastRenderedState:e},t}function Pm(e,t,a,o){return e.baseState=a,Xc(e,bt,typeof o=="function"?o:Qa)}function v1(e,t,a,o,s){if(xs(e))throw Error(i(485));if(e=t.action,e!==null){var f={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){f.listeners.push(g)}};T.T!==null?a(!0):f.isTransition=!1,o(f),a=t.pending,a===null?(f.next=t.pending=f,Um(t,f)):(f.next=a.next,t.pending=a.next=f)}}function Um(e,t){var a=t.action,o=t.payload,s=e.state;if(t.isTransition){var f=T.T,g={};T.T=g;try{var C=a(s,o),k=T.S;k!==null&&k(g,C),qm(e,t,C)}catch(F){Fc(e,t,F)}finally{T.T=f}}else try{f=a(s,o),qm(e,t,f)}catch(F){Fc(e,t,F)}}function qm(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(o){Im(e,t,o)},function(o){return Fc(e,t,o)}):Im(e,t,a)}function Im(e,t,a){t.status="fulfilled",t.value=a,Vm(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Um(e,a)))}function Fc(e,t,a){var o=e.pending;if(e.pending=null,o!==null){o=o.next;do t.status="rejected",t.reason=a,Vm(t),t=t.next;while(t!==o)}e.action=null}function Vm(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Ym(e,t){return t}function Gm(e,t){if(ft){var a=Ot.formState;if(a!==null){e:{var o=Ie;if(ft){if(_t){t:{for(var s=_t,f=Ra;s.nodeType!==8;){if(!f){s=null;break t}if(s=xa(s.nextSibling),s===null){s=null;break t}}f=s.data,s=f==="F!"||f==="F"?s:null}if(s){_t=xa(s.nextSibling),o=s.data==="F!";break e}}Qr(o)}o=!1}o&&(t=a[0])}}return a=En(),a.memoizedState=a.baseState=t,o={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ym,lastRenderedState:t},a.queue=o,a=fh.bind(null,Ie,o),o.dispatch=a,o=Qc(!1),f=tf.bind(null,Ie,!1,o.queue),o=En(),s={state:t,dispatch:null,action:e,pending:null},o.queue=s,a=v1.bind(null,Ie,s,f,a),s.dispatch=a,o.memoizedState=e,[t,a,!1]}function Xm(e){var t=Qt();return Km(t,bt,e)}function Km(e,t,a){if(t=Xc(e,t,Ym)[0],e=vs(Qa)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var o=ql(t)}catch(g){throw g===Bl?ds:g}else o=t;t=Qt();var s=t.queue,f=s.dispatch;return a!==t.memoizedState&&(Ie.flags|=2048,ko(9,bs(),b1.bind(null,s,a),null)),[o,f,e]}function b1(e,t){e.action=t}function Qm(e){var t=Qt(),a=bt;if(a!==null)return Km(t,a,e);Qt(),t=t.memoizedState,a=Qt();var o=a.queue.dispatch;return a.memoizedState=e,[t,o,!1]}function ko(e,t,a,o){return e={tag:e,create:a,deps:o,inst:t,next:null},t=Ie.updateQueue,t===null&&(t=Yc(),Ie.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(o=a.next,a.next=e,e.next=o,t.lastEffect=e),e}function bs(){return{destroy:void 0,resource:void 0}}function Fm(){return Qt().memoizedState}function Ss(e,t,a,o){var s=En();o=o===void 0?null:o,Ie.flags|=e,s.memoizedState=ko(1|t,bs(),a,o)}function Il(e,t,a,o){var s=Qt();o=o===void 0?null:o;var f=s.memoizedState.inst;bt!==null&&o!==null&&Pc(o,bt.memoizedState.deps)?s.memoizedState=ko(t,f,a,o):(Ie.flags|=e,s.memoizedState=ko(1|t,f,a,o))}function Wm(e,t){Ss(8390656,8,e,t)}function Zm(e,t){Il(2048,8,e,t)}function Jm(e,t){return Il(4,2,e,t)}function eh(e,t){return Il(4,4,e,t)}function th(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function nh(e,t,a){a=a!=null?a.concat([e]):null,Il(4,4,th.bind(null,t,e),a)}function Wc(){}function ah(e,t){var a=Qt();t=t===void 0?null:t;var o=a.memoizedState;return t!==null&&Pc(t,o[1])?o[0]:(a.memoizedState=[e,t],e)}function rh(e,t){var a=Qt();t=t===void 0?null:t;var o=a.memoizedState;if(t!==null&&Pc(t,o[1]))return o[0];if(o=e(),Jr){Ye(!0);try{e()}finally{Ye(!1)}}return a.memoizedState=[o,t],o}function Zc(e,t,a){return a===void 0||(gr&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=ig(),Ie.lanes|=e,Tr|=e,a)}function oh(e,t,a,o){return $n(a,t)?a:Do.current!==null?(e=Zc(e,a,o),$n(e,t)||(nn=!0),e):(gr&42)===0?(nn=!0,e.memoizedState=a):(e=ig(),Ie.lanes|=e,Tr|=e,t)}function lh(e,t,a,o,s){var f=L.p;L.p=f!==0&&8>f?f:8;var g=T.T,C={};T.T=C,tf(e,!1,t,a);try{var k=s(),F=T.S;if(F!==null&&F(C,k),k!==null&&typeof k=="object"&&typeof k.then=="function"){var ie=h1(k,o);Vl(e,t,ie,_n(e))}else Vl(e,t,o,_n(e))}catch(ce){Vl(e,t,{then:function(){},status:"rejected",reason:ce},_n())}finally{L.p=f,T.T=g}}function S1(){}function Jc(e,t,a,o){if(e.tag!==5)throw Error(i(476));var s=ih(e).queue;lh(e,s,t,Y,a===null?S1:function(){return sh(e),a(o)})}function ih(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:Y,baseState:Y,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qa,lastRenderedState:Y},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qa,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function sh(e){var t=ih(e).next.queue;Vl(e,t,{},_n())}function ef(){return dn(si)}function uh(){return Qt().memoizedState}function ch(){return Qt().memoizedState}function x1(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=_n();e=mr(a);var o=hr(t,e,a);o!==null&&(Ln(o,t,a),Ll(o,t,a)),t={cache:zc()},e.payload=t;return}t=t.return}}function C1(e,t,a){var o=_n();a={lane:o,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},xs(e)?dh(t,a):(a=Sc(e,t,a,o),a!==null&&(Ln(a,e,o),ph(a,t,o)))}function fh(e,t,a){var o=_n();Vl(e,t,a,o)}function Vl(e,t,a,o){var s={lane:o,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(xs(e))dh(t,s);else{var f=e.alternate;if(e.lanes===0&&(f===null||f.lanes===0)&&(f=t.lastRenderedReducer,f!==null))try{var g=t.lastRenderedState,C=f(g,a);if(s.hasEagerState=!0,s.eagerState=C,$n(C,g))return rs(e,t,s,0),Ot===null&&as(),!1}catch{}finally{}if(a=Sc(e,t,s,o),a!==null)return Ln(a,e,o),ph(a,t,o),!0}return!1}function tf(e,t,a,o){if(o={lane:2,revertLane:kf(),action:o,hasEagerState:!1,eagerState:null,next:null},xs(e)){if(t)throw Error(i(479))}else t=Sc(e,a,o,2),t!==null&&Ln(t,e,2)}function xs(e){var t=e.alternate;return e===Ie||t!==null&&t===Ie}function dh(e,t){$o=hs=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function ph(e,t,a){if((a&4194048)!==0){var o=t.lanes;o&=e.pendingLanes,a|=o,t.lanes=a,Dn(e,a)}}var Cs={readContext:dn,use:ys,useCallback:It,useContext:It,useEffect:It,useImperativeHandle:It,useLayoutEffect:It,useInsertionEffect:It,useMemo:It,useReducer:It,useRef:It,useState:It,useDebugValue:It,useDeferredValue:It,useTransition:It,useSyncExternalStore:It,useId:It,useHostTransitionStatus:It,useFormState:It,useActionState:It,useOptimistic:It,useMemoCache:It,useCacheRefresh:It},mh={readContext:dn,use:ys,useCallback:function(e,t){return En().memoizedState=[e,t===void 0?null:t],e},useContext:dn,useEffect:Wm,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Ss(4194308,4,th.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Ss(4194308,4,e,t)},useInsertionEffect:function(e,t){Ss(4,2,e,t)},useMemo:function(e,t){var a=En();t=t===void 0?null:t;var o=e();if(Jr){Ye(!0);try{e()}finally{Ye(!1)}}return a.memoizedState=[o,t],o},useReducer:function(e,t,a){var o=En();if(a!==void 0){var s=a(t);if(Jr){Ye(!0);try{a(t)}finally{Ye(!1)}}}else s=t;return o.memoizedState=o.baseState=s,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:s},o.queue=e,e=e.dispatch=C1.bind(null,Ie,e),[o.memoizedState,e]},useRef:function(e){var t=En();return e={current:e},t.memoizedState=e},useState:function(e){e=Qc(e);var t=e.queue,a=fh.bind(null,Ie,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Wc,useDeferredValue:function(e,t){var a=En();return Zc(a,e,t)},useTransition:function(){var e=Qc(!1);return e=lh.bind(null,Ie,e.queue,!0,!1),En().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var o=Ie,s=En();if(ft){if(a===void 0)throw Error(i(407));a=a()}else{if(a=t(),Ot===null)throw Error(i(349));(it&124)!==0||Nm(o,t,a)}s.memoizedState=a;var f={value:a,getSnapshot:t};return s.queue=f,Wm(_m.bind(null,o,f,e),[e]),o.flags|=2048,ko(9,bs(),Bm.bind(null,o,f,a,t),null),a},useId:function(){var e=En(),t=Ot.identifierPrefix;if(ft){var a=Ga,o=Ya;a=(o&~(1<<32-et(o)-1)).toString(32)+a,t="«"+t+"R"+a,a=gs++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=g1++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:ef,useFormState:Gm,useActionState:Gm,useOptimistic:function(e){var t=En();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=tf.bind(null,Ie,!0,a),a.dispatch=t,[e,t]},useMemoCache:Gc,useCacheRefresh:function(){return En().memoizedState=x1.bind(null,Ie)}},hh={readContext:dn,use:ys,useCallback:ah,useContext:dn,useEffect:Zm,useImperativeHandle:nh,useInsertionEffect:Jm,useLayoutEffect:eh,useMemo:rh,useReducer:vs,useRef:Fm,useState:function(){return vs(Qa)},useDebugValue:Wc,useDeferredValue:function(e,t){var a=Qt();return oh(a,bt.memoizedState,e,t)},useTransition:function(){var e=vs(Qa)[0],t=Qt().memoizedState;return[typeof e=="boolean"?e:ql(e),t]},useSyncExternalStore:km,useId:uh,useHostTransitionStatus:ef,useFormState:Xm,useActionState:Xm,useOptimistic:function(e,t){var a=Qt();return Pm(a,bt,e,t)},useMemoCache:Gc,useCacheRefresh:ch},T1={readContext:dn,use:ys,useCallback:ah,useContext:dn,useEffect:Zm,useImperativeHandle:nh,useInsertionEffect:Jm,useLayoutEffect:eh,useMemo:rh,useReducer:Kc,useRef:Fm,useState:function(){return Kc(Qa)},useDebugValue:Wc,useDeferredValue:function(e,t){var a=Qt();return bt===null?Zc(a,e,t):oh(a,bt.memoizedState,e,t)},useTransition:function(){var e=Kc(Qa)[0],t=Qt().memoizedState;return[typeof e=="boolean"?e:ql(e),t]},useSyncExternalStore:km,useId:uh,useHostTransitionStatus:ef,useFormState:Qm,useActionState:Qm,useOptimistic:function(e,t){var a=Qt();return bt!==null?Pm(a,bt,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Gc,useCacheRefresh:ch},No=null,Yl=0;function Ts(e){var t=Yl;return Yl+=1,No===null&&(No=[]),Om(No,e,t)}function Gl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Es(e,t){throw t.$$typeof===x?Error(i(525)):(e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function gh(e){var t=e._init;return t(e._payload)}function yh(e){function t(q,H){if(e){var Q=q.deletions;Q===null?(q.deletions=[H],q.flags|=16):Q.push(H)}}function a(q,H){if(!e)return null;for(;H!==null;)t(q,H),H=H.sibling;return null}function o(q){for(var H=new Map;q!==null;)q.key!==null?H.set(q.key,q):H.set(q.index,q),q=q.sibling;return H}function s(q,H){return q=Va(q,H),q.index=0,q.sibling=null,q}function f(q,H,Q){return q.index=Q,e?(Q=q.alternate,Q!==null?(Q=Q.index,Q<H?(q.flags|=67108866,H):Q):(q.flags|=67108866,H)):(q.flags|=1048576,H)}function g(q){return e&&q.alternate===null&&(q.flags|=67108866),q}function C(q,H,Q,se){return H===null||H.tag!==6?(H=Cc(Q,q.mode,se),H.return=q,H):(H=s(H,Q),H.return=q,H)}function k(q,H,Q,se){var Se=Q.type;return Se===E?ie(q,H,Q.props.children,se,Q.key):H!==null&&(H.elementType===Se||typeof Se=="object"&&Se!==null&&Se.$$typeof===G&&gh(Se)===H.type)?(H=s(H,Q.props),Gl(H,Q),H.return=q,H):(H=ls(Q.type,Q.key,Q.props,null,q.mode,se),Gl(H,Q),H.return=q,H)}function F(q,H,Q,se){return H===null||H.tag!==4||H.stateNode.containerInfo!==Q.containerInfo||H.stateNode.implementation!==Q.implementation?(H=Tc(Q,q.mode,se),H.return=q,H):(H=s(H,Q.children||[]),H.return=q,H)}function ie(q,H,Q,se,Se){return H===null||H.tag!==7?(H=Yr(Q,q.mode,se,Se),H.return=q,H):(H=s(H,Q),H.return=q,H)}function ce(q,H,Q){if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return H=Cc(""+H,q.mode,Q),H.return=q,H;if(typeof H=="object"&&H!==null){switch(H.$$typeof){case R:return Q=ls(H.type,H.key,H.props,null,q.mode,Q),Gl(Q,H),Q.return=q,Q;case M:return H=Tc(H,q.mode,Q),H.return=q,H;case G:var se=H._init;return H=se(H._payload),ce(q,H,Q)}if(_(H)||V(H))return H=Yr(H,q.mode,Q,null),H.return=q,H;if(typeof H.then=="function")return ce(q,Ts(H),Q);if(H.$$typeof===D)return ce(q,cs(q,H),Q);Es(q,H)}return null}function W(q,H,Q,se){var Se=H!==null?H.key:null;if(typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint")return Se!==null?null:C(q,H,""+Q,se);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case R:return Q.key===Se?k(q,H,Q,se):null;case M:return Q.key===Se?F(q,H,Q,se):null;case G:return Se=Q._init,Q=Se(Q._payload),W(q,H,Q,se)}if(_(Q)||V(Q))return Se!==null?null:ie(q,H,Q,se,null);if(typeof Q.then=="function")return W(q,H,Ts(Q),se);if(Q.$$typeof===D)return W(q,H,cs(q,Q),se);Es(q,Q)}return null}function Z(q,H,Q,se,Se){if(typeof se=="string"&&se!==""||typeof se=="number"||typeof se=="bigint")return q=q.get(Q)||null,C(H,q,""+se,Se);if(typeof se=="object"&&se!==null){switch(se.$$typeof){case R:return q=q.get(se.key===null?Q:se.key)||null,k(H,q,se,Se);case M:return q=q.get(se.key===null?Q:se.key)||null,F(H,q,se,Se);case G:var Ge=se._init;return se=Ge(se._payload),Z(q,H,Q,se,Se)}if(_(se)||V(se))return q=q.get(Q)||null,ie(H,q,se,Se,null);if(typeof se.then=="function")return Z(q,H,Q,Ts(se),Se);if(se.$$typeof===D)return Z(q,H,Q,cs(H,se),Se);Es(H,se)}return null}function je(q,H,Q,se){for(var Se=null,Ge=null,Oe=H,De=H=0,rn=null;Oe!==null&&De<Q.length;De++){Oe.index>De?(rn=Oe,Oe=null):rn=Oe.sibling;var ct=W(q,Oe,Q[De],se);if(ct===null){Oe===null&&(Oe=rn);break}e&&Oe&&ct.alternate===null&&t(q,Oe),H=f(ct,H,De),Ge===null?Se=ct:Ge.sibling=ct,Ge=ct,Oe=rn}if(De===Q.length)return a(q,Oe),ft&&Xr(q,De),Se;if(Oe===null){for(;De<Q.length;De++)Oe=ce(q,Q[De],se),Oe!==null&&(H=f(Oe,H,De),Ge===null?Se=Oe:Ge.sibling=Oe,Ge=Oe);return ft&&Xr(q,De),Se}for(Oe=o(Oe);De<Q.length;De++)rn=Z(Oe,q,De,Q[De],se),rn!==null&&(e&&rn.alternate!==null&&Oe.delete(rn.key===null?De:rn.key),H=f(rn,H,De),Ge===null?Se=rn:Ge.sibling=rn,Ge=rn);return e&&Oe.forEach(function($r){return t(q,$r)}),ft&&Xr(q,De),Se}function ze(q,H,Q,se){if(Q==null)throw Error(i(151));for(var Se=null,Ge=null,Oe=H,De=H=0,rn=null,ct=Q.next();Oe!==null&&!ct.done;De++,ct=Q.next()){Oe.index>De?(rn=Oe,Oe=null):rn=Oe.sibling;var $r=W(q,Oe,ct.value,se);if($r===null){Oe===null&&(Oe=rn);break}e&&Oe&&$r.alternate===null&&t(q,Oe),H=f($r,H,De),Ge===null?Se=$r:Ge.sibling=$r,Ge=$r,Oe=rn}if(ct.done)return a(q,Oe),ft&&Xr(q,De),Se;if(Oe===null){for(;!ct.done;De++,ct=Q.next())ct=ce(q,ct.value,se),ct!==null&&(H=f(ct,H,De),Ge===null?Se=ct:Ge.sibling=ct,Ge=ct);return ft&&Xr(q,De),Se}for(Oe=o(Oe);!ct.done;De++,ct=Q.next())ct=Z(Oe,q,De,ct.value,se),ct!==null&&(e&&ct.alternate!==null&&Oe.delete(ct.key===null?De:ct.key),H=f(ct,H,De),Ge===null?Se=ct:Ge.sibling=ct,Ge=ct);return e&&Oe.forEach(function(ES){return t(q,ES)}),ft&&Xr(q,De),Se}function xt(q,H,Q,se){if(typeof Q=="object"&&Q!==null&&Q.type===E&&Q.key===null&&(Q=Q.props.children),typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case R:e:{for(var Se=Q.key;H!==null;){if(H.key===Se){if(Se=Q.type,Se===E){if(H.tag===7){a(q,H.sibling),se=s(H,Q.props.children),se.return=q,q=se;break e}}else if(H.elementType===Se||typeof Se=="object"&&Se!==null&&Se.$$typeof===G&&gh(Se)===H.type){a(q,H.sibling),se=s(H,Q.props),Gl(se,Q),se.return=q,q=se;break e}a(q,H);break}else t(q,H);H=H.sibling}Q.type===E?(se=Yr(Q.props.children,q.mode,se,Q.key),se.return=q,q=se):(se=ls(Q.type,Q.key,Q.props,null,q.mode,se),Gl(se,Q),se.return=q,q=se)}return g(q);case M:e:{for(Se=Q.key;H!==null;){if(H.key===Se)if(H.tag===4&&H.stateNode.containerInfo===Q.containerInfo&&H.stateNode.implementation===Q.implementation){a(q,H.sibling),se=s(H,Q.children||[]),se.return=q,q=se;break e}else{a(q,H);break}else t(q,H);H=H.sibling}se=Tc(Q,q.mode,se),se.return=q,q=se}return g(q);case G:return Se=Q._init,Q=Se(Q._payload),xt(q,H,Q,se)}if(_(Q))return je(q,H,Q,se);if(V(Q)){if(Se=V(Q),typeof Se!="function")throw Error(i(150));return Q=Se.call(Q),ze(q,H,Q,se)}if(typeof Q.then=="function")return xt(q,H,Ts(Q),se);if(Q.$$typeof===D)return xt(q,H,cs(q,Q),se);Es(q,Q)}return typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint"?(Q=""+Q,H!==null&&H.tag===6?(a(q,H.sibling),se=s(H,Q),se.return=q,q=se):(a(q,H),se=Cc(Q,q.mode,se),se.return=q,q=se),g(q)):a(q,H)}return function(q,H,Q,se){try{Yl=0;var Se=xt(q,H,Q,se);return No=null,Se}catch(Oe){if(Oe===Bl||Oe===ds)throw Oe;var Ge=jn(29,Oe,null,q.mode);return Ge.lanes=se,Ge.return=q,Ge}finally{}}}var Bo=yh(!0),vh=yh(!1),ra=U(null),Oa=null;function yr(e){var t=e.alternate;ne(en,en.current&1),ne(ra,e),Oa===null&&(t===null||Do.current!==null||t.memoizedState!==null)&&(Oa=e)}function bh(e){if(e.tag===22){if(ne(en,en.current),ne(ra,e),Oa===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Oa=e)}}else vr()}function vr(){ne(en,en.current),ne(ra,ra.current)}function Fa(e){J(ra),Oa===e&&(Oa=null),J(en)}var en=U(0);function Rs(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Gf(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function nf(e,t,a,o){t=e.memoizedState,a=a(o,t),a=a==null?t:y({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var af={enqueueSetState:function(e,t,a){e=e._reactInternals;var o=_n(),s=mr(o);s.payload=t,a!=null&&(s.callback=a),t=hr(e,s,o),t!==null&&(Ln(t,e,o),Ll(t,e,o))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var o=_n(),s=mr(o);s.tag=1,s.payload=t,a!=null&&(s.callback=a),t=hr(e,s,o),t!==null&&(Ln(t,e,o),Ll(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=_n(),o=mr(a);o.tag=2,t!=null&&(o.callback=t),t=hr(e,o,a),t!==null&&(Ln(t,e,a),Ll(t,e,a))}};function Sh(e,t,a,o,s,f,g){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(o,f,g):t.prototype&&t.prototype.isPureReactComponent?!wl(a,o)||!wl(s,f):!0}function xh(e,t,a,o){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,o),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,o),t.state!==e&&af.enqueueReplaceState(t,t.state,null)}function eo(e,t){var a=t;if("ref"in t){a={};for(var o in t)o!=="ref"&&(a[o]=t[o])}if(e=e.defaultProps){a===t&&(a=y({},a));for(var s in e)a[s]===void 0&&(a[s]=e[s])}return a}var Os=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Ch(e){Os(e)}function Th(e){console.error(e)}function Eh(e){Os(e)}function Ms(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(o){setTimeout(function(){throw o})}}function Rh(e,t,a){try{var o=e.onCaughtError;o(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function rf(e,t,a){return a=mr(a),a.tag=3,a.payload={element:null},a.callback=function(){Ms(e,t)},a}function Oh(e){return e=mr(e),e.tag=3,e}function Mh(e,t,a,o){var s=a.type.getDerivedStateFromError;if(typeof s=="function"){var f=o.value;e.payload=function(){return s(f)},e.callback=function(){Rh(t,a,o)}}var g=a.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(e.callback=function(){Rh(t,a,o),typeof s!="function"&&(Er===null?Er=new Set([this]):Er.add(this));var C=o.stack;this.componentDidCatch(o.value,{componentStack:C!==null?C:""})})}function E1(e,t,a,o,s){if(a.flags|=32768,o!==null&&typeof o=="object"&&typeof o.then=="function"){if(t=a.alternate,t!==null&&jl(t,a,s,!0),a=ra.current,a!==null){switch(a.tag){case 13:return Oa===null?Af():a.alternate===null&&Lt===0&&(Lt=3),a.flags&=-257,a.flags|=65536,a.lanes=s,o===jc?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([o]):t.add(o),Df(e,o,s)),!1;case 22:return a.flags|=65536,o===jc?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([o])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([o]):a.add(o)),Df(e,o,s)),!1}throw Error(i(435,a.tag))}return Df(e,o,s),Af(),!1}if(ft)return t=ra.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=s,o!==Oc&&(e=Error(i(422),{cause:o}),$l(ea(e,a)))):(o!==Oc&&(t=Error(i(423),{cause:o}),$l(ea(t,a))),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,o=ea(o,a),s=rf(e.stateNode,o,s),Bc(e,s),Lt!==4&&(Lt=2)),!1;var f=Error(i(520),{cause:o});if(f=ea(f,a),Jl===null?Jl=[f]:Jl.push(f),Lt!==4&&(Lt=2),t===null)return!0;o=ea(o,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=s&-s,a.lanes|=e,e=rf(a.stateNode,o,e),Bc(a,e),!1;case 1:if(t=a.type,f=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Er===null||!Er.has(f))))return a.flags|=65536,s&=-s,a.lanes|=s,s=Oh(s),Mh(s,e,a,o),Bc(a,s),!1}a=a.return}while(a!==null);return!1}var wh=Error(i(461)),nn=!1;function on(e,t,a,o){t.child=e===null?vh(t,null,a,o):Bo(t,e.child,a,o)}function Ah(e,t,a,o,s){a=a.render;var f=t.ref;if("ref"in o){var g={};for(var C in o)C!=="ref"&&(g[C]=o[C])}else g=o;return Wr(t),o=Uc(e,t,a,g,f,s),C=qc(),e!==null&&!nn?(Ic(e,t,s),Wa(e,t,s)):(ft&&C&&Ec(t),t.flags|=1,on(e,t,o,s),t.child)}function zh(e,t,a,o,s){if(e===null){var f=a.type;return typeof f=="function"&&!xc(f)&&f.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=f,Dh(e,t,f,o,s)):(e=ls(a.type,null,o,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(f=e.child,!pf(e,s)){var g=f.memoizedProps;if(a=a.compare,a=a!==null?a:wl,a(g,o)&&e.ref===t.ref)return Wa(e,t,s)}return t.flags|=1,e=Va(f,o),e.ref=t.ref,e.return=t,t.child=e}function Dh(e,t,a,o,s){if(e!==null){var f=e.memoizedProps;if(wl(f,o)&&e.ref===t.ref)if(nn=!1,t.pendingProps=o=f,pf(e,s))(e.flags&131072)!==0&&(nn=!0);else return t.lanes=e.lanes,Wa(e,t,s)}return of(e,t,a,o,s)}function $h(e,t,a){var o=t.pendingProps,s=o.children,f=e!==null?e.memoizedState:null;if(o.mode==="hidden"){if((t.flags&128)!==0){if(o=f!==null?f.baseLanes|a:a,e!==null){for(s=t.child=e.child,f=0;s!==null;)f=f|s.lanes|s.childLanes,s=s.sibling;t.childLanes=f&~o}else t.childLanes=0,t.child=null;return jh(e,t,o,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&fs(t,f!==null?f.cachePool:null),f!==null?Dm(t,f):Lc(),bh(t);else return t.lanes=t.childLanes=536870912,jh(e,t,f!==null?f.baseLanes|a:a,a)}else f!==null?(fs(t,f.cachePool),Dm(t,f),vr(),t.memoizedState=null):(e!==null&&fs(t,null),Lc(),vr());return on(e,t,s,a),t.child}function jh(e,t,a,o){var s=$c();return s=s===null?null:{parent:Jt._currentValue,pool:s},t.memoizedState={baseLanes:a,cachePool:s},e!==null&&fs(t,null),Lc(),bh(t),e!==null&&jl(e,t,o,!0),null}function ws(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(i(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function of(e,t,a,o,s){return Wr(t),a=Uc(e,t,a,o,void 0,s),o=qc(),e!==null&&!nn?(Ic(e,t,s),Wa(e,t,s)):(ft&&o&&Ec(t),t.flags|=1,on(e,t,a,s),t.child)}function kh(e,t,a,o,s,f){return Wr(t),t.updateQueue=null,a=jm(t,o,a,s),$m(e),o=qc(),e!==null&&!nn?(Ic(e,t,f),Wa(e,t,f)):(ft&&o&&Ec(t),t.flags|=1,on(e,t,a,f),t.child)}function Nh(e,t,a,o,s){if(Wr(t),t.stateNode===null){var f=Oo,g=a.contextType;typeof g=="object"&&g!==null&&(f=dn(g)),f=new a(o,f),t.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=af,t.stateNode=f,f._reactInternals=t,f=t.stateNode,f.props=o,f.state=t.memoizedState,f.refs={},kc(t),g=a.contextType,f.context=typeof g=="object"&&g!==null?dn(g):Oo,f.state=t.memoizedState,g=a.getDerivedStateFromProps,typeof g=="function"&&(nf(t,a,g,o),f.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(g=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),g!==f.state&&af.enqueueReplaceState(f,f.state,null),Pl(t,o,f,s),Hl(),f.state=t.memoizedState),typeof f.componentDidMount=="function"&&(t.flags|=4194308),o=!0}else if(e===null){f=t.stateNode;var C=t.memoizedProps,k=eo(a,C);f.props=k;var F=f.context,ie=a.contextType;g=Oo,typeof ie=="object"&&ie!==null&&(g=dn(ie));var ce=a.getDerivedStateFromProps;ie=typeof ce=="function"||typeof f.getSnapshotBeforeUpdate=="function",C=t.pendingProps!==C,ie||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(C||F!==g)&&xh(t,f,o,g),pr=!1;var W=t.memoizedState;f.state=W,Pl(t,o,f,s),Hl(),F=t.memoizedState,C||W!==F||pr?(typeof ce=="function"&&(nf(t,a,ce,o),F=t.memoizedState),(k=pr||Sh(t,a,k,o,W,F,g))?(ie||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(t.flags|=4194308)):(typeof f.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=o,t.memoizedState=F),f.props=o,f.state=F,f.context=g,o=k):(typeof f.componentDidMount=="function"&&(t.flags|=4194308),o=!1)}else{f=t.stateNode,Nc(e,t),g=t.memoizedProps,ie=eo(a,g),f.props=ie,ce=t.pendingProps,W=f.context,F=a.contextType,k=Oo,typeof F=="object"&&F!==null&&(k=dn(F)),C=a.getDerivedStateFromProps,(F=typeof C=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==ce||W!==k)&&xh(t,f,o,k),pr=!1,W=t.memoizedState,f.state=W,Pl(t,o,f,s),Hl();var Z=t.memoizedState;g!==ce||W!==Z||pr||e!==null&&e.dependencies!==null&&us(e.dependencies)?(typeof C=="function"&&(nf(t,a,C,o),Z=t.memoizedState),(ie=pr||Sh(t,a,ie,o,W,Z,k)||e!==null&&e.dependencies!==null&&us(e.dependencies))?(F||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(o,Z,k),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(o,Z,k)),typeof f.componentDidUpdate=="function"&&(t.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&W===e.memoizedState||(t.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&W===e.memoizedState||(t.flags|=1024),t.memoizedProps=o,t.memoizedState=Z),f.props=o,f.state=Z,f.context=k,o=ie):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&W===e.memoizedState||(t.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&W===e.memoizedState||(t.flags|=1024),o=!1)}return f=o,ws(e,t),o=(t.flags&128)!==0,f||o?(f=t.stateNode,a=o&&typeof a.getDerivedStateFromError!="function"?null:f.render(),t.flags|=1,e!==null&&o?(t.child=Bo(t,e.child,null,s),t.child=Bo(t,null,a,s)):on(e,t,a,s),t.memoizedState=f.state,e=t.child):e=Wa(e,t,s),e}function Bh(e,t,a,o){return Dl(),t.flags|=256,on(e,t,a,o),t.child}var lf={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function sf(e){return{baseLanes:e,cachePool:Tm()}}function uf(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=oa),e}function _h(e,t,a){var o=t.pendingProps,s=!1,f=(t.flags&128)!==0,g;if((g=f)||(g=e!==null&&e.memoizedState===null?!1:(en.current&2)!==0),g&&(s=!0,t.flags&=-129),g=(t.flags&32)!==0,t.flags&=-33,e===null){if(ft){if(s?yr(t):vr(),ft){var C=_t,k;if(k=C){e:{for(k=C,C=Ra;k.nodeType!==8;){if(!C){C=null;break e}if(k=xa(k.nextSibling),k===null){C=null;break e}}C=k}C!==null?(t.memoizedState={dehydrated:C,treeContext:Gr!==null?{id:Ya,overflow:Ga}:null,retryLane:536870912,hydrationErrors:null},k=jn(18,null,null,0),k.stateNode=C,k.return=t,t.child=k,Sn=t,_t=null,k=!0):k=!1}k||Qr(t)}if(C=t.memoizedState,C!==null&&(C=C.dehydrated,C!==null))return Gf(C)?t.lanes=32:t.lanes=536870912,null;Fa(t)}return C=o.children,o=o.fallback,s?(vr(),s=t.mode,C=As({mode:"hidden",children:C},s),o=Yr(o,s,a,null),C.return=t,o.return=t,C.sibling=o,t.child=C,s=t.child,s.memoizedState=sf(a),s.childLanes=uf(e,g,a),t.memoizedState=lf,o):(yr(t),cf(t,C))}if(k=e.memoizedState,k!==null&&(C=k.dehydrated,C!==null)){if(f)t.flags&256?(yr(t),t.flags&=-257,t=ff(e,t,a)):t.memoizedState!==null?(vr(),t.child=e.child,t.flags|=128,t=null):(vr(),s=o.fallback,C=t.mode,o=As({mode:"visible",children:o.children},C),s=Yr(s,C,a,null),s.flags|=2,o.return=t,s.return=t,o.sibling=s,t.child=o,Bo(t,e.child,null,a),o=t.child,o.memoizedState=sf(a),o.childLanes=uf(e,g,a),t.memoizedState=lf,t=s);else if(yr(t),Gf(C)){if(g=C.nextSibling&&C.nextSibling.dataset,g)var F=g.dgst;g=F,o=Error(i(419)),o.stack="",o.digest=g,$l({value:o,source:null,stack:null}),t=ff(e,t,a)}else if(nn||jl(e,t,a,!1),g=(a&e.childLanes)!==0,nn||g){if(g=Ot,g!==null&&(o=a&-a,o=(o&42)!==0?1:ur(o),o=(o&(g.suspendedLanes|a))!==0?0:o,o!==0&&o!==k.retryLane))throw k.retryLane=o,Ro(e,o),Ln(g,e,o),wh;C.data==="$?"||Af(),t=ff(e,t,a)}else C.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=k.treeContext,_t=xa(C.nextSibling),Sn=t,ft=!0,Kr=null,Ra=!1,e!==null&&(na[aa++]=Ya,na[aa++]=Ga,na[aa++]=Gr,Ya=e.id,Ga=e.overflow,Gr=t),t=cf(t,o.children),t.flags|=4096);return t}return s?(vr(),s=o.fallback,C=t.mode,k=e.child,F=k.sibling,o=Va(k,{mode:"hidden",children:o.children}),o.subtreeFlags=k.subtreeFlags&65011712,F!==null?s=Va(F,s):(s=Yr(s,C,a,null),s.flags|=2),s.return=t,o.return=t,o.sibling=s,t.child=o,o=s,s=t.child,C=e.child.memoizedState,C===null?C=sf(a):(k=C.cachePool,k!==null?(F=Jt._currentValue,k=k.parent!==F?{parent:F,pool:F}:k):k=Tm(),C={baseLanes:C.baseLanes|a,cachePool:k}),s.memoizedState=C,s.childLanes=uf(e,g,a),t.memoizedState=lf,o):(yr(t),a=e.child,e=a.sibling,a=Va(a,{mode:"visible",children:o.children}),a.return=t,a.sibling=null,e!==null&&(g=t.deletions,g===null?(t.deletions=[e],t.flags|=16):g.push(e)),t.child=a,t.memoizedState=null,a)}function cf(e,t){return t=As({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function As(e,t){return e=jn(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function ff(e,t,a){return Bo(t,e.child,null,a),e=cf(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Lh(e,t,a){e.lanes|=t;var o=e.alternate;o!==null&&(o.lanes|=t),wc(e.return,t,a)}function df(e,t,a,o,s){var f=e.memoizedState;f===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:o,tail:a,tailMode:s}:(f.isBackwards=t,f.rendering=null,f.renderingStartTime=0,f.last=o,f.tail=a,f.tailMode=s)}function Hh(e,t,a){var o=t.pendingProps,s=o.revealOrder,f=o.tail;if(on(e,t,o.children,a),o=en.current,(o&2)!==0)o=o&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Lh(e,a,t);else if(e.tag===19)Lh(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}switch(ne(en,o),s){case"forwards":for(a=t.child,s=null;a!==null;)e=a.alternate,e!==null&&Rs(e)===null&&(s=a),a=a.sibling;a=s,a===null?(s=t.child,t.child=null):(s=a.sibling,a.sibling=null),df(t,!1,s,a,f);break;case"backwards":for(a=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Rs(e)===null){t.child=s;break}e=s.sibling,s.sibling=a,a=s,s=e}df(t,!0,a,null,f);break;case"together":df(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wa(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Tr|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(jl(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,a=Va(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Va(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function pf(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&us(e)))}function R1(e,t,a){switch(t.tag){case 3:xe(t,t.stateNode.containerInfo),dr(t,Jt,e.memoizedState.cache),Dl();break;case 27:case 5:me(t);break;case 4:xe(t,t.stateNode.containerInfo);break;case 10:dr(t,t.type,t.memoizedProps.value);break;case 13:var o=t.memoizedState;if(o!==null)return o.dehydrated!==null?(yr(t),t.flags|=128,null):(a&t.child.childLanes)!==0?_h(e,t,a):(yr(t),e=Wa(e,t,a),e!==null?e.sibling:null);yr(t);break;case 19:var s=(e.flags&128)!==0;if(o=(a&t.childLanes)!==0,o||(jl(e,t,a,!1),o=(a&t.childLanes)!==0),s){if(o)return Hh(e,t,a);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),ne(en,en.current),o)break;return null;case 22:case 23:return t.lanes=0,$h(e,t,a);case 24:dr(t,Jt,e.memoizedState.cache)}return Wa(e,t,a)}function Ph(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)nn=!0;else{if(!pf(e,a)&&(t.flags&128)===0)return nn=!1,R1(e,t,a);nn=(e.flags&131072)!==0}else nn=!1,ft&&(t.flags&1048576)!==0&&gm(t,ss,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var o=t.elementType,s=o._init;if(o=s(o._payload),t.type=o,typeof o=="function")xc(o)?(e=eo(o,e),t.tag=1,t=Nh(null,t,o,e,a)):(t.tag=0,t=of(null,t,o,e,a));else{if(o!=null){if(s=o.$$typeof,s===$){t.tag=11,t=Ah(null,t,o,e,a);break e}else if(s===I){t.tag=14,t=zh(null,t,o,e,a);break e}}throw t=ee(o)||o,Error(i(306,t,""))}}return t;case 0:return of(e,t,t.type,t.pendingProps,a);case 1:return o=t.type,s=eo(o,t.pendingProps),Nh(e,t,o,s,a);case 3:e:{if(xe(t,t.stateNode.containerInfo),e===null)throw Error(i(387));o=t.pendingProps;var f=t.memoizedState;s=f.element,Nc(e,t),Pl(t,o,null,a);var g=t.memoizedState;if(o=g.cache,dr(t,Jt,o),o!==f.cache&&Ac(t,[Jt],a,!0),Hl(),o=g.element,f.isDehydrated)if(f={element:o,isDehydrated:!1,cache:g.cache},t.updateQueue.baseState=f,t.memoizedState=f,t.flags&256){t=Bh(e,t,o,a);break e}else if(o!==s){s=ea(Error(i(424)),t),$l(s),t=Bh(e,t,o,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(_t=xa(e.firstChild),Sn=t,ft=!0,Kr=null,Ra=!0,a=vh(t,null,o,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Dl(),o===s){t=Wa(e,t,a);break e}on(e,t,o,a)}t=t.child}return t;case 26:return ws(e,t),e===null?(a=Vg(t.type,null,t.pendingProps,null))?t.memoizedState=a:ft||(a=t.type,e=t.pendingProps,o=Is(le.current).createElement(a),o[zt]=t,o[Xt]=e,sn(o,a,e),te(o),t.stateNode=o):t.memoizedState=Vg(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return me(t),e===null&&ft&&(o=t.stateNode=Ug(t.type,t.pendingProps,le.current),Sn=t,Ra=!0,s=_t,Mr(t.type)?(Xf=s,_t=xa(o.firstChild)):_t=s),on(e,t,t.pendingProps.children,a),ws(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&ft&&((s=o=_t)&&(o=J1(o,t.type,t.pendingProps,Ra),o!==null?(t.stateNode=o,Sn=t,_t=xa(o.firstChild),Ra=!1,s=!0):s=!1),s||Qr(t)),me(t),s=t.type,f=t.pendingProps,g=e!==null?e.memoizedProps:null,o=f.children,If(s,f)?o=null:g!==null&&If(s,g)&&(t.flags|=32),t.memoizedState!==null&&(s=Uc(e,t,y1,null,null,a),si._currentValue=s),ws(e,t),on(e,t,o,a),t.child;case 6:return e===null&&ft&&((e=a=_t)&&(a=eS(a,t.pendingProps,Ra),a!==null?(t.stateNode=a,Sn=t,_t=null,e=!0):e=!1),e||Qr(t)),null;case 13:return _h(e,t,a);case 4:return xe(t,t.stateNode.containerInfo),o=t.pendingProps,e===null?t.child=Bo(t,null,o,a):on(e,t,o,a),t.child;case 11:return Ah(e,t,t.type,t.pendingProps,a);case 7:return on(e,t,t.pendingProps,a),t.child;case 8:return on(e,t,t.pendingProps.children,a),t.child;case 12:return on(e,t,t.pendingProps.children,a),t.child;case 10:return o=t.pendingProps,dr(t,t.type,o.value),on(e,t,o.children,a),t.child;case 9:return s=t.type._context,o=t.pendingProps.children,Wr(t),s=dn(s),o=o(s),t.flags|=1,on(e,t,o,a),t.child;case 14:return zh(e,t,t.type,t.pendingProps,a);case 15:return Dh(e,t,t.type,t.pendingProps,a);case 19:return Hh(e,t,a);case 31:return o=t.pendingProps,a=t.mode,o={mode:o.mode,children:o.children},e===null?(a=As(o,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Va(e.child,o),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return $h(e,t,a);case 24:return Wr(t),o=dn(Jt),e===null?(s=$c(),s===null&&(s=Ot,f=zc(),s.pooledCache=f,f.refCount++,f!==null&&(s.pooledCacheLanes|=a),s=f),t.memoizedState={parent:o,cache:s},kc(t),dr(t,Jt,s)):((e.lanes&a)!==0&&(Nc(e,t),Pl(t,null,null,a),Hl()),s=e.memoizedState,f=t.memoizedState,s.parent!==o?(s={parent:o,cache:o},t.memoizedState=s,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=s),dr(t,Jt,o)):(o=f.cache,dr(t,Jt,o),o!==s.cache&&Ac(t,[Jt],a,!0))),on(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function Za(e){e.flags|=4}function Uh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Qg(t)){if(t=ra.current,t!==null&&((it&4194048)===it?Oa!==null:(it&62914560)!==it&&(it&536870912)===0||t!==Oa))throw _l=jc,Em;e.flags|=8192}}function zs(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?po():536870912,e.lanes|=t,Po|=t)}function Xl(e,t){if(!ft)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var o=null;a!==null;)a.alternate!==null&&(o=a),a=a.sibling;o===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:o.sibling=null}}function Nt(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,o=0;if(t)for(var s=e.child;s!==null;)a|=s.lanes|s.childLanes,o|=s.subtreeFlags&65011712,o|=s.flags&65011712,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)a|=s.lanes|s.childLanes,o|=s.subtreeFlags,o|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=o,e.childLanes=a,t}function O1(e,t,a){var o=t.pendingProps;switch(Rc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Nt(t),null;case 1:return Nt(t),null;case 3:return a=t.stateNode,o=null,e!==null&&(o=e.memoizedState.cache),t.memoizedState.cache!==o&&(t.flags|=2048),Ka(Jt),be(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(zl(t)?Za(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,bm())),Nt(t),null;case 26:return a=t.memoizedState,e===null?(Za(t),a!==null?(Nt(t),Uh(t,a)):(Nt(t),t.flags&=-16777217)):a?a!==e.memoizedState?(Za(t),Nt(t),Uh(t,a)):(Nt(t),t.flags&=-16777217):(e.memoizedProps!==o&&Za(t),Nt(t),t.flags&=-16777217),null;case 27:Re(t),a=le.current;var s=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==o&&Za(t);else{if(!o){if(t.stateNode===null)throw Error(i(166));return Nt(t),null}e=fe.current,zl(t)?ym(t):(e=Ug(s,o,a),t.stateNode=e,Za(t))}return Nt(t),null;case 5:if(Re(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==o&&Za(t);else{if(!o){if(t.stateNode===null)throw Error(i(166));return Nt(t),null}if(e=fe.current,zl(t))ym(t);else{switch(s=Is(le.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof o.is=="string"?s.createElement("select",{is:o.is}):s.createElement("select"),o.multiple?e.multiple=!0:o.size&&(e.size=o.size);break;default:e=typeof o.is=="string"?s.createElement(a,{is:o.is}):s.createElement(a)}}e[zt]=t,e[Xt]=o;e:for(s=t.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===t)break e;for(;s.sibling===null;){if(s.return===null||s.return===t)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}t.stateNode=e;e:switch(sn(e,a,o),a){case"button":case"input":case"select":case"textarea":e=!!o.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Za(t)}}return Nt(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==o&&Za(t);else{if(typeof o!="string"&&t.stateNode===null)throw Error(i(166));if(e=le.current,zl(t)){if(e=t.stateNode,a=t.memoizedProps,o=null,s=Sn,s!==null)switch(s.tag){case 27:case 5:o=s.memoizedProps}e[zt]=t,e=!!(e.nodeValue===a||o!==null&&o.suppressHydrationWarning===!0||kg(e.nodeValue,a)),e||Qr(t)}else e=Is(e).createTextNode(o),e[zt]=t,t.stateNode=e}return Nt(t),null;case 13:if(o=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(s=zl(t),o!==null&&o.dehydrated!==null){if(e===null){if(!s)throw Error(i(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(i(317));s[zt]=t}else Dl(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Nt(t),s=!1}else s=bm(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return t.flags&256?(Fa(t),t):(Fa(t),null)}if(Fa(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=o!==null,e=e!==null&&e.memoizedState!==null,a){o=t.child,s=null,o.alternate!==null&&o.alternate.memoizedState!==null&&o.alternate.memoizedState.cachePool!==null&&(s=o.alternate.memoizedState.cachePool.pool);var f=null;o.memoizedState!==null&&o.memoizedState.cachePool!==null&&(f=o.memoizedState.cachePool.pool),f!==s&&(o.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),zs(t,t.updateQueue),Nt(t),null;case 4:return be(),e===null&&Lf(t.stateNode.containerInfo),Nt(t),null;case 10:return Ka(t.type),Nt(t),null;case 19:if(J(en),s=t.memoizedState,s===null)return Nt(t),null;if(o=(t.flags&128)!==0,f=s.rendering,f===null)if(o)Xl(s,!1);else{if(Lt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(f=Rs(e),f!==null){for(t.flags|=128,Xl(s,!1),e=f.updateQueue,t.updateQueue=e,zs(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)hm(a,e),a=a.sibling;return ne(en,en.current&1|2),t.child}e=e.sibling}s.tail!==null&&at()>js&&(t.flags|=128,o=!0,Xl(s,!1),t.lanes=4194304)}else{if(!o)if(e=Rs(f),e!==null){if(t.flags|=128,o=!0,e=e.updateQueue,t.updateQueue=e,zs(t,e),Xl(s,!0),s.tail===null&&s.tailMode==="hidden"&&!f.alternate&&!ft)return Nt(t),null}else 2*at()-s.renderingStartTime>js&&a!==536870912&&(t.flags|=128,o=!0,Xl(s,!1),t.lanes=4194304);s.isBackwards?(f.sibling=t.child,t.child=f):(e=s.last,e!==null?e.sibling=f:t.child=f,s.last=f)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=at(),t.sibling=null,e=en.current,ne(en,o?e&1|2:e&1),t):(Nt(t),null);case 22:case 23:return Fa(t),Hc(),o=t.memoizedState!==null,e!==null?e.memoizedState!==null!==o&&(t.flags|=8192):o&&(t.flags|=8192),o?(a&536870912)!==0&&(t.flags&128)===0&&(Nt(t),t.subtreeFlags&6&&(t.flags|=8192)):Nt(t),a=t.updateQueue,a!==null&&zs(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),o=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(o=t.memoizedState.cachePool.pool),o!==a&&(t.flags|=2048),e!==null&&J(Zr),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Ka(Jt),Nt(t),null;case 25:return null;case 30:return null}throw Error(i(156,t.tag))}function M1(e,t){switch(Rc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ka(Jt),be(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Re(t),null;case 13:if(Fa(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));Dl()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(en),null;case 4:return be(),null;case 10:return Ka(t.type),null;case 22:case 23:return Fa(t),Hc(),e!==null&&J(Zr),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Ka(Jt),null;case 25:return null;default:return null}}function qh(e,t){switch(Rc(t),t.tag){case 3:Ka(Jt),be();break;case 26:case 27:case 5:Re(t);break;case 4:be();break;case 13:Fa(t);break;case 19:J(en);break;case 10:Ka(t.type);break;case 22:case 23:Fa(t),Hc(),e!==null&&J(Zr);break;case 24:Ka(Jt)}}function Kl(e,t){try{var a=t.updateQueue,o=a!==null?a.lastEffect:null;if(o!==null){var s=o.next;a=s;do{if((a.tag&e)===e){o=void 0;var f=a.create,g=a.inst;o=f(),g.destroy=o}a=a.next}while(a!==s)}}catch(C){Tt(t,t.return,C)}}function br(e,t,a){try{var o=t.updateQueue,s=o!==null?o.lastEffect:null;if(s!==null){var f=s.next;o=f;do{if((o.tag&e)===e){var g=o.inst,C=g.destroy;if(C!==void 0){g.destroy=void 0,s=t;var k=a,F=C;try{F()}catch(ie){Tt(s,k,ie)}}}o=o.next}while(o!==f)}}catch(ie){Tt(t,t.return,ie)}}function Ih(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{zm(t,a)}catch(o){Tt(e,e.return,o)}}}function Vh(e,t,a){a.props=eo(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(o){Tt(e,t,o)}}function Ql(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var o=e.stateNode;break;case 30:o=e.stateNode;break;default:o=e.stateNode}typeof a=="function"?e.refCleanup=a(o):a.current=o}}catch(s){Tt(e,t,s)}}function Ma(e,t){var a=e.ref,o=e.refCleanup;if(a!==null)if(typeof o=="function")try{o()}catch(s){Tt(e,t,s)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(s){Tt(e,t,s)}else a.current=null}function Yh(e){var t=e.type,a=e.memoizedProps,o=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&o.focus();break e;case"img":a.src?o.src=a.src:a.srcSet&&(o.srcset=a.srcSet)}}catch(s){Tt(e,e.return,s)}}function mf(e,t,a){try{var o=e.stateNode;K1(o,e.type,a,t),o[Xt]=t}catch(s){Tt(e,e.return,s)}}function Gh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Mr(e.type)||e.tag===4}function hf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Gh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Mr(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function gf(e,t,a){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=qs));else if(o!==4&&(o===27&&Mr(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(gf(e,t,a),e=e.sibling;e!==null;)gf(e,t,a),e=e.sibling}function Ds(e,t,a){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(o!==4&&(o===27&&Mr(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Ds(e,t,a),e=e.sibling;e!==null;)Ds(e,t,a),e=e.sibling}function Xh(e){var t=e.stateNode,a=e.memoizedProps;try{for(var o=e.type,s=t.attributes;s.length;)t.removeAttributeNode(s[0]);sn(t,o,a),t[zt]=e,t[Xt]=a}catch(f){Tt(e,e.return,f)}}var Ja=!1,Vt=!1,yf=!1,Kh=typeof WeakSet=="function"?WeakSet:Set,an=null;function w1(e,t){if(e=e.containerInfo,Uf=Qs,e=om(e),mc(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var o=a.getSelection&&a.getSelection();if(o&&o.rangeCount!==0){a=o.anchorNode;var s=o.anchorOffset,f=o.focusNode;o=o.focusOffset;try{a.nodeType,f.nodeType}catch{a=null;break e}var g=0,C=-1,k=-1,F=0,ie=0,ce=e,W=null;t:for(;;){for(var Z;ce!==a||s!==0&&ce.nodeType!==3||(C=g+s),ce!==f||o!==0&&ce.nodeType!==3||(k=g+o),ce.nodeType===3&&(g+=ce.nodeValue.length),(Z=ce.firstChild)!==null;)W=ce,ce=Z;for(;;){if(ce===e)break t;if(W===a&&++F===s&&(C=g),W===f&&++ie===o&&(k=g),(Z=ce.nextSibling)!==null)break;ce=W,W=ce.parentNode}ce=Z}a=C===-1||k===-1?null:{start:C,end:k}}else a=null}a=a||{start:0,end:0}}else a=null;for(qf={focusedElem:e,selectionRange:a},Qs=!1,an=t;an!==null;)if(t=an,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,an=e;else for(;an!==null;){switch(t=an,f=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&f!==null){e=void 0,a=t,s=f.memoizedProps,f=f.memoizedState,o=a.stateNode;try{var je=eo(a.type,s,a.elementType===a.type);e=o.getSnapshotBeforeUpdate(je,f),o.__reactInternalSnapshotBeforeUpdate=e}catch(ze){Tt(a,a.return,ze)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Yf(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Yf(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(i(163))}if(e=t.sibling,e!==null){e.return=t.return,an=e;break}an=t.return}}function Qh(e,t,a){var o=a.flags;switch(a.tag){case 0:case 11:case 15:Sr(e,a),o&4&&Kl(5,a);break;case 1:if(Sr(e,a),o&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(g){Tt(a,a.return,g)}else{var s=eo(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(s,t,e.__reactInternalSnapshotBeforeUpdate)}catch(g){Tt(a,a.return,g)}}o&64&&Ih(a),o&512&&Ql(a,a.return);break;case 3:if(Sr(e,a),o&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{zm(e,t)}catch(g){Tt(a,a.return,g)}}break;case 27:t===null&&o&4&&Xh(a);case 26:case 5:Sr(e,a),t===null&&o&4&&Yh(a),o&512&&Ql(a,a.return);break;case 12:Sr(e,a);break;case 13:Sr(e,a),o&4&&Zh(e,a),o&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=_1.bind(null,a),tS(e,a))));break;case 22:if(o=a.memoizedState!==null||Ja,!o){t=t!==null&&t.memoizedState!==null||Vt,s=Ja;var f=Vt;Ja=o,(Vt=t)&&!f?xr(e,a,(a.subtreeFlags&8772)!==0):Sr(e,a),Ja=s,Vt=f}break;case 30:break;default:Sr(e,a)}}function Fh(e){var t=e.alternate;t!==null&&(e.alternate=null,Fh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Bt(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var $t=null,Rn=!1;function er(e,t,a){for(a=a.child;a!==null;)Wh(e,t,a),a=a.sibling}function Wh(e,t,a){if(ke&&typeof ke.onCommitFiberUnmount=="function")try{ke.onCommitFiberUnmount(rt,a)}catch{}switch(a.tag){case 26:Vt||Ma(a,t),er(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Vt||Ma(a,t);var o=$t,s=Rn;Mr(a.type)&&($t=a.stateNode,Rn=!1),er(e,t,a),ri(a.stateNode),$t=o,Rn=s;break;case 5:Vt||Ma(a,t);case 6:if(o=$t,s=Rn,$t=null,er(e,t,a),$t=o,Rn=s,$t!==null)if(Rn)try{($t.nodeType===9?$t.body:$t.nodeName==="HTML"?$t.ownerDocument.body:$t).removeChild(a.stateNode)}catch(f){Tt(a,t,f)}else try{$t.removeChild(a.stateNode)}catch(f){Tt(a,t,f)}break;case 18:$t!==null&&(Rn?(e=$t,Hg(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),di(e)):Hg($t,a.stateNode));break;case 4:o=$t,s=Rn,$t=a.stateNode.containerInfo,Rn=!0,er(e,t,a),$t=o,Rn=s;break;case 0:case 11:case 14:case 15:Vt||br(2,a,t),Vt||br(4,a,t),er(e,t,a);break;case 1:Vt||(Ma(a,t),o=a.stateNode,typeof o.componentWillUnmount=="function"&&Vh(a,t,o)),er(e,t,a);break;case 21:er(e,t,a);break;case 22:Vt=(o=Vt)||a.memoizedState!==null,er(e,t,a),Vt=o;break;default:er(e,t,a)}}function Zh(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{di(e)}catch(a){Tt(t,t.return,a)}}function A1(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Kh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Kh),t;default:throw Error(i(435,e.tag))}}function vf(e,t){var a=A1(e);t.forEach(function(o){var s=L1.bind(null,e,o);a.has(o)||(a.add(o),o.then(s,s))})}function kn(e,t){var a=t.deletions;if(a!==null)for(var o=0;o<a.length;o++){var s=a[o],f=e,g=t,C=g;e:for(;C!==null;){switch(C.tag){case 27:if(Mr(C.type)){$t=C.stateNode,Rn=!1;break e}break;case 5:$t=C.stateNode,Rn=!1;break e;case 3:case 4:$t=C.stateNode.containerInfo,Rn=!0;break e}C=C.return}if($t===null)throw Error(i(160));Wh(f,g,s),$t=null,Rn=!1,f=s.alternate,f!==null&&(f.return=null),s.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Jh(t,e),t=t.sibling}var Sa=null;function Jh(e,t){var a=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:kn(t,e),Nn(e),o&4&&(br(3,e,e.return),Kl(3,e),br(5,e,e.return));break;case 1:kn(t,e),Nn(e),o&512&&(Vt||a===null||Ma(a,a.return)),o&64&&Ja&&(e=e.updateQueue,e!==null&&(o=e.callbacks,o!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?o:a.concat(o))));break;case 26:var s=Sa;if(kn(t,e),Nn(e),o&512&&(Vt||a===null||Ma(a,a.return)),o&4){var f=a!==null?a.memoizedState:null;if(o=e.memoizedState,a===null)if(o===null)if(e.stateNode===null){e:{o=e.type,a=e.memoizedProps,s=s.ownerDocument||s;t:switch(o){case"title":f=s.getElementsByTagName("title")[0],(!f||f[Qn]||f[zt]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=s.createElement(o),s.head.insertBefore(f,s.querySelector("head > title"))),sn(f,o,a),f[zt]=e,te(f),o=f;break e;case"link":var g=Xg("link","href",s).get(o+(a.href||""));if(g){for(var C=0;C<g.length;C++)if(f=g[C],f.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&f.getAttribute("rel")===(a.rel==null?null:a.rel)&&f.getAttribute("title")===(a.title==null?null:a.title)&&f.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){g.splice(C,1);break t}}f=s.createElement(o),sn(f,o,a),s.head.appendChild(f);break;case"meta":if(g=Xg("meta","content",s).get(o+(a.content||""))){for(C=0;C<g.length;C++)if(f=g[C],f.getAttribute("content")===(a.content==null?null:""+a.content)&&f.getAttribute("name")===(a.name==null?null:a.name)&&f.getAttribute("property")===(a.property==null?null:a.property)&&f.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&f.getAttribute("charset")===(a.charSet==null?null:a.charSet)){g.splice(C,1);break t}}f=s.createElement(o),sn(f,o,a),s.head.appendChild(f);break;default:throw Error(i(468,o))}f[zt]=e,te(f),o=f}e.stateNode=o}else Kg(s,e.type,e.stateNode);else e.stateNode=Gg(s,o,e.memoizedProps);else f!==o?(f===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):f.count--,o===null?Kg(s,e.type,e.stateNode):Gg(s,o,e.memoizedProps)):o===null&&e.stateNode!==null&&mf(e,e.memoizedProps,a.memoizedProps)}break;case 27:kn(t,e),Nn(e),o&512&&(Vt||a===null||Ma(a,a.return)),a!==null&&o&4&&mf(e,e.memoizedProps,a.memoizedProps);break;case 5:if(kn(t,e),Nn(e),o&512&&(Vt||a===null||Ma(a,a.return)),e.flags&32){s=e.stateNode;try{vo(s,"")}catch(Z){Tt(e,e.return,Z)}}o&4&&e.stateNode!=null&&(s=e.memoizedProps,mf(e,s,a!==null?a.memoizedProps:s)),o&1024&&(yf=!0);break;case 6:if(kn(t,e),Nn(e),o&4){if(e.stateNode===null)throw Error(i(162));o=e.memoizedProps,a=e.stateNode;try{a.nodeValue=o}catch(Z){Tt(e,e.return,Z)}}break;case 3:if(Gs=null,s=Sa,Sa=Vs(t.containerInfo),kn(t,e),Sa=s,Nn(e),o&4&&a!==null&&a.memoizedState.isDehydrated)try{di(t.containerInfo)}catch(Z){Tt(e,e.return,Z)}yf&&(yf=!1,eg(e));break;case 4:o=Sa,Sa=Vs(e.stateNode.containerInfo),kn(t,e),Nn(e),Sa=o;break;case 12:kn(t,e),Nn(e);break;case 13:kn(t,e),Nn(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Ef=at()),o&4&&(o=e.updateQueue,o!==null&&(e.updateQueue=null,vf(e,o)));break;case 22:s=e.memoizedState!==null;var k=a!==null&&a.memoizedState!==null,F=Ja,ie=Vt;if(Ja=F||s,Vt=ie||k,kn(t,e),Vt=ie,Ja=F,Nn(e),o&8192)e:for(t=e.stateNode,t._visibility=s?t._visibility&-2:t._visibility|1,s&&(a===null||k||Ja||Vt||to(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){k=a=t;try{if(f=k.stateNode,s)g=f.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{C=k.stateNode;var ce=k.memoizedProps.style,W=ce!=null&&ce.hasOwnProperty("display")?ce.display:null;C.style.display=W==null||typeof W=="boolean"?"":(""+W).trim()}}catch(Z){Tt(k,k.return,Z)}}}else if(t.tag===6){if(a===null){k=t;try{k.stateNode.nodeValue=s?"":k.memoizedProps}catch(Z){Tt(k,k.return,Z)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}o&4&&(o=e.updateQueue,o!==null&&(a=o.retryQueue,a!==null&&(o.retryQueue=null,vf(e,a))));break;case 19:kn(t,e),Nn(e),o&4&&(o=e.updateQueue,o!==null&&(e.updateQueue=null,vf(e,o)));break;case 30:break;case 21:break;default:kn(t,e),Nn(e)}}function Nn(e){var t=e.flags;if(t&2){try{for(var a,o=e.return;o!==null;){if(Gh(o)){a=o;break}o=o.return}if(a==null)throw Error(i(160));switch(a.tag){case 27:var s=a.stateNode,f=hf(e);Ds(e,f,s);break;case 5:var g=a.stateNode;a.flags&32&&(vo(g,""),a.flags&=-33);var C=hf(e);Ds(e,C,g);break;case 3:case 4:var k=a.stateNode.containerInfo,F=hf(e);gf(e,F,k);break;default:throw Error(i(161))}}catch(ie){Tt(e,e.return,ie)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function eg(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;eg(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Sr(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Qh(e,t.alternate,t),t=t.sibling}function to(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:br(4,t,t.return),to(t);break;case 1:Ma(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Vh(t,t.return,a),to(t);break;case 27:ri(t.stateNode);case 26:case 5:Ma(t,t.return),to(t);break;case 22:t.memoizedState===null&&to(t);break;case 30:to(t);break;default:to(t)}e=e.sibling}}function xr(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var o=t.alternate,s=e,f=t,g=f.flags;switch(f.tag){case 0:case 11:case 15:xr(s,f,a),Kl(4,f);break;case 1:if(xr(s,f,a),o=f,s=o.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(F){Tt(o,o.return,F)}if(o=f,s=o.updateQueue,s!==null){var C=o.stateNode;try{var k=s.shared.hiddenCallbacks;if(k!==null)for(s.shared.hiddenCallbacks=null,s=0;s<k.length;s++)Am(k[s],C)}catch(F){Tt(o,o.return,F)}}a&&g&64&&Ih(f),Ql(f,f.return);break;case 27:Xh(f);case 26:case 5:xr(s,f,a),a&&o===null&&g&4&&Yh(f),Ql(f,f.return);break;case 12:xr(s,f,a);break;case 13:xr(s,f,a),a&&g&4&&Zh(s,f);break;case 22:f.memoizedState===null&&xr(s,f,a),Ql(f,f.return);break;case 30:break;default:xr(s,f,a)}t=t.sibling}}function bf(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&kl(a))}function Sf(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&kl(e))}function wa(e,t,a,o){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)tg(e,t,a,o),t=t.sibling}function tg(e,t,a,o){var s=t.flags;switch(t.tag){case 0:case 11:case 15:wa(e,t,a,o),s&2048&&Kl(9,t);break;case 1:wa(e,t,a,o);break;case 3:wa(e,t,a,o),s&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&kl(e)));break;case 12:if(s&2048){wa(e,t,a,o),e=t.stateNode;try{var f=t.memoizedProps,g=f.id,C=f.onPostCommit;typeof C=="function"&&C(g,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(k){Tt(t,t.return,k)}}else wa(e,t,a,o);break;case 13:wa(e,t,a,o);break;case 23:break;case 22:f=t.stateNode,g=t.alternate,t.memoizedState!==null?f._visibility&2?wa(e,t,a,o):Fl(e,t):f._visibility&2?wa(e,t,a,o):(f._visibility|=2,_o(e,t,a,o,(t.subtreeFlags&10256)!==0)),s&2048&&bf(g,t);break;case 24:wa(e,t,a,o),s&2048&&Sf(t.alternate,t);break;default:wa(e,t,a,o)}}function _o(e,t,a,o,s){for(s=s&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var f=e,g=t,C=a,k=o,F=g.flags;switch(g.tag){case 0:case 11:case 15:_o(f,g,C,k,s),Kl(8,g);break;case 23:break;case 22:var ie=g.stateNode;g.memoizedState!==null?ie._visibility&2?_o(f,g,C,k,s):Fl(f,g):(ie._visibility|=2,_o(f,g,C,k,s)),s&&F&2048&&bf(g.alternate,g);break;case 24:_o(f,g,C,k,s),s&&F&2048&&Sf(g.alternate,g);break;default:_o(f,g,C,k,s)}t=t.sibling}}function Fl(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,o=t,s=o.flags;switch(o.tag){case 22:Fl(a,o),s&2048&&bf(o.alternate,o);break;case 24:Fl(a,o),s&2048&&Sf(o.alternate,o);break;default:Fl(a,o)}t=t.sibling}}var Wl=8192;function Lo(e){if(e.subtreeFlags&Wl)for(e=e.child;e!==null;)ng(e),e=e.sibling}function ng(e){switch(e.tag){case 26:Lo(e),e.flags&Wl&&e.memoizedState!==null&&mS(Sa,e.memoizedState,e.memoizedProps);break;case 5:Lo(e);break;case 3:case 4:var t=Sa;Sa=Vs(e.stateNode.containerInfo),Lo(e),Sa=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Wl,Wl=16777216,Lo(e),Wl=t):Lo(e));break;default:Lo(e)}}function ag(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Zl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var o=t[a];an=o,og(o,e)}ag(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)rg(e),e=e.sibling}function rg(e){switch(e.tag){case 0:case 11:case 15:Zl(e),e.flags&2048&&br(9,e,e.return);break;case 3:Zl(e);break;case 12:Zl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,$s(e)):Zl(e);break;default:Zl(e)}}function $s(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var o=t[a];an=o,og(o,e)}ag(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:br(8,t,t.return),$s(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,$s(t));break;default:$s(t)}e=e.sibling}}function og(e,t){for(;an!==null;){var a=an;switch(a.tag){case 0:case 11:case 15:br(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var o=a.memoizedState.cachePool.pool;o!=null&&o.refCount++}break;case 24:kl(a.memoizedState.cache)}if(o=a.child,o!==null)o.return=a,an=o;else e:for(a=e;an!==null;){o=an;var s=o.sibling,f=o.return;if(Fh(o),o===a){an=null;break e}if(s!==null){s.return=f,an=s;break e}an=f}}}var z1={getCacheForType:function(e){var t=dn(Jt),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},D1=typeof WeakMap=="function"?WeakMap:Map,ht=0,Ot=null,Fe=null,it=0,gt=0,Bn=null,Cr=!1,Ho=!1,xf=!1,tr=0,Lt=0,Tr=0,no=0,Cf=0,oa=0,Po=0,Jl=null,On=null,Tf=!1,Ef=0,js=1/0,ks=null,Er=null,ln=0,Rr=null,Uo=null,qo=0,Rf=0,Of=null,lg=null,ei=0,Mf=null;function _n(){if((ht&2)!==0&&it!==0)return it&-it;if(T.T!==null){var e=Ao;return e!==0?e:kf()}return Ea()}function ig(){oa===0&&(oa=(it&536870912)===0||ft?ha():536870912);var e=ra.current;return e!==null&&(e.flags|=32),oa}function Ln(e,t,a){(e===Ot&&(gt===2||gt===9)||e.cancelPendingCommit!==null)&&(Io(e,0),Or(e,it,oa,!1)),zn(e,a),((ht&2)===0||e!==Ot)&&(e===Ot&&((ht&2)===0&&(no|=a),Lt===4&&Or(e,it,oa,!1)),Aa(e))}function sg(e,t,a){if((ht&6)!==0)throw Error(i(327));var o=!a&&(t&124)===0&&(t&e.expiredLanes)===0||ot(e,t),s=o?k1(e,t):zf(e,t,!0),f=o;do{if(s===0){Ho&&!o&&Or(e,t,0,!1);break}else{if(a=e.current.alternate,f&&!$1(a)){s=zf(e,t,!1),f=!1;continue}if(s===2){if(f=t,e.errorRecoveryDisabledLanes&f)var g=0;else g=e.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){t=g;e:{var C=e;s=Jl;var k=C.current.memoizedState.isDehydrated;if(k&&(Io(C,g).flags|=256),g=zf(C,g,!1),g!==2){if(xf&&!k){C.errorRecoveryDisabledLanes|=f,no|=f,s=4;break e}f=On,On=s,f!==null&&(On===null?On=f:On.push.apply(On,f))}s=g}if(f=!1,s!==2)continue}}if(s===1){Io(e,0),Or(e,t,0,!0);break}e:{switch(o=e,f=s,f){case 0:case 1:throw Error(i(345));case 4:if((t&4194048)!==t)break;case 6:Or(o,t,oa,!Cr);break e;case 2:On=null;break;case 3:case 5:break;default:throw Error(i(329))}if((t&62914560)===t&&(s=Ef+300-at(),10<s)){if(Or(o,t,oa,!Cr),Ue(o,0,!0)!==0)break e;o.timeoutHandle=_g(ug.bind(null,o,a,On,ks,Tf,t,oa,no,Po,Cr,f,2,-0,0),s);break e}ug(o,a,On,ks,Tf,t,oa,no,Po,Cr,f,0,-0,0)}}break}while(!0);Aa(e)}function ug(e,t,a,o,s,f,g,C,k,F,ie,ce,W,Z){if(e.timeoutHandle=-1,ce=t.subtreeFlags,(ce&8192||(ce&16785408)===16785408)&&(ii={stylesheets:null,count:0,unsuspend:pS},ng(t),ce=hS(),ce!==null)){e.cancelPendingCommit=ce(gg.bind(null,e,t,f,a,o,s,g,C,k,ie,1,W,Z)),Or(e,f,g,!F);return}gg(e,t,f,a,o,s,g,C,k)}function $1(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var o=0;o<a.length;o++){var s=a[o],f=s.getSnapshot;s=s.value;try{if(!$n(f(),s))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Or(e,t,a,o){t&=~Cf,t&=~no,e.suspendedLanes|=t,e.pingedLanes&=~t,o&&(e.warmLanes|=t),o=e.expirationTimes;for(var s=t;0<s;){var f=31-et(s),g=1<<f;o[f]=-1,s&=~g}a!==0&&Ua(e,a,t)}function Ns(){return(ht&6)===0?(ti(0),!1):!0}function wf(){if(Fe!==null){if(gt===0)var e=Fe.return;else e=Fe,Xa=Fr=null,Vc(e),No=null,Yl=0,e=Fe;for(;e!==null;)qh(e.alternate,e),e=e.return;Fe=null}}function Io(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,F1(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),wf(),Ot=e,Fe=a=Va(e.current,null),it=t,gt=0,Bn=null,Cr=!1,Ho=ot(e,t),xf=!1,Po=oa=Cf=no=Tr=Lt=0,On=Jl=null,Tf=!1,(t&8)!==0&&(t|=t&32);var o=e.entangledLanes;if(o!==0)for(e=e.entanglements,o&=t;0<o;){var s=31-et(o),f=1<<s;t|=e[s],o&=~f}return tr=t,as(),a}function cg(e,t){Ie=null,T.H=Cs,t===Bl||t===ds?(t=Mm(),gt=3):t===Em?(t=Mm(),gt=4):gt=t===wh?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Bn=t,Fe===null&&(Lt=1,Ms(e,ea(t,e.current)))}function fg(){var e=T.H;return T.H=Cs,e===null?Cs:e}function dg(){var e=T.A;return T.A=z1,e}function Af(){Lt=4,Cr||(it&4194048)!==it&&ra.current!==null||(Ho=!0),(Tr&134217727)===0&&(no&134217727)===0||Ot===null||Or(Ot,it,oa,!1)}function zf(e,t,a){var o=ht;ht|=2;var s=fg(),f=dg();(Ot!==e||it!==t)&&(ks=null,Io(e,t)),t=!1;var g=Lt;e:do try{if(gt!==0&&Fe!==null){var C=Fe,k=Bn;switch(gt){case 8:wf(),g=6;break e;case 3:case 2:case 9:case 6:ra.current===null&&(t=!0);var F=gt;if(gt=0,Bn=null,Vo(e,C,k,F),a&&Ho){g=0;break e}break;default:F=gt,gt=0,Bn=null,Vo(e,C,k,F)}}j1(),g=Lt;break}catch(ie){cg(e,ie)}while(!0);return t&&e.shellSuspendCounter++,Xa=Fr=null,ht=o,T.H=s,T.A=f,Fe===null&&(Ot=null,it=0,as()),g}function j1(){for(;Fe!==null;)pg(Fe)}function k1(e,t){var a=ht;ht|=2;var o=fg(),s=dg();Ot!==e||it!==t?(ks=null,js=at()+500,Io(e,t)):Ho=ot(e,t);e:do try{if(gt!==0&&Fe!==null){t=Fe;var f=Bn;t:switch(gt){case 1:gt=0,Bn=null,Vo(e,t,f,1);break;case 2:case 9:if(Rm(f)){gt=0,Bn=null,mg(t);break}t=function(){gt!==2&&gt!==9||Ot!==e||(gt=7),Aa(e)},f.then(t,t);break e;case 3:gt=7;break e;case 4:gt=5;break e;case 7:Rm(f)?(gt=0,Bn=null,mg(t)):(gt=0,Bn=null,Vo(e,t,f,7));break;case 5:var g=null;switch(Fe.tag){case 26:g=Fe.memoizedState;case 5:case 27:var C=Fe;if(!g||Qg(g)){gt=0,Bn=null;var k=C.sibling;if(k!==null)Fe=k;else{var F=C.return;F!==null?(Fe=F,Bs(F)):Fe=null}break t}}gt=0,Bn=null,Vo(e,t,f,5);break;case 6:gt=0,Bn=null,Vo(e,t,f,6);break;case 8:wf(),Lt=6;break e;default:throw Error(i(462))}}N1();break}catch(ie){cg(e,ie)}while(!0);return Xa=Fr=null,T.H=o,T.A=s,ht=a,Fe!==null?0:(Ot=null,it=0,as(),Lt)}function N1(){for(;Fe!==null&&!nt();)pg(Fe)}function pg(e){var t=Ph(e.alternate,e,tr);e.memoizedProps=e.pendingProps,t===null?Bs(e):Fe=t}function mg(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=kh(a,t,t.pendingProps,t.type,void 0,it);break;case 11:t=kh(a,t,t.pendingProps,t.type.render,t.ref,it);break;case 5:Vc(t);default:qh(a,t),t=Fe=hm(t,tr),t=Ph(a,t,tr)}e.memoizedProps=e.pendingProps,t===null?Bs(e):Fe=t}function Vo(e,t,a,o){Xa=Fr=null,Vc(t),No=null,Yl=0;var s=t.return;try{if(E1(e,s,t,a,it)){Lt=1,Ms(e,ea(a,e.current)),Fe=null;return}}catch(f){if(s!==null)throw Fe=s,f;Lt=1,Ms(e,ea(a,e.current)),Fe=null;return}t.flags&32768?(ft||o===1?e=!0:Ho||(it&536870912)!==0?e=!1:(Cr=e=!0,(o===2||o===9||o===3||o===6)&&(o=ra.current,o!==null&&o.tag===13&&(o.flags|=16384))),hg(t,e)):Bs(t)}function Bs(e){var t=e;do{if((t.flags&32768)!==0){hg(t,Cr);return}e=t.return;var a=O1(t.alternate,t,tr);if(a!==null){Fe=a;return}if(t=t.sibling,t!==null){Fe=t;return}Fe=t=e}while(t!==null);Lt===0&&(Lt=5)}function hg(e,t){do{var a=M1(e.alternate,e);if(a!==null){a.flags&=32767,Fe=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){Fe=e;return}Fe=e=a}while(e!==null);Lt=6,Fe=null}function gg(e,t,a,o,s,f,g,C,k){e.cancelPendingCommit=null;do _s();while(ln!==0);if((ht&6)!==0)throw Error(i(327));if(t!==null){if(t===e.current)throw Error(i(177));if(f=t.lanes|t.childLanes,f|=bc,ga(e,a,f,g,C,k),e===Ot&&(Fe=Ot=null,it=0),Uo=t,Rr=e,qo=a,Rf=f,Of=s,lg=o,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,H1(He,function(){return xg(),null})):(e.callbackNode=null,e.callbackPriority=0),o=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||o){o=T.T,T.T=null,s=L.p,L.p=2,g=ht,ht|=4;try{w1(e,t,a)}finally{ht=g,L.p=s,T.T=o}}ln=1,yg(),vg(),bg()}}function yg(){if(ln===1){ln=0;var e=Rr,t=Uo,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=T.T,T.T=null;var o=L.p;L.p=2;var s=ht;ht|=4;try{Jh(t,e);var f=qf,g=om(e.containerInfo),C=f.focusedElem,k=f.selectionRange;if(g!==C&&C&&C.ownerDocument&&rm(C.ownerDocument.documentElement,C)){if(k!==null&&mc(C)){var F=k.start,ie=k.end;if(ie===void 0&&(ie=F),"selectionStart"in C)C.selectionStart=F,C.selectionEnd=Math.min(ie,C.value.length);else{var ce=C.ownerDocument||document,W=ce&&ce.defaultView||window;if(W.getSelection){var Z=W.getSelection(),je=C.textContent.length,ze=Math.min(k.start,je),xt=k.end===void 0?ze:Math.min(k.end,je);!Z.extend&&ze>xt&&(g=xt,xt=ze,ze=g);var q=am(C,ze),H=am(C,xt);if(q&&H&&(Z.rangeCount!==1||Z.anchorNode!==q.node||Z.anchorOffset!==q.offset||Z.focusNode!==H.node||Z.focusOffset!==H.offset)){var Q=ce.createRange();Q.setStart(q.node,q.offset),Z.removeAllRanges(),ze>xt?(Z.addRange(Q),Z.extend(H.node,H.offset)):(Q.setEnd(H.node,H.offset),Z.addRange(Q))}}}}for(ce=[],Z=C;Z=Z.parentNode;)Z.nodeType===1&&ce.push({element:Z,left:Z.scrollLeft,top:Z.scrollTop});for(typeof C.focus=="function"&&C.focus(),C=0;C<ce.length;C++){var se=ce[C];se.element.scrollLeft=se.left,se.element.scrollTop=se.top}}Qs=!!Uf,qf=Uf=null}finally{ht=s,L.p=o,T.T=a}}e.current=t,ln=2}}function vg(){if(ln===2){ln=0;var e=Rr,t=Uo,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=T.T,T.T=null;var o=L.p;L.p=2;var s=ht;ht|=4;try{Qh(e,t.alternate,t)}finally{ht=s,L.p=o,T.T=a}}ln=3}}function bg(){if(ln===4||ln===3){ln=0,Ve();var e=Rr,t=Uo,a=qo,o=lg;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ln=5:(ln=0,Uo=Rr=null,Sg(e,e.pendingLanes));var s=e.pendingLanes;if(s===0&&(Er=null),cr(a),t=t.stateNode,ke&&typeof ke.onCommitFiberRoot=="function")try{ke.onCommitFiberRoot(rt,t,void 0,(t.current.flags&128)===128)}catch{}if(o!==null){t=T.T,s=L.p,L.p=2,T.T=null;try{for(var f=e.onRecoverableError,g=0;g<o.length;g++){var C=o[g];f(C.value,{componentStack:C.stack})}}finally{T.T=t,L.p=s}}(qo&3)!==0&&_s(),Aa(e),s=e.pendingLanes,(a&4194090)!==0&&(s&42)!==0?e===Mf?ei++:(ei=0,Mf=e):ei=0,ti(0)}}function Sg(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,kl(t)))}function _s(e){return yg(),vg(),bg(),xg()}function xg(){if(ln!==5)return!1;var e=Rr,t=Rf;Rf=0;var a=cr(qo),o=T.T,s=L.p;try{L.p=32>a?32:a,T.T=null,a=Of,Of=null;var f=Rr,g=qo;if(ln=0,Uo=Rr=null,qo=0,(ht&6)!==0)throw Error(i(331));var C=ht;if(ht|=4,rg(f.current),tg(f,f.current,g,a),ht=C,ti(0,!1),ke&&typeof ke.onPostCommitFiberRoot=="function")try{ke.onPostCommitFiberRoot(rt,f)}catch{}return!0}finally{L.p=s,T.T=o,Sg(e,t)}}function Cg(e,t,a){t=ea(a,t),t=rf(e.stateNode,t,2),e=hr(e,t,2),e!==null&&(zn(e,2),Aa(e))}function Tt(e,t,a){if(e.tag===3)Cg(e,e,a);else for(;t!==null;){if(t.tag===3){Cg(t,e,a);break}else if(t.tag===1){var o=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(Er===null||!Er.has(o))){e=ea(a,e),a=Oh(2),o=hr(t,a,2),o!==null&&(Mh(a,o,t,e),zn(o,2),Aa(o));break}}t=t.return}}function Df(e,t,a){var o=e.pingCache;if(o===null){o=e.pingCache=new D1;var s=new Set;o.set(t,s)}else s=o.get(t),s===void 0&&(s=new Set,o.set(t,s));s.has(a)||(xf=!0,s.add(a),e=B1.bind(null,e,t,a),t.then(e,e))}function B1(e,t,a){var o=e.pingCache;o!==null&&o.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Ot===e&&(it&a)===a&&(Lt===4||Lt===3&&(it&62914560)===it&&300>at()-Ef?(ht&2)===0&&Io(e,0):Cf|=a,Po===it&&(Po=0)),Aa(e)}function Tg(e,t){t===0&&(t=po()),e=Ro(e,t),e!==null&&(zn(e,t),Aa(e))}function _1(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Tg(e,a)}function L1(e,t){var a=0;switch(e.tag){case 13:var o=e.stateNode,s=e.memoizedState;s!==null&&(a=s.retryLane);break;case 19:o=e.stateNode;break;case 22:o=e.stateNode._retryCache;break;default:throw Error(i(314))}o!==null&&o.delete(t),Tg(e,a)}function H1(e,t){return Le(e,t)}var Ls=null,Yo=null,$f=!1,Hs=!1,jf=!1,ao=0;function Aa(e){e!==Yo&&e.next===null&&(Yo===null?Ls=Yo=e:Yo=Yo.next=e),Hs=!0,$f||($f=!0,U1())}function ti(e,t){if(!jf&&Hs){jf=!0;do for(var a=!1,o=Ls;o!==null;){if(e!==0){var s=o.pendingLanes;if(s===0)var f=0;else{var g=o.suspendedLanes,C=o.pingedLanes;f=(1<<31-et(42|e)+1)-1,f&=s&~(g&~C),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(a=!0,Mg(o,f))}else f=it,f=Ue(o,o===Ot?f:0,o.cancelPendingCommit!==null||o.timeoutHandle!==-1),(f&3)===0||ot(o,f)||(a=!0,Mg(o,f));o=o.next}while(a);jf=!1}}function P1(){Eg()}function Eg(){Hs=$f=!1;var e=0;ao!==0&&(Q1()&&(e=ao),ao=0);for(var t=at(),a=null,o=Ls;o!==null;){var s=o.next,f=Rg(o,t);f===0?(o.next=null,a===null?Ls=s:a.next=s,s===null&&(Yo=a)):(a=o,(e!==0||(f&3)!==0)&&(Hs=!0)),o=s}ti(e)}function Rg(e,t){for(var a=e.suspendedLanes,o=e.pingedLanes,s=e.expirationTimes,f=e.pendingLanes&-62914561;0<f;){var g=31-et(f),C=1<<g,k=s[g];k===-1?((C&a)===0||(C&o)!==0)&&(s[g]=Gn(C,t)):k<=t&&(e.expiredLanes|=C),f&=~C}if(t=Ot,a=it,a=Ue(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),o=e.callbackNode,a===0||e===t&&(gt===2||gt===9)||e.cancelPendingCommit!==null)return o!==null&&o!==null&&$e(o),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||ot(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(o!==null&&$e(o),cr(a)){case 2:case 8:a=Te;break;case 32:a=He;break;case 268435456:a=qt;break;default:a=He}return o=Og.bind(null,e),a=Le(a,o),e.callbackPriority=t,e.callbackNode=a,t}return o!==null&&o!==null&&$e(o),e.callbackPriority=2,e.callbackNode=null,2}function Og(e,t){if(ln!==0&&ln!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(_s()&&e.callbackNode!==a)return null;var o=it;return o=Ue(e,e===Ot?o:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),o===0?null:(sg(e,o,t),Rg(e,at()),e.callbackNode!=null&&e.callbackNode===a?Og.bind(null,e):null)}function Mg(e,t){if(_s())return null;sg(e,t,!0)}function U1(){W1(function(){(ht&6)!==0?Le(st,P1):Eg()})}function kf(){return ao===0&&(ao=ha()),ao}function wg(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Fi(""+e)}function Ag(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function q1(e,t,a,o,s){if(t==="submit"&&a&&a.stateNode===s){var f=wg((s[Xt]||null).action),g=o.submitter;g&&(t=(t=g[Xt]||null)?wg(t.formAction):g.getAttribute("formAction"),t!==null&&(f=t,g=null));var C=new es("action","action",null,o,s);e.push({event:C,listeners:[{instance:null,listener:function(){if(o.defaultPrevented){if(ao!==0){var k=g?Ag(s,g):new FormData(s);Jc(a,{pending:!0,data:k,method:s.method,action:f},null,k)}}else typeof f=="function"&&(C.preventDefault(),k=g?Ag(s,g):new FormData(s),Jc(a,{pending:!0,data:k,method:s.method,action:f},f,k))},currentTarget:s}]})}}for(var Nf=0;Nf<vc.length;Nf++){var Bf=vc[Nf],I1=Bf.toLowerCase(),V1=Bf[0].toUpperCase()+Bf.slice(1);ba(I1,"on"+V1)}ba(sm,"onAnimationEnd"),ba(um,"onAnimationIteration"),ba(cm,"onAnimationStart"),ba("dblclick","onDoubleClick"),ba("focusin","onFocus"),ba("focusout","onBlur"),ba(i1,"onTransitionRun"),ba(s1,"onTransitionStart"),ba(u1,"onTransitionCancel"),ba(fm,"onTransitionEnd"),Ee("onMouseEnter",["mouseout","mouseover"]),Ee("onMouseLeave",["mouseout","mouseover"]),Ee("onPointerEnter",["pointerout","pointerover"]),Ee("onPointerLeave",["pointerout","pointerover"]),Be("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Be("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Be("onBeforeInput",["compositionend","keypress","textInput","paste"]),Be("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Be("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Be("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ni="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Y1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ni));function zg(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var o=e[a],s=o.event;o=o.listeners;e:{var f=void 0;if(t)for(var g=o.length-1;0<=g;g--){var C=o[g],k=C.instance,F=C.currentTarget;if(C=C.listener,k!==f&&s.isPropagationStopped())break e;f=C,s.currentTarget=F;try{f(s)}catch(ie){Os(ie)}s.currentTarget=null,f=k}else for(g=0;g<o.length;g++){if(C=o[g],k=C.instance,F=C.currentTarget,C=C.listener,k!==f&&s.isPropagationStopped())break e;f=C,s.currentTarget=F;try{f(s)}catch(ie){Os(ie)}s.currentTarget=null,f=k}}}}function We(e,t){var a=t[Pr];a===void 0&&(a=t[Pr]=new Set);var o=e+"__bubble";a.has(o)||(Dg(t,e,2,!1),a.add(o))}function _f(e,t,a){var o=0;t&&(o|=4),Dg(a,e,o,t)}var Ps="_reactListening"+Math.random().toString(36).slice(2);function Lf(e){if(!e[Ps]){e[Ps]=!0,re.forEach(function(a){a!=="selectionchange"&&(Y1.has(a)||_f(a,!1,e),_f(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ps]||(t[Ps]=!0,_f("selectionchange",!1,t))}}function Dg(e,t,a,o){switch(ty(t)){case 2:var s=vS;break;case 8:s=bS;break;default:s=Zf}a=s.bind(null,t,a,e),s=void 0,!oc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),o?s!==void 0?e.addEventListener(t,a,{capture:!0,passive:s}):e.addEventListener(t,a,!0):s!==void 0?e.addEventListener(t,a,{passive:s}):e.addEventListener(t,a,!1)}function Hf(e,t,a,o,s){var f=o;if((t&1)===0&&(t&2)===0&&o!==null)e:for(;;){if(o===null)return;var g=o.tag;if(g===3||g===4){var C=o.stateNode.containerInfo;if(C===s)break;if(g===4)for(g=o.return;g!==null;){var k=g.tag;if((k===3||k===4)&&g.stateNode.containerInfo===s)return;g=g.return}for(;C!==null;){if(g=Mt(C),g===null)return;if(k=g.tag,k===5||k===6||k===26||k===27){o=f=g;continue e}C=C.parentNode}}o=o.return}Lp(function(){var F=f,ie=ac(a),ce=[];e:{var W=dm.get(e);if(W!==void 0){var Z=es,je=e;switch(e){case"keypress":if(Zi(a)===0)break e;case"keydown":case"keyup":Z=Hb;break;case"focusin":je="focus",Z=uc;break;case"focusout":je="blur",Z=uc;break;case"beforeblur":case"afterblur":Z=uc;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":Z=Up;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":Z=Mb;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":Z=qb;break;case sm:case um:case cm:Z=zb;break;case fm:Z=Vb;break;case"scroll":case"scrollend":Z=Rb;break;case"wheel":Z=Gb;break;case"copy":case"cut":case"paste":Z=$b;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":Z=Ip;break;case"toggle":case"beforetoggle":Z=Kb}var ze=(t&4)!==0,xt=!ze&&(e==="scroll"||e==="scrollend"),q=ze?W!==null?W+"Capture":null:W;ze=[];for(var H=F,Q;H!==null;){var se=H;if(Q=se.stateNode,se=se.tag,se!==5&&se!==26&&se!==27||Q===null||q===null||(se=xl(H,q),se!=null&&ze.push(ai(H,se,Q))),xt)break;H=H.return}0<ze.length&&(W=new Z(W,je,null,a,ie),ce.push({event:W,listeners:ze}))}}if((t&7)===0){e:{if(W=e==="mouseover"||e==="pointerover",Z=e==="mouseout"||e==="pointerout",W&&a!==nc&&(je=a.relatedTarget||a.fromElement)&&(Mt(je)||je[Kn]))break e;if((Z||W)&&(W=ie.window===ie?ie:(W=ie.ownerDocument)?W.defaultView||W.parentWindow:window,Z?(je=a.relatedTarget||a.toElement,Z=F,je=je?Mt(je):null,je!==null&&(xt=c(je),ze=je.tag,je!==xt||ze!==5&&ze!==27&&ze!==6)&&(je=null)):(Z=null,je=F),Z!==je)){if(ze=Up,se="onMouseLeave",q="onMouseEnter",H="mouse",(e==="pointerout"||e==="pointerover")&&(ze=Ip,se="onPointerLeave",q="onPointerEnter",H="pointer"),xt=Z==null?W:ya(Z),Q=je==null?W:ya(je),W=new ze(se,H+"leave",Z,a,ie),W.target=xt,W.relatedTarget=Q,se=null,Mt(ie)===F&&(ze=new ze(q,H+"enter",je,a,ie),ze.target=Q,ze.relatedTarget=xt,se=ze),xt=se,Z&&je)t:{for(ze=Z,q=je,H=0,Q=ze;Q;Q=Go(Q))H++;for(Q=0,se=q;se;se=Go(se))Q++;for(;0<H-Q;)ze=Go(ze),H--;for(;0<Q-H;)q=Go(q),Q--;for(;H--;){if(ze===q||q!==null&&ze===q.alternate)break t;ze=Go(ze),q=Go(q)}ze=null}else ze=null;Z!==null&&$g(ce,W,Z,ze,!1),je!==null&&xt!==null&&$g(ce,xt,je,ze,!0)}}e:{if(W=F?ya(F):window,Z=W.nodeName&&W.nodeName.toLowerCase(),Z==="select"||Z==="input"&&W.type==="file")var Se=Wp;else if(Qp(W))if(Zp)Se=r1;else{Se=n1;var Ge=t1}else Z=W.nodeName,!Z||Z.toLowerCase()!=="input"||W.type!=="checkbox"&&W.type!=="radio"?F&&tc(F.elementType)&&(Se=Wp):Se=a1;if(Se&&(Se=Se(e,F))){Fp(ce,Se,a,ie);break e}Ge&&Ge(e,W,F),e==="focusout"&&F&&W.type==="number"&&F.memoizedProps.value!=null&&ec(W,"number",W.value)}switch(Ge=F?ya(F):window,e){case"focusin":(Qp(Ge)||Ge.contentEditable==="true")&&(Co=Ge,hc=F,Al=null);break;case"focusout":Al=hc=Co=null;break;case"mousedown":gc=!0;break;case"contextmenu":case"mouseup":case"dragend":gc=!1,lm(ce,a,ie);break;case"selectionchange":if(l1)break;case"keydown":case"keyup":lm(ce,a,ie)}var Oe;if(fc)e:{switch(e){case"compositionstart":var De="onCompositionStart";break e;case"compositionend":De="onCompositionEnd";break e;case"compositionupdate":De="onCompositionUpdate";break e}De=void 0}else xo?Xp(e,a)&&(De="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(De="onCompositionStart");De&&(Vp&&a.locale!=="ko"&&(xo||De!=="onCompositionStart"?De==="onCompositionEnd"&&xo&&(Oe=Hp()):(fr=ie,lc="value"in fr?fr.value:fr.textContent,xo=!0)),Ge=Us(F,De),0<Ge.length&&(De=new qp(De,e,null,a,ie),ce.push({event:De,listeners:Ge}),Oe?De.data=Oe:(Oe=Kp(a),Oe!==null&&(De.data=Oe)))),(Oe=Fb?Wb(e,a):Zb(e,a))&&(De=Us(F,"onBeforeInput"),0<De.length&&(Ge=new qp("onBeforeInput","beforeinput",null,a,ie),ce.push({event:Ge,listeners:De}),Ge.data=Oe)),q1(ce,e,F,a,ie)}zg(ce,t)})}function ai(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Us(e,t){for(var a=t+"Capture",o=[];e!==null;){var s=e,f=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||f===null||(s=xl(e,a),s!=null&&o.unshift(ai(e,s,f)),s=xl(e,t),s!=null&&o.push(ai(e,s,f))),e.tag===3)return o;e=e.return}return[]}function Go(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function $g(e,t,a,o,s){for(var f=t._reactName,g=[];a!==null&&a!==o;){var C=a,k=C.alternate,F=C.stateNode;if(C=C.tag,k!==null&&k===o)break;C!==5&&C!==26&&C!==27||F===null||(k=F,s?(F=xl(a,f),F!=null&&g.unshift(ai(a,F,k))):s||(F=xl(a,f),F!=null&&g.push(ai(a,F,k)))),a=a.return}g.length!==0&&e.push({event:t,listeners:g})}var G1=/\r\n?/g,X1=/\u0000|\uFFFD/g;function jg(e){return(typeof e=="string"?e:""+e).replace(G1,`
`).replace(X1,"")}function kg(e,t){return t=jg(t),jg(e)===t}function qs(){}function St(e,t,a,o,s,f){switch(a){case"children":typeof o=="string"?t==="body"||t==="textarea"&&o===""||vo(e,o):(typeof o=="number"||typeof o=="bigint")&&t!=="body"&&vo(e,""+o);break;case"className":Ur(e,"class",o);break;case"tabIndex":Ur(e,"tabindex",o);break;case"dir":case"role":case"viewBox":case"width":case"height":Ur(e,a,o);break;case"style":Bp(e,o,f);break;case"data":if(t!=="object"){Ur(e,"data",o);break}case"src":case"href":if(o===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(o==null||typeof o=="function"||typeof o=="symbol"||typeof o=="boolean"){e.removeAttribute(a);break}o=Fi(""+o),e.setAttribute(a,o);break;case"action":case"formAction":if(typeof o=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(a==="formAction"?(t!=="input"&&St(e,t,"name",s.name,s,null),St(e,t,"formEncType",s.formEncType,s,null),St(e,t,"formMethod",s.formMethod,s,null),St(e,t,"formTarget",s.formTarget,s,null)):(St(e,t,"encType",s.encType,s,null),St(e,t,"method",s.method,s,null),St(e,t,"target",s.target,s,null)));if(o==null||typeof o=="symbol"||typeof o=="boolean"){e.removeAttribute(a);break}o=Fi(""+o),e.setAttribute(a,o);break;case"onClick":o!=null&&(e.onclick=qs);break;case"onScroll":o!=null&&We("scroll",e);break;case"onScrollEnd":o!=null&&We("scrollend",e);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(i(61));if(a=o.__html,a!=null){if(s.children!=null)throw Error(i(60));e.innerHTML=a}}break;case"multiple":e.multiple=o&&typeof o!="function"&&typeof o!="symbol";break;case"muted":e.muted=o&&typeof o!="function"&&typeof o!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(o==null||typeof o=="function"||typeof o=="boolean"||typeof o=="symbol"){e.removeAttribute("xlink:href");break}a=Fi(""+o),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":o!=null&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(a,""+o):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":o&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":o===!0?e.setAttribute(a,""):o!==!1&&o!=null&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(a,o):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":o!=null&&typeof o!="function"&&typeof o!="symbol"&&!isNaN(o)&&1<=o?e.setAttribute(a,o):e.removeAttribute(a);break;case"rowSpan":case"start":o==null||typeof o=="function"||typeof o=="symbol"||isNaN(o)?e.removeAttribute(a):e.setAttribute(a,o);break;case"popover":We("beforetoggle",e),We("toggle",e),ho(e,"popover",o);break;case"xlinkActuate":Fn(e,"http://www.w3.org/1999/xlink","xlink:actuate",o);break;case"xlinkArcrole":Fn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",o);break;case"xlinkRole":Fn(e,"http://www.w3.org/1999/xlink","xlink:role",o);break;case"xlinkShow":Fn(e,"http://www.w3.org/1999/xlink","xlink:show",o);break;case"xlinkTitle":Fn(e,"http://www.w3.org/1999/xlink","xlink:title",o);break;case"xlinkType":Fn(e,"http://www.w3.org/1999/xlink","xlink:type",o);break;case"xmlBase":Fn(e,"http://www.w3.org/XML/1998/namespace","xml:base",o);break;case"xmlLang":Fn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",o);break;case"xmlSpace":Fn(e,"http://www.w3.org/XML/1998/namespace","xml:space",o);break;case"is":ho(e,"is",o);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Tb.get(a)||a,ho(e,a,o))}}function Pf(e,t,a,o,s,f){switch(a){case"style":Bp(e,o,f);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(i(61));if(a=o.__html,a!=null){if(s.children!=null)throw Error(i(60));e.innerHTML=a}}break;case"children":typeof o=="string"?vo(e,o):(typeof o=="number"||typeof o=="bigint")&&vo(e,""+o);break;case"onScroll":o!=null&&We("scroll",e);break;case"onScrollEnd":o!=null&&We("scrollend",e);break;case"onClick":o!=null&&(e.onclick=qs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ge.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(s=a.endsWith("Capture"),t=a.slice(2,s?a.length-7:void 0),f=e[Xt]||null,f=f!=null?f[a]:null,typeof f=="function"&&e.removeEventListener(t,f,s),typeof o=="function")){typeof f!="function"&&f!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,o,s);break e}a in e?e[a]=o:o===!0?e.setAttribute(a,""):ho(e,a,o)}}}function sn(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":We("error",e),We("load",e);var o=!1,s=!1,f;for(f in a)if(a.hasOwnProperty(f)){var g=a[f];if(g!=null)switch(f){case"src":o=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:St(e,t,f,g,a,null)}}s&&St(e,t,"srcSet",a.srcSet,a,null),o&&St(e,t,"src",a.src,a,null);return;case"input":We("invalid",e);var C=f=g=s=null,k=null,F=null;for(o in a)if(a.hasOwnProperty(o)){var ie=a[o];if(ie!=null)switch(o){case"name":s=ie;break;case"type":g=ie;break;case"checked":k=ie;break;case"defaultChecked":F=ie;break;case"value":f=ie;break;case"defaultValue":C=ie;break;case"children":case"dangerouslySetInnerHTML":if(ie!=null)throw Error(i(137,t));break;default:St(e,t,o,ie,a,null)}}$p(e,f,C,k,F,g,s,!1),Ki(e);return;case"select":We("invalid",e),o=g=f=null;for(s in a)if(a.hasOwnProperty(s)&&(C=a[s],C!=null))switch(s){case"value":f=C;break;case"defaultValue":g=C;break;case"multiple":o=C;default:St(e,t,s,C,a,null)}t=f,a=g,e.multiple=!!o,t!=null?yo(e,!!o,t,!1):a!=null&&yo(e,!!o,a,!0);return;case"textarea":We("invalid",e),f=s=o=null;for(g in a)if(a.hasOwnProperty(g)&&(C=a[g],C!=null))switch(g){case"value":o=C;break;case"defaultValue":s=C;break;case"children":f=C;break;case"dangerouslySetInnerHTML":if(C!=null)throw Error(i(91));break;default:St(e,t,g,C,a,null)}kp(e,o,s,f),Ki(e);return;case"option":for(k in a)if(a.hasOwnProperty(k)&&(o=a[k],o!=null))switch(k){case"selected":e.selected=o&&typeof o!="function"&&typeof o!="symbol";break;default:St(e,t,k,o,a,null)}return;case"dialog":We("beforetoggle",e),We("toggle",e),We("cancel",e),We("close",e);break;case"iframe":case"object":We("load",e);break;case"video":case"audio":for(o=0;o<ni.length;o++)We(ni[o],e);break;case"image":We("error",e),We("load",e);break;case"details":We("toggle",e);break;case"embed":case"source":case"link":We("error",e),We("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(F in a)if(a.hasOwnProperty(F)&&(o=a[F],o!=null))switch(F){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:St(e,t,F,o,a,null)}return;default:if(tc(t)){for(ie in a)a.hasOwnProperty(ie)&&(o=a[ie],o!==void 0&&Pf(e,t,ie,o,a,void 0));return}}for(C in a)a.hasOwnProperty(C)&&(o=a[C],o!=null&&St(e,t,C,o,a,null))}function K1(e,t,a,o){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,f=null,g=null,C=null,k=null,F=null,ie=null;for(Z in a){var ce=a[Z];if(a.hasOwnProperty(Z)&&ce!=null)switch(Z){case"checked":break;case"value":break;case"defaultValue":k=ce;default:o.hasOwnProperty(Z)||St(e,t,Z,null,o,ce)}}for(var W in o){var Z=o[W];if(ce=a[W],o.hasOwnProperty(W)&&(Z!=null||ce!=null))switch(W){case"type":f=Z;break;case"name":s=Z;break;case"checked":F=Z;break;case"defaultChecked":ie=Z;break;case"value":g=Z;break;case"defaultValue":C=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(i(137,t));break;default:Z!==ce&&St(e,t,W,Z,o,ce)}}Ju(e,g,C,k,F,ie,f,s);return;case"select":Z=g=C=W=null;for(f in a)if(k=a[f],a.hasOwnProperty(f)&&k!=null)switch(f){case"value":break;case"multiple":Z=k;default:o.hasOwnProperty(f)||St(e,t,f,null,o,k)}for(s in o)if(f=o[s],k=a[s],o.hasOwnProperty(s)&&(f!=null||k!=null))switch(s){case"value":W=f;break;case"defaultValue":C=f;break;case"multiple":g=f;default:f!==k&&St(e,t,s,f,o,k)}t=C,a=g,o=Z,W!=null?yo(e,!!a,W,!1):!!o!=!!a&&(t!=null?yo(e,!!a,t,!0):yo(e,!!a,a?[]:"",!1));return;case"textarea":Z=W=null;for(C in a)if(s=a[C],a.hasOwnProperty(C)&&s!=null&&!o.hasOwnProperty(C))switch(C){case"value":break;case"children":break;default:St(e,t,C,null,o,s)}for(g in o)if(s=o[g],f=a[g],o.hasOwnProperty(g)&&(s!=null||f!=null))switch(g){case"value":W=s;break;case"defaultValue":Z=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(i(91));break;default:s!==f&&St(e,t,g,s,o,f)}jp(e,W,Z);return;case"option":for(var je in a)if(W=a[je],a.hasOwnProperty(je)&&W!=null&&!o.hasOwnProperty(je))switch(je){case"selected":e.selected=!1;break;default:St(e,t,je,null,o,W)}for(k in o)if(W=o[k],Z=a[k],o.hasOwnProperty(k)&&W!==Z&&(W!=null||Z!=null))switch(k){case"selected":e.selected=W&&typeof W!="function"&&typeof W!="symbol";break;default:St(e,t,k,W,o,Z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ze in a)W=a[ze],a.hasOwnProperty(ze)&&W!=null&&!o.hasOwnProperty(ze)&&St(e,t,ze,null,o,W);for(F in o)if(W=o[F],Z=a[F],o.hasOwnProperty(F)&&W!==Z&&(W!=null||Z!=null))switch(F){case"children":case"dangerouslySetInnerHTML":if(W!=null)throw Error(i(137,t));break;default:St(e,t,F,W,o,Z)}return;default:if(tc(t)){for(var xt in a)W=a[xt],a.hasOwnProperty(xt)&&W!==void 0&&!o.hasOwnProperty(xt)&&Pf(e,t,xt,void 0,o,W);for(ie in o)W=o[ie],Z=a[ie],!o.hasOwnProperty(ie)||W===Z||W===void 0&&Z===void 0||Pf(e,t,ie,W,o,Z);return}}for(var q in a)W=a[q],a.hasOwnProperty(q)&&W!=null&&!o.hasOwnProperty(q)&&St(e,t,q,null,o,W);for(ce in o)W=o[ce],Z=a[ce],!o.hasOwnProperty(ce)||W===Z||W==null&&Z==null||St(e,t,ce,W,o,Z)}var Uf=null,qf=null;function Is(e){return e.nodeType===9?e:e.ownerDocument}function Ng(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Bg(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function If(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Vf=null;function Q1(){var e=window.event;return e&&e.type==="popstate"?e===Vf?!1:(Vf=e,!0):(Vf=null,!1)}var _g=typeof setTimeout=="function"?setTimeout:void 0,F1=typeof clearTimeout=="function"?clearTimeout:void 0,Lg=typeof Promise=="function"?Promise:void 0,W1=typeof queueMicrotask=="function"?queueMicrotask:typeof Lg<"u"?function(e){return Lg.resolve(null).then(e).catch(Z1)}:_g;function Z1(e){setTimeout(function(){throw e})}function Mr(e){return e==="head"}function Hg(e,t){var a=t,o=0,s=0;do{var f=a.nextSibling;if(e.removeChild(a),f&&f.nodeType===8)if(a=f.data,a==="/$"){if(0<o&&8>o){a=o;var g=e.ownerDocument;if(a&1&&ri(g.documentElement),a&2&&ri(g.body),a&4)for(a=g.head,ri(a),g=a.firstChild;g;){var C=g.nextSibling,k=g.nodeName;g[Qn]||k==="SCRIPT"||k==="STYLE"||k==="LINK"&&g.rel.toLowerCase()==="stylesheet"||a.removeChild(g),g=C}}if(s===0){e.removeChild(f),di(t);return}s--}else a==="$"||a==="$?"||a==="$!"?s++:o=a.charCodeAt(0)-48;else o=0;a=f}while(a);di(t)}function Yf(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Yf(a),Bt(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function J1(e,t,a,o){for(;e.nodeType===1;){var s=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!o&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(o){if(!e[Qn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(f=e.getAttribute("rel"),f==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(f!==s.rel||e.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||e.getAttribute("title")!==(s.title==null?null:s.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(f=e.getAttribute("src"),(f!==(s.src==null?null:s.src)||e.getAttribute("type")!==(s.type==null?null:s.type)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&f&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var f=s.name==null?null:""+s.name;if(s.type==="hidden"&&e.getAttribute("name")===f)return e}else return e;if(e=xa(e.nextSibling),e===null)break}return null}function eS(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=xa(e.nextSibling),e===null))return null;return e}function Gf(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function tS(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var o=function(){t(),a.removeEventListener("DOMContentLoaded",o)};a.addEventListener("DOMContentLoaded",o),e._reactRetry=o}}function xa(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Xf=null;function Pg(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function Ug(e,t,a){switch(t=Is(a),e){case"html":if(e=t.documentElement,!e)throw Error(i(452));return e;case"head":if(e=t.head,!e)throw Error(i(453));return e;case"body":if(e=t.body,!e)throw Error(i(454));return e;default:throw Error(i(451))}}function ri(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Bt(e)}var la=new Map,qg=new Set;function Vs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var nr=L.d;L.d={f:nS,r:aS,D:rS,C:oS,L:lS,m:iS,X:uS,S:sS,M:cS};function nS(){var e=nr.f(),t=Ns();return e||t}function aS(e){var t=yn(e);t!==null&&t.tag===5&&t.type==="form"?sh(t):nr.r(e)}var Xo=typeof document>"u"?null:document;function Ig(e,t,a){var o=Xo;if(o&&typeof t=="string"&&t){var s=Jn(t);s='link[rel="'+e+'"][href="'+s+'"]',typeof a=="string"&&(s+='[crossorigin="'+a+'"]'),qg.has(s)||(qg.add(s),e={rel:e,crossOrigin:a,href:t},o.querySelector(s)===null&&(t=o.createElement("link"),sn(t,"link",e),te(t),o.head.appendChild(t)))}}function rS(e){nr.D(e),Ig("dns-prefetch",e,null)}function oS(e,t){nr.C(e,t),Ig("preconnect",e,t)}function lS(e,t,a){nr.L(e,t,a);var o=Xo;if(o&&e&&t){var s='link[rel="preload"][as="'+Jn(t)+'"]';t==="image"&&a&&a.imageSrcSet?(s+='[imagesrcset="'+Jn(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(s+='[imagesizes="'+Jn(a.imageSizes)+'"]')):s+='[href="'+Jn(e)+'"]';var f=s;switch(t){case"style":f=Ko(e);break;case"script":f=Qo(e)}la.has(f)||(e=y({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),la.set(f,e),o.querySelector(s)!==null||t==="style"&&o.querySelector(oi(f))||t==="script"&&o.querySelector(li(f))||(t=o.createElement("link"),sn(t,"link",e),te(t),o.head.appendChild(t)))}}function iS(e,t){nr.m(e,t);var a=Xo;if(a&&e){var o=t&&typeof t.as=="string"?t.as:"script",s='link[rel="modulepreload"][as="'+Jn(o)+'"][href="'+Jn(e)+'"]',f=s;switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=Qo(e)}if(!la.has(f)&&(e=y({rel:"modulepreload",href:e},t),la.set(f,e),a.querySelector(s)===null)){switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(li(f)))return}o=a.createElement("link"),sn(o,"link",e),te(o),a.head.appendChild(o)}}}function sS(e,t,a){nr.S(e,t,a);var o=Xo;if(o&&e){var s=va(o).hoistableStyles,f=Ko(e);t=t||"default";var g=s.get(f);if(!g){var C={loading:0,preload:null};if(g=o.querySelector(oi(f)))C.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},a),(a=la.get(f))&&Kf(e,a);var k=g=o.createElement("link");te(k),sn(k,"link",e),k._p=new Promise(function(F,ie){k.onload=F,k.onerror=ie}),k.addEventListener("load",function(){C.loading|=1}),k.addEventListener("error",function(){C.loading|=2}),C.loading|=4,Ys(g,t,o)}g={type:"stylesheet",instance:g,count:1,state:C},s.set(f,g)}}}function uS(e,t){nr.X(e,t);var a=Xo;if(a&&e){var o=va(a).hoistableScripts,s=Qo(e),f=o.get(s);f||(f=a.querySelector(li(s)),f||(e=y({src:e,async:!0},t),(t=la.get(s))&&Qf(e,t),f=a.createElement("script"),te(f),sn(f,"link",e),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},o.set(s,f))}}function cS(e,t){nr.M(e,t);var a=Xo;if(a&&e){var o=va(a).hoistableScripts,s=Qo(e),f=o.get(s);f||(f=a.querySelector(li(s)),f||(e=y({src:e,async:!0,type:"module"},t),(t=la.get(s))&&Qf(e,t),f=a.createElement("script"),te(f),sn(f,"link",e),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},o.set(s,f))}}function Vg(e,t,a,o){var s=(s=le.current)?Vs(s):null;if(!s)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Ko(a.href),a=va(s).hoistableStyles,o=a.get(t),o||(o={type:"style",instance:null,count:0,state:null},a.set(t,o)),o):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Ko(a.href);var f=va(s).hoistableStyles,g=f.get(e);if(g||(s=s.ownerDocument||s,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(e,g),(f=s.querySelector(oi(e)))&&!f._p&&(g.instance=f,g.state.loading=5),la.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},la.set(e,a),f||fS(s,e,a,g.state))),t&&o===null)throw Error(i(528,""));return g}if(t&&o!==null)throw Error(i(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Qo(a),a=va(s).hoistableScripts,o=a.get(t),o||(o={type:"script",instance:null,count:0,state:null},a.set(t,o)),o):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Ko(e){return'href="'+Jn(e)+'"'}function oi(e){return'link[rel="stylesheet"]['+e+"]"}function Yg(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function fS(e,t,a,o){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?o.loading=1:(t=e.createElement("link"),o.preload=t,t.addEventListener("load",function(){return o.loading|=1}),t.addEventListener("error",function(){return o.loading|=2}),sn(t,"link",a),te(t),e.head.appendChild(t))}function Qo(e){return'[src="'+Jn(e)+'"]'}function li(e){return"script[async]"+e}function Gg(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var o=e.querySelector('style[data-href~="'+Jn(a.href)+'"]');if(o)return t.instance=o,te(o),o;var s=y({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return o=(e.ownerDocument||e).createElement("style"),te(o),sn(o,"style",s),Ys(o,a.precedence,e),t.instance=o;case"stylesheet":s=Ko(a.href);var f=e.querySelector(oi(s));if(f)return t.state.loading|=4,t.instance=f,te(f),f;o=Yg(a),(s=la.get(s))&&Kf(o,s),f=(e.ownerDocument||e).createElement("link"),te(f);var g=f;return g._p=new Promise(function(C,k){g.onload=C,g.onerror=k}),sn(f,"link",o),t.state.loading|=4,Ys(f,a.precedence,e),t.instance=f;case"script":return f=Qo(a.src),(s=e.querySelector(li(f)))?(t.instance=s,te(s),s):(o=a,(s=la.get(f))&&(o=y({},a),Qf(o,s)),e=e.ownerDocument||e,s=e.createElement("script"),te(s),sn(s,"link",o),e.head.appendChild(s),t.instance=s);case"void":return null;default:throw Error(i(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(o=t.instance,t.state.loading|=4,Ys(o,a.precedence,e));return t.instance}function Ys(e,t,a){for(var o=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=o.length?o[o.length-1]:null,f=s,g=0;g<o.length;g++){var C=o[g];if(C.dataset.precedence===t)f=C;else if(f!==s)break}f?f.parentNode.insertBefore(e,f.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Kf(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Qf(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Gs=null;function Xg(e,t,a){if(Gs===null){var o=new Map,s=Gs=new Map;s.set(a,o)}else s=Gs,o=s.get(a),o||(o=new Map,s.set(a,o));if(o.has(e))return o;for(o.set(e,null),a=a.getElementsByTagName(e),s=0;s<a.length;s++){var f=a[s];if(!(f[Qn]||f[zt]||e==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var g=f.getAttribute(t)||"";g=e+g;var C=o.get(g);C?C.push(f):o.set(g,[f])}}return o}function Kg(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function dS(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Qg(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var ii=null;function pS(){}function mS(e,t,a){if(ii===null)throw Error(i(475));var o=ii;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var s=Ko(a.href),f=e.querySelector(oi(s));if(f){e=f._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(o.count++,o=Xs.bind(o),e.then(o,o)),t.state.loading|=4,t.instance=f,te(f);return}f=e.ownerDocument||e,a=Yg(a),(s=la.get(s))&&Kf(a,s),f=f.createElement("link"),te(f);var g=f;g._p=new Promise(function(C,k){g.onload=C,g.onerror=k}),sn(f,"link",a),t.instance=f}o.stylesheets===null&&(o.stylesheets=new Map),o.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(o.count++,t=Xs.bind(o),e.addEventListener("load",t),e.addEventListener("error",t))}}function hS(){if(ii===null)throw Error(i(475));var e=ii;return e.stylesheets&&e.count===0&&Ff(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Ff(e,e.stylesheets),e.unsuspend){var o=e.unsuspend;e.unsuspend=null,o()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Xs(){if(this.count--,this.count===0){if(this.stylesheets)Ff(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Ks=null;function Ff(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ks=new Map,t.forEach(gS,e),Ks=null,Xs.call(e))}function gS(e,t){if(!(t.state.loading&4)){var a=Ks.get(e);if(a)var o=a.get(null);else{a=new Map,Ks.set(e,a);for(var s=e.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<s.length;f++){var g=s[f];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(a.set(g.dataset.precedence,g),o=g)}o&&a.set(null,o)}s=t.instance,g=s.getAttribute("data-precedence"),f=a.get(g)||o,f===o&&a.set(null,s),a.set(g,s),this.count++,o=Xs.bind(this),s.addEventListener("load",o),s.addEventListener("error",o),f?f.parentNode.insertBefore(s,f.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(s,e.firstChild)),t.state.loading|=4}}var si={$$typeof:D,Provider:null,Consumer:null,_currentValue:Y,_currentValue2:Y,_threadCount:0};function yS(e,t,a,o,s,f,g,C){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=tn(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=tn(0),this.hiddenUpdates=tn(null),this.identifierPrefix=o,this.onUncaughtError=s,this.onCaughtError=f,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=C,this.incompleteTransitions=new Map}function Fg(e,t,a,o,s,f,g,C,k,F,ie,ce){return e=new yS(e,t,a,g,C,k,F,ce),t=1,f===!0&&(t|=24),f=jn(3,null,null,t),e.current=f,f.stateNode=e,t=zc(),t.refCount++,e.pooledCache=t,t.refCount++,f.memoizedState={element:o,isDehydrated:a,cache:t},kc(f),e}function Wg(e){return e?(e=Oo,e):Oo}function Zg(e,t,a,o,s,f){s=Wg(s),o.context===null?o.context=s:o.pendingContext=s,o=mr(t),o.payload={element:a},f=f===void 0?null:f,f!==null&&(o.callback=f),a=hr(e,o,t),a!==null&&(Ln(a,e,t),Ll(a,e,t))}function Jg(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function Wf(e,t){Jg(e,t),(e=e.alternate)&&Jg(e,t)}function ey(e){if(e.tag===13){var t=Ro(e,67108864);t!==null&&Ln(t,e,67108864),Wf(e,67108864)}}var Qs=!0;function vS(e,t,a,o){var s=T.T;T.T=null;var f=L.p;try{L.p=2,Zf(e,t,a,o)}finally{L.p=f,T.T=s}}function bS(e,t,a,o){var s=T.T;T.T=null;var f=L.p;try{L.p=8,Zf(e,t,a,o)}finally{L.p=f,T.T=s}}function Zf(e,t,a,o){if(Qs){var s=Jf(o);if(s===null)Hf(e,t,o,Fs,a),ny(e,o);else if(xS(s,e,t,a,o))o.stopPropagation();else if(ny(e,o),t&4&&-1<SS.indexOf(e)){for(;s!==null;){var f=yn(s);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var g=Ce(f.pendingLanes);if(g!==0){var C=f;for(C.pendingLanes|=2,C.entangledLanes|=2;g;){var k=1<<31-et(g);C.entanglements[1]|=k,g&=~k}Aa(f),(ht&6)===0&&(js=at()+500,ti(0))}}break;case 13:C=Ro(f,2),C!==null&&Ln(C,f,2),Ns(),Wf(f,2)}if(f=Jf(o),f===null&&Hf(e,t,o,Fs,a),f===s)break;s=f}s!==null&&o.stopPropagation()}else Hf(e,t,o,null,a)}}function Jf(e){return e=ac(e),ed(e)}var Fs=null;function ed(e){if(Fs=null,e=Mt(e),e!==null){var t=c(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=d(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Fs=e,null}function ty(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(he()){case st:return 2;case Te:return 8;case He:case ye:return 32;case qt:return 268435456;default:return 32}default:return 32}}var td=!1,wr=null,Ar=null,zr=null,ui=new Map,ci=new Map,Dr=[],SS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ny(e,t){switch(e){case"focusin":case"focusout":wr=null;break;case"dragenter":case"dragleave":Ar=null;break;case"mouseover":case"mouseout":zr=null;break;case"pointerover":case"pointerout":ui.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ci.delete(t.pointerId)}}function fi(e,t,a,o,s,f){return e===null||e.nativeEvent!==f?(e={blockedOn:t,domEventName:a,eventSystemFlags:o,nativeEvent:f,targetContainers:[s]},t!==null&&(t=yn(t),t!==null&&ey(t)),e):(e.eventSystemFlags|=o,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function xS(e,t,a,o,s){switch(t){case"focusin":return wr=fi(wr,e,t,a,o,s),!0;case"dragenter":return Ar=fi(Ar,e,t,a,o,s),!0;case"mouseover":return zr=fi(zr,e,t,a,o,s),!0;case"pointerover":var f=s.pointerId;return ui.set(f,fi(ui.get(f)||null,e,t,a,o,s)),!0;case"gotpointercapture":return f=s.pointerId,ci.set(f,fi(ci.get(f)||null,e,t,a,o,s)),!0}return!1}function ay(e){var t=Mt(e.target);if(t!==null){var a=c(t);if(a!==null){if(t=a.tag,t===13){if(t=d(a),t!==null){e.blockedOn=t,yl(e.priority,function(){if(a.tag===13){var o=_n();o=ur(o);var s=Ro(a,o);s!==null&&Ln(s,a,o),Wf(a,o)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ws(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Jf(e.nativeEvent);if(a===null){a=e.nativeEvent;var o=new a.constructor(a.type,a);nc=o,a.target.dispatchEvent(o),nc=null}else return t=yn(a),t!==null&&ey(t),e.blockedOn=a,!1;t.shift()}return!0}function ry(e,t,a){Ws(e)&&a.delete(t)}function CS(){td=!1,wr!==null&&Ws(wr)&&(wr=null),Ar!==null&&Ws(Ar)&&(Ar=null),zr!==null&&Ws(zr)&&(zr=null),ui.forEach(ry),ci.forEach(ry)}function Zs(e,t){e.blockedOn===t&&(e.blockedOn=null,td||(td=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,CS)))}var Js=null;function oy(e){Js!==e&&(Js=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Js===e&&(Js=null);for(var t=0;t<e.length;t+=3){var a=e[t],o=e[t+1],s=e[t+2];if(typeof o!="function"){if(ed(o||a)===null)continue;break}var f=yn(a);f!==null&&(e.splice(t,3),t-=3,Jc(f,{pending:!0,data:s,method:a.method,action:o},o,s))}}))}function di(e){function t(k){return Zs(k,e)}wr!==null&&Zs(wr,e),Ar!==null&&Zs(Ar,e),zr!==null&&Zs(zr,e),ui.forEach(t),ci.forEach(t);for(var a=0;a<Dr.length;a++){var o=Dr[a];o.blockedOn===e&&(o.blockedOn=null)}for(;0<Dr.length&&(a=Dr[0],a.blockedOn===null);)ay(a),a.blockedOn===null&&Dr.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(o=0;o<a.length;o+=3){var s=a[o],f=a[o+1],g=s[Xt]||null;if(typeof f=="function")g||oy(a);else if(g){var C=null;if(f&&f.hasAttribute("formAction")){if(s=f,g=f[Xt]||null)C=g.formAction;else if(ed(s)!==null)continue}else C=g.action;typeof C=="function"?a[o+1]=C:(a.splice(o,3),o-=3),oy(a)}}}function nd(e){this._internalRoot=e}eu.prototype.render=nd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));var a=t.current,o=_n();Zg(a,o,e,t,null,null)},eu.prototype.unmount=nd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Zg(e.current,2,null,e,null,null),Ns(),t[Kn]=null}};function eu(e){this._internalRoot=e}eu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ea();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Dr.length&&t!==0&&t<Dr[a].priority;a++);Dr.splice(a,0,e),a===0&&ay(e)}};var ly=r.version;if(ly!=="19.1.0")throw Error(i(527,ly,"19.1.0"));L.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=m(t),e=e!==null?h(e):null,e=e===null?null:e.stateNode,e};var TS={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:T,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var tu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!tu.isDisabled&&tu.supportsFiber)try{rt=tu.inject(TS),ke=tu}catch{}}return mi.createRoot=function(e,t){if(!u(e))throw Error(i(299));var a=!1,o="",s=Ch,f=Th,g=Eh,C=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(o=t.identifierPrefix),t.onUncaughtError!==void 0&&(s=t.onUncaughtError),t.onCaughtError!==void 0&&(f=t.onCaughtError),t.onRecoverableError!==void 0&&(g=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(C=t.unstable_transitionCallbacks)),t=Fg(e,1,!1,null,null,a,o,s,f,g,C,null),e[Kn]=t.current,Lf(e),new nd(t)},mi.hydrateRoot=function(e,t,a){if(!u(e))throw Error(i(299));var o=!1,s="",f=Ch,g=Th,C=Eh,k=null,F=null;return a!=null&&(a.unstable_strictMode===!0&&(o=!0),a.identifierPrefix!==void 0&&(s=a.identifierPrefix),a.onUncaughtError!==void 0&&(f=a.onUncaughtError),a.onCaughtError!==void 0&&(g=a.onCaughtError),a.onRecoverableError!==void 0&&(C=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(k=a.unstable_transitionCallbacks),a.formState!==void 0&&(F=a.formState)),t=Fg(e,1,!0,t,a??null,o,s,f,g,C,k,F),t.context=Wg(null),a=t.current,o=_n(),o=ur(o),s=mr(o),s.callback=null,hr(a,s,o),a=o,t.current.lanes=a,zn(t,a),Aa(t),e[Kn]=t.current,Lf(e),new eu(t)},mi.version="19.1.0",mi}var gy;function jS(){if(gy)return od.exports;gy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),od.exports=$S(),od.exports}var kS=jS();const Ai={black:"#000",white:"#fff"},Fo={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Wo={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},Zo={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Jo={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},el={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"},hi={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},NS={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"};function or(n,...r){const l=new URL(`https://mui.com/production-error/?code=${n}`);return r.forEach(i=>l.searchParams.append("args[]",i)),`Minified MUI error #${n}; visit ${l} for the full message.`}const Ta="$$material";function Su(){return Su=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var l=arguments[r];for(var i in l)({}).hasOwnProperty.call(l,i)&&(n[i]=l[i])}return n},Su.apply(null,arguments)}function BS(n){if(n.sheet)return n.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===n)return document.styleSheets[r]}function _S(n){var r=document.createElement("style");return r.setAttribute("data-emotion",n.key),n.nonce!==void 0&&r.setAttribute("nonce",n.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var LS=function(){function n(l){var i=this;this._insertTag=function(u){var c;i.tags.length===0?i.insertionPoint?c=i.insertionPoint.nextSibling:i.prepend?c=i.container.firstChild:c=i.before:c=i.tags[i.tags.length-1].nextSibling,i.container.insertBefore(u,c),i.tags.push(u)},this.isSpeedy=l.speedy===void 0?!0:l.speedy,this.tags=[],this.ctr=0,this.nonce=l.nonce,this.key=l.key,this.container=l.container,this.prepend=l.prepend,this.insertionPoint=l.insertionPoint,this.before=null}var r=n.prototype;return r.hydrate=function(i){i.forEach(this._insertTag)},r.insert=function(i){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(_S(this));var u=this.tags[this.tags.length-1];if(this.isSpeedy){var c=BS(u);try{c.insertRule(i,c.cssRules.length)}catch{}}else u.appendChild(document.createTextNode(i));this.ctr++},r.flush=function(){this.tags.forEach(function(i){var u;return(u=i.parentNode)==null?void 0:u.removeChild(i)}),this.tags=[],this.ctr=0},n}(),mn="-ms-",xu="-moz-",dt="-webkit-",Kv="comm",Gd="rule",Xd="decl",HS="@import",Qv="@keyframes",PS="@layer",US=Math.abs,Au=String.fromCharCode,qS=Object.assign;function IS(n,r){return un(n,0)^45?(((r<<2^un(n,0))<<2^un(n,1))<<2^un(n,2))<<2^un(n,3):0}function Fv(n){return n.trim()}function VS(n,r){return(n=r.exec(n))?n[0]:n}function pt(n,r,l){return n.replace(r,l)}function Cd(n,r){return n.indexOf(r)}function un(n,r){return n.charCodeAt(r)|0}function zi(n,r,l){return n.slice(r,l)}function ja(n){return n.length}function Kd(n){return n.length}function nu(n,r){return r.push(n),n}function YS(n,r){return n.map(r).join("")}var zu=1,ul=1,Wv=0,An=0,Wt=0,ml="";function Du(n,r,l,i,u,c,d){return{value:n,root:r,parent:l,type:i,props:u,children:c,line:zu,column:ul,length:d,return:""}}function gi(n,r){return qS(Du("",null,null,"",null,null,0),n,{length:-n.length},r)}function GS(){return Wt}function XS(){return Wt=An>0?un(ml,--An):0,ul--,Wt===10&&(ul=1,zu--),Wt}function qn(){return Wt=An<Wv?un(ml,An++):0,ul++,Wt===10&&(ul=1,zu++),Wt}function _a(){return un(ml,An)}function fu(){return An}function _i(n,r){return zi(ml,n,r)}function Di(n){switch(n){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Zv(n){return zu=ul=1,Wv=ja(ml=n),An=0,[]}function Jv(n){return ml="",n}function du(n){return Fv(_i(An-1,Td(n===91?n+2:n===40?n+1:n)))}function KS(n){for(;(Wt=_a())&&Wt<33;)qn();return Di(n)>2||Di(Wt)>3?"":" "}function QS(n,r){for(;--r&&qn()&&!(Wt<48||Wt>102||Wt>57&&Wt<65||Wt>70&&Wt<97););return _i(n,fu()+(r<6&&_a()==32&&qn()==32))}function Td(n){for(;qn();)switch(Wt){case n:return An;case 34:case 39:n!==34&&n!==39&&Td(Wt);break;case 40:n===41&&Td(n);break;case 92:qn();break}return An}function FS(n,r){for(;qn()&&n+Wt!==57;)if(n+Wt===84&&_a()===47)break;return"/*"+_i(r,An-1)+"*"+Au(n===47?n:qn())}function WS(n){for(;!Di(_a());)qn();return _i(n,An)}function ZS(n){return Jv(pu("",null,null,null,[""],n=Zv(n),0,[0],n))}function pu(n,r,l,i,u,c,d,p,m){for(var h=0,y=0,x=d,R=0,M=0,E=0,v=1,w=1,j=1,P=0,D="",$=u,z=c,N=i,I=D;w;)switch(E=P,P=qn()){case 40:if(E!=108&&un(I,x-1)==58){Cd(I+=pt(du(P),"&","&\f"),"&\f")!=-1&&(j=-1);break}case 34:case 39:case 91:I+=du(P);break;case 9:case 10:case 13:case 32:I+=KS(E);break;case 92:I+=QS(fu()-1,7);continue;case 47:switch(_a()){case 42:case 47:nu(JS(FS(qn(),fu()),r,l),m);break;default:I+="/"}break;case 123*v:p[h++]=ja(I)*j;case 125*v:case 59:case 0:switch(P){case 0:case 125:w=0;case 59+y:j==-1&&(I=pt(I,/\f/g,"")),M>0&&ja(I)-x&&nu(M>32?vy(I+";",i,l,x-1):vy(pt(I," ","")+";",i,l,x-2),m);break;case 59:I+=";";default:if(nu(N=yy(I,r,l,h,y,u,p,D,$=[],z=[],x),c),P===123)if(y===0)pu(I,r,N,N,$,c,x,p,z);else switch(R===99&&un(I,3)===110?100:R){case 100:case 108:case 109:case 115:pu(n,N,N,i&&nu(yy(n,N,N,0,0,u,p,D,u,$=[],x),z),u,z,x,p,i?$:z);break;default:pu(I,N,N,N,[""],z,0,p,z)}}h=y=M=0,v=j=1,D=I="",x=d;break;case 58:x=1+ja(I),M=E;default:if(v<1){if(P==123)--v;else if(P==125&&v++==0&&XS()==125)continue}switch(I+=Au(P),P*v){case 38:j=y>0?1:(I+="\f",-1);break;case 44:p[h++]=(ja(I)-1)*j,j=1;break;case 64:_a()===45&&(I+=du(qn())),R=_a(),y=x=ja(D=I+=WS(fu())),P++;break;case 45:E===45&&ja(I)==2&&(v=0)}}return c}function yy(n,r,l,i,u,c,d,p,m,h,y){for(var x=u-1,R=u===0?c:[""],M=Kd(R),E=0,v=0,w=0;E<i;++E)for(var j=0,P=zi(n,x+1,x=US(v=d[E])),D=n;j<M;++j)(D=Fv(v>0?R[j]+" "+P:pt(P,/&\f/g,R[j])))&&(m[w++]=D);return Du(n,r,l,u===0?Gd:p,m,h,y)}function JS(n,r,l){return Du(n,r,l,Kv,Au(GS()),zi(n,2,-2),0)}function vy(n,r,l,i){return Du(n,r,l,Xd,zi(n,0,i),zi(n,i+1,-1),i)}function ol(n,r){for(var l="",i=Kd(n),u=0;u<i;u++)l+=r(n[u],u,n,r)||"";return l}function ex(n,r,l,i){switch(n.type){case PS:if(n.children.length)break;case HS:case Xd:return n.return=n.return||n.value;case Kv:return"";case Qv:return n.return=n.value+"{"+ol(n.children,i)+"}";case Gd:n.value=n.props.join(",")}return ja(l=ol(n.children,i))?n.return=n.value+"{"+l+"}":""}function tx(n){var r=Kd(n);return function(l,i,u,c){for(var d="",p=0;p<r;p++)d+=n[p](l,i,u,c)||"";return d}}function nx(n){return function(r){r.root||(r=r.return)&&n(r)}}function e0(n){var r=Object.create(null);return function(l){return r[l]===void 0&&(r[l]=n(l)),r[l]}}var ax=function(r,l,i){for(var u=0,c=0;u=c,c=_a(),u===38&&c===12&&(l[i]=1),!Di(c);)qn();return _i(r,An)},rx=function(r,l){var i=-1,u=44;do switch(Di(u)){case 0:u===38&&_a()===12&&(l[i]=1),r[i]+=ax(An-1,l,i);break;case 2:r[i]+=du(u);break;case 4:if(u===44){r[++i]=_a()===58?"&\f":"",l[i]=r[i].length;break}default:r[i]+=Au(u)}while(u=qn());return r},ox=function(r,l){return Jv(rx(Zv(r),l))},by=new WeakMap,lx=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var l=r.value,i=r.parent,u=r.column===i.column&&r.line===i.line;i.type!=="rule";)if(i=i.parent,!i)return;if(!(r.props.length===1&&l.charCodeAt(0)!==58&&!by.get(i))&&!u){by.set(r,!0);for(var c=[],d=ox(l,c),p=i.props,m=0,h=0;m<d.length;m++)for(var y=0;y<p.length;y++,h++)r.props[h]=c[m]?d[m].replace(/&\f/g,p[y]):p[y]+" "+d[m]}}},ix=function(r){if(r.type==="decl"){var l=r.value;l.charCodeAt(0)===108&&l.charCodeAt(2)===98&&(r.return="",r.value="")}};function t0(n,r){switch(IS(n,r)){case 5103:return dt+"print-"+n+n;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return dt+n+n;case 5349:case 4246:case 4810:case 6968:case 2756:return dt+n+xu+n+mn+n+n;case 6828:case 4268:return dt+n+mn+n+n;case 6165:return dt+n+mn+"flex-"+n+n;case 5187:return dt+n+pt(n,/(\w+).+(:[^]+)/,dt+"box-$1$2"+mn+"flex-$1$2")+n;case 5443:return dt+n+mn+"flex-item-"+pt(n,/flex-|-self/,"")+n;case 4675:return dt+n+mn+"flex-line-pack"+pt(n,/align-content|flex-|-self/,"")+n;case 5548:return dt+n+mn+pt(n,"shrink","negative")+n;case 5292:return dt+n+mn+pt(n,"basis","preferred-size")+n;case 6060:return dt+"box-"+pt(n,"-grow","")+dt+n+mn+pt(n,"grow","positive")+n;case 4554:return dt+pt(n,/([^-])(transform)/g,"$1"+dt+"$2")+n;case 6187:return pt(pt(pt(n,/(zoom-|grab)/,dt+"$1"),/(image-set)/,dt+"$1"),n,"")+n;case 5495:case 3959:return pt(n,/(image-set\([^]*)/,dt+"$1$`$1");case 4968:return pt(pt(n,/(.+:)(flex-)?(.*)/,dt+"box-pack:$3"+mn+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+dt+n+n;case 4095:case 3583:case 4068:case 2532:return pt(n,/(.+)-inline(.+)/,dt+"$1$2")+n;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(ja(n)-1-r>6)switch(un(n,r+1)){case 109:if(un(n,r+4)!==45)break;case 102:return pt(n,/(.+:)(.+)-([^]+)/,"$1"+dt+"$2-$3$1"+xu+(un(n,r+3)==108?"$3":"$2-$3"))+n;case 115:return~Cd(n,"stretch")?t0(pt(n,"stretch","fill-available"),r)+n:n}break;case 4949:if(un(n,r+1)!==115)break;case 6444:switch(un(n,ja(n)-3-(~Cd(n,"!important")&&10))){case 107:return pt(n,":",":"+dt)+n;case 101:return pt(n,/(.+:)([^;!]+)(;|!.+)?/,"$1"+dt+(un(n,14)===45?"inline-":"")+"box$3$1"+dt+"$2$3$1"+mn+"$2box$3")+n}break;case 5936:switch(un(n,r+11)){case 114:return dt+n+mn+pt(n,/[svh]\w+-[tblr]{2}/,"tb")+n;case 108:return dt+n+mn+pt(n,/[svh]\w+-[tblr]{2}/,"tb-rl")+n;case 45:return dt+n+mn+pt(n,/[svh]\w+-[tblr]{2}/,"lr")+n}return dt+n+mn+n+n}return n}var sx=function(r,l,i,u){if(r.length>-1&&!r.return)switch(r.type){case Xd:r.return=t0(r.value,r.length);break;case Qv:return ol([gi(r,{value:pt(r.value,"@","@"+dt)})],u);case Gd:if(r.length)return YS(r.props,function(c){switch(VS(c,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return ol([gi(r,{props:[pt(c,/:(read-\w+)/,":"+xu+"$1")]})],u);case"::placeholder":return ol([gi(r,{props:[pt(c,/:(plac\w+)/,":"+dt+"input-$1")]}),gi(r,{props:[pt(c,/:(plac\w+)/,":"+xu+"$1")]}),gi(r,{props:[pt(c,/:(plac\w+)/,mn+"input-$1")]})],u)}return""})}},ux=[sx],cx=function(r){var l=r.key;if(l==="css"){var i=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(i,function(v){var w=v.getAttribute("data-emotion");w.indexOf(" ")!==-1&&(document.head.appendChild(v),v.setAttribute("data-s",""))})}var u=r.stylisPlugins||ux,c={},d,p=[];d=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+l+' "]'),function(v){for(var w=v.getAttribute("data-emotion").split(" "),j=1;j<w.length;j++)c[w[j]]=!0;p.push(v)});var m,h=[lx,ix];{var y,x=[ex,nx(function(v){y.insert(v)})],R=tx(h.concat(u,x)),M=function(w){return ol(ZS(w),R)};m=function(w,j,P,D){y=P,M(w?w+"{"+j.styles+"}":j.styles),D&&(E.inserted[j.name]=!0)}}var E={key:l,sheet:new LS({key:l,container:d,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:c,registered:{},insert:m};return E.sheet.hydrate(p),E},ud={exports:{}},mt={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sy;function fx(){if(Sy)return mt;Sy=1;var n=typeof Symbol=="function"&&Symbol.for,r=n?Symbol.for("react.element"):60103,l=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,u=n?Symbol.for("react.strict_mode"):60108,c=n?Symbol.for("react.profiler"):60114,d=n?Symbol.for("react.provider"):60109,p=n?Symbol.for("react.context"):60110,m=n?Symbol.for("react.async_mode"):60111,h=n?Symbol.for("react.concurrent_mode"):60111,y=n?Symbol.for("react.forward_ref"):60112,x=n?Symbol.for("react.suspense"):60113,R=n?Symbol.for("react.suspense_list"):60120,M=n?Symbol.for("react.memo"):60115,E=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,w=n?Symbol.for("react.fundamental"):60117,j=n?Symbol.for("react.responder"):60118,P=n?Symbol.for("react.scope"):60119;function D(z){if(typeof z=="object"&&z!==null){var N=z.$$typeof;switch(N){case r:switch(z=z.type,z){case m:case h:case i:case c:case u:case x:return z;default:switch(z=z&&z.$$typeof,z){case p:case y:case E:case M:case d:return z;default:return N}}case l:return N}}}function $(z){return D(z)===h}return mt.AsyncMode=m,mt.ConcurrentMode=h,mt.ContextConsumer=p,mt.ContextProvider=d,mt.Element=r,mt.ForwardRef=y,mt.Fragment=i,mt.Lazy=E,mt.Memo=M,mt.Portal=l,mt.Profiler=c,mt.StrictMode=u,mt.Suspense=x,mt.isAsyncMode=function(z){return $(z)||D(z)===m},mt.isConcurrentMode=$,mt.isContextConsumer=function(z){return D(z)===p},mt.isContextProvider=function(z){return D(z)===d},mt.isElement=function(z){return typeof z=="object"&&z!==null&&z.$$typeof===r},mt.isForwardRef=function(z){return D(z)===y},mt.isFragment=function(z){return D(z)===i},mt.isLazy=function(z){return D(z)===E},mt.isMemo=function(z){return D(z)===M},mt.isPortal=function(z){return D(z)===l},mt.isProfiler=function(z){return D(z)===c},mt.isStrictMode=function(z){return D(z)===u},mt.isSuspense=function(z){return D(z)===x},mt.isValidElementType=function(z){return typeof z=="string"||typeof z=="function"||z===i||z===h||z===c||z===u||z===x||z===R||typeof z=="object"&&z!==null&&(z.$$typeof===E||z.$$typeof===M||z.$$typeof===d||z.$$typeof===p||z.$$typeof===y||z.$$typeof===w||z.$$typeof===j||z.$$typeof===P||z.$$typeof===v)},mt.typeOf=D,mt}var xy;function dx(){return xy||(xy=1,ud.exports=fx()),ud.exports}var cd,Cy;function px(){if(Cy)return cd;Cy=1;var n=dx(),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},l={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},c={};c[n.ForwardRef]=i,c[n.Memo]=u;function d(E){return n.isMemo(E)?u:c[E.$$typeof]||r}var p=Object.defineProperty,m=Object.getOwnPropertyNames,h=Object.getOwnPropertySymbols,y=Object.getOwnPropertyDescriptor,x=Object.getPrototypeOf,R=Object.prototype;function M(E,v,w){if(typeof v!="string"){if(R){var j=x(v);j&&j!==R&&M(E,j,w)}var P=m(v);h&&(P=P.concat(h(v)));for(var D=d(E),$=d(v),z=0;z<P.length;++z){var N=P[z];if(!l[N]&&!(w&&w[N])&&!($&&$[N])&&!(D&&D[N])){var I=y(v,N);try{p(E,N,I)}catch{}}}}return E}return cd=M,cd}px();var mx=!0;function n0(n,r,l){var i="";return l.split(" ").forEach(function(u){n[u]!==void 0?r.push(n[u]+";"):u&&(i+=u+" ")}),i}var Qd=function(r,l,i){var u=r.key+"-"+l.name;(i===!1||mx===!1)&&r.registered[u]===void 0&&(r.registered[u]=l.styles)},Fd=function(r,l,i){Qd(r,l,i);var u=r.key+"-"+l.name;if(r.inserted[l.name]===void 0){var c=l;do r.insert(l===c?"."+u:"",c,r.sheet,!0),c=c.next;while(c!==void 0)}};function hx(n){for(var r=0,l,i=0,u=n.length;u>=4;++i,u-=4)l=n.charCodeAt(i)&255|(n.charCodeAt(++i)&255)<<8|(n.charCodeAt(++i)&255)<<16|(n.charCodeAt(++i)&255)<<24,l=(l&65535)*1540483477+((l>>>16)*59797<<16),l^=l>>>24,r=(l&65535)*1540483477+((l>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(u){case 3:r^=(n.charCodeAt(i+2)&255)<<16;case 2:r^=(n.charCodeAt(i+1)&255)<<8;case 1:r^=n.charCodeAt(i)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var gx={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},yx=/[A-Z]|^ms/g,vx=/_EMO_([^_]+?)_([^]*?)_EMO_/g,a0=function(r){return r.charCodeAt(1)===45},Ty=function(r){return r!=null&&typeof r!="boolean"},fd=e0(function(n){return a0(n)?n:n.replace(yx,"-$&").toLowerCase()}),Ey=function(r,l){switch(r){case"animation":case"animationName":if(typeof l=="string")return l.replace(vx,function(i,u,c){return ka={name:u,styles:c,next:ka},u})}return gx[r]!==1&&!a0(r)&&typeof l=="number"&&l!==0?l+"px":l};function $i(n,r,l){if(l==null)return"";var i=l;if(i.__emotion_styles!==void 0)return i;switch(typeof l){case"boolean":return"";case"object":{var u=l;if(u.anim===1)return ka={name:u.name,styles:u.styles,next:ka},u.name;var c=l;if(c.styles!==void 0){var d=c.next;if(d!==void 0)for(;d!==void 0;)ka={name:d.name,styles:d.styles,next:ka},d=d.next;var p=c.styles+";";return p}return bx(n,r,l)}case"function":{if(n!==void 0){var m=ka,h=l(n);return ka=m,$i(n,r,h)}break}}var y=l;if(r==null)return y;var x=r[y];return x!==void 0?x:y}function bx(n,r,l){var i="";if(Array.isArray(l))for(var u=0;u<l.length;u++)i+=$i(n,r,l[u])+";";else for(var c in l){var d=l[c];if(typeof d!="object"){var p=d;r!=null&&r[p]!==void 0?i+=c+"{"+r[p]+"}":Ty(p)&&(i+=fd(c)+":"+Ey(c,p)+";")}else if(Array.isArray(d)&&typeof d[0]=="string"&&(r==null||r[d[0]]===void 0))for(var m=0;m<d.length;m++)Ty(d[m])&&(i+=fd(c)+":"+Ey(c,d[m])+";");else{var h=$i(n,r,d);switch(c){case"animation":case"animationName":{i+=fd(c)+":"+h+";";break}default:i+=c+"{"+h+"}"}}}return i}var Ry=/label:\s*([^\s;{]+)\s*(;|$)/g,ka;function Li(n,r,l){if(n.length===1&&typeof n[0]=="object"&&n[0]!==null&&n[0].styles!==void 0)return n[0];var i=!0,u="";ka=void 0;var c=n[0];if(c==null||c.raw===void 0)i=!1,u+=$i(l,r,c);else{var d=c;u+=d[0]}for(var p=1;p<n.length;p++)if(u+=$i(l,r,n[p]),i){var m=c;u+=m[p]}Ry.lastIndex=0;for(var h="",y;(y=Ry.exec(u))!==null;)h+="-"+y[1];var x=hx(u)+h;return{name:x,styles:u,next:ka}}var Sx=function(r){return r()},r0=bu.useInsertionEffect?bu.useInsertionEffect:!1,o0=r0||Sx,Oy=r0||O.useLayoutEffect,l0=O.createContext(typeof HTMLElement<"u"?cx({key:"css"}):null);l0.Provider;var Wd=function(r){return O.forwardRef(function(l,i){var u=O.useContext(l0);return r(l,u,i)})},Hi=O.createContext({}),Zd={}.hasOwnProperty,Ed="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",xx=function(r,l){var i={};for(var u in l)Zd.call(l,u)&&(i[u]=l[u]);return i[Ed]=r,i},Cx=function(r){var l=r.cache,i=r.serialized,u=r.isStringTag;return Qd(l,i,u),o0(function(){return Fd(l,i,u)}),null},Tx=Wd(function(n,r,l){var i=n.css;typeof i=="string"&&r.registered[i]!==void 0&&(i=r.registered[i]);var u=n[Ed],c=[i],d="";typeof n.className=="string"?d=n0(r.registered,c,n.className):n.className!=null&&(d=n.className+" ");var p=Li(c,void 0,O.useContext(Hi));d+=r.key+"-"+p.name;var m={};for(var h in n)Zd.call(n,h)&&h!=="css"&&h!==Ed&&(m[h]=n[h]);return m.className=d,l&&(m.ref=l),O.createElement(O.Fragment,null,O.createElement(Cx,{cache:r,serialized:p,isStringTag:typeof u=="string"}),O.createElement(u,m))}),Ex=Tx,My=function(r,l){var i=arguments;if(l==null||!Zd.call(l,"css"))return O.createElement.apply(void 0,i);var u=i.length,c=new Array(u);c[0]=Ex,c[1]=xx(r,l);for(var d=2;d<u;d++)c[d]=i[d];return O.createElement.apply(null,c)};(function(n){var r;r||(r=n.JSX||(n.JSX={}))})(My||(My={}));var Rx=Wd(function(n,r){var l=n.styles,i=Li([l],void 0,O.useContext(Hi)),u=O.useRef();return Oy(function(){var c=r.key+"-global",d=new r.sheet.constructor({key:c,nonce:r.sheet.nonce,container:r.sheet.container,speedy:r.sheet.isSpeedy}),p=!1,m=document.querySelector('style[data-emotion="'+c+" "+i.name+'"]');return r.sheet.tags.length&&(d.before=r.sheet.tags[0]),m!==null&&(p=!0,m.setAttribute("data-emotion",c),d.hydrate([m])),u.current=[d,p],function(){d.flush()}},[r]),Oy(function(){var c=u.current,d=c[0],p=c[1];if(p){c[1]=!1;return}if(i.next!==void 0&&Fd(r,i.next,!0),d.tags.length){var m=d.tags[d.tags.length-1].nextElementSibling;d.before=m,d.flush()}r.insert("",i,d,!1)},[r,i.name]),null});function Jd(){for(var n=arguments.length,r=new Array(n),l=0;l<n;l++)r[l]=arguments[l];return Li(r)}function Pi(){var n=Jd.apply(void 0,arguments),r="animation-"+n.name;return{name:r,styles:"@keyframes "+r+"{"+n.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var Ox=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Mx=e0(function(n){return Ox.test(n)||n.charCodeAt(0)===111&&n.charCodeAt(1)===110&&n.charCodeAt(2)<91}),wx=Mx,Ax=function(r){return r!=="theme"},wy=function(r){return typeof r=="string"&&r.charCodeAt(0)>96?wx:Ax},Ay=function(r,l,i){var u;if(l){var c=l.shouldForwardProp;u=r.__emotion_forwardProp&&c?function(d){return r.__emotion_forwardProp(d)&&c(d)}:c}return typeof u!="function"&&i&&(u=r.__emotion_forwardProp),u},zx=function(r){var l=r.cache,i=r.serialized,u=r.isStringTag;return Qd(l,i,u),o0(function(){return Fd(l,i,u)}),null},Dx=function n(r,l){var i=r.__emotion_real===r,u=i&&r.__emotion_base||r,c,d;l!==void 0&&(c=l.label,d=l.target);var p=Ay(r,l,i),m=p||wy(u),h=!m("as");return function(){var y=arguments,x=i&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(c!==void 0&&x.push("label:"+c+";"),y[0]==null||y[0].raw===void 0)x.push.apply(x,y);else{var R=y[0];x.push(R[0]);for(var M=y.length,E=1;E<M;E++)x.push(y[E],R[E])}var v=Wd(function(w,j,P){var D=h&&w.as||u,$="",z=[],N=w;if(w.theme==null){N={};for(var I in w)N[I]=w[I];N.theme=O.useContext(Hi)}typeof w.className=="string"?$=n0(j.registered,z,w.className):w.className!=null&&($=w.className+" ");var G=Li(x.concat(z),j.registered,N);$+=j.key+"-"+G.name,d!==void 0&&($+=" "+d);var K=h&&p===void 0?wy(D):m,b={};for(var B in w)h&&B==="as"||K(B)&&(b[B]=w[B]);return b.className=$,P&&(b.ref=P),O.createElement(O.Fragment,null,O.createElement(zx,{cache:j,serialized:G,isStringTag:typeof D=="string"}),O.createElement(D,b))});return v.displayName=c!==void 0?c:"Styled("+(typeof u=="string"?u:u.displayName||u.name||"Component")+")",v.defaultProps=r.defaultProps,v.__emotion_real=v,v.__emotion_base=u,v.__emotion_styles=x,v.__emotion_forwardProp=p,Object.defineProperty(v,"toString",{value:function(){return"."+d}}),v.withComponent=function(w,j){var P=n(w,Su({},l,j,{shouldForwardProp:Ay(v,j,!0)}));return P.apply(void 0,x)},v}},$x=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],Rd=Dx.bind(null);$x.forEach(function(n){Rd[n]=Rd(n)});function jx(n){return n==null||Object.keys(n).length===0}function i0(n){const{styles:r,defaultTheme:l={}}=n,i=typeof r=="function"?u=>r(jx(u)?l:u):r;return S.jsx(Rx,{styles:i})}function s0(n,r){return Rd(n,r)}function kx(n,r){Array.isArray(n.__emotion_styles)&&(n.__emotion_styles=r(n.__emotion_styles))}const zy=[];function Nr(n){return zy[0]=n,Li(zy)}var dd={exports:{}},Ct={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dy;function Nx(){if(Dy)return Ct;Dy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),R=Symbol.for("react.view_transition"),M=Symbol.for("react.client.reference");function E(v){if(typeof v=="object"&&v!==null){var w=v.$$typeof;switch(w){case n:switch(v=v.type,v){case l:case u:case i:case m:case h:case R:return v;default:switch(v=v&&v.$$typeof,v){case d:case p:case x:case y:return v;case c:return v;default:return w}}case r:return w}}}return Ct.ContextConsumer=c,Ct.ContextProvider=d,Ct.Element=n,Ct.ForwardRef=p,Ct.Fragment=l,Ct.Lazy=x,Ct.Memo=y,Ct.Portal=r,Ct.Profiler=u,Ct.StrictMode=i,Ct.Suspense=m,Ct.SuspenseList=h,Ct.isContextConsumer=function(v){return E(v)===c},Ct.isContextProvider=function(v){return E(v)===d},Ct.isElement=function(v){return typeof v=="object"&&v!==null&&v.$$typeof===n},Ct.isForwardRef=function(v){return E(v)===p},Ct.isFragment=function(v){return E(v)===l},Ct.isLazy=function(v){return E(v)===x},Ct.isMemo=function(v){return E(v)===y},Ct.isPortal=function(v){return E(v)===r},Ct.isProfiler=function(v){return E(v)===u},Ct.isStrictMode=function(v){return E(v)===i},Ct.isSuspense=function(v){return E(v)===m},Ct.isSuspenseList=function(v){return E(v)===h},Ct.isValidElementType=function(v){return typeof v=="string"||typeof v=="function"||v===l||v===u||v===i||v===m||v===h||typeof v=="object"&&v!==null&&(v.$$typeof===x||v.$$typeof===y||v.$$typeof===d||v.$$typeof===c||v.$$typeof===p||v.$$typeof===M||v.getModuleId!==void 0)},Ct.typeOf=E,Ct}var $y;function Bx(){return $y||($y=1,dd.exports=Nx()),dd.exports}var u0=Bx();function Na(n){if(typeof n!="object"||n===null)return!1;const r=Object.getPrototypeOf(n);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)}function c0(n){if(O.isValidElement(n)||u0.isValidElementType(n)||!Na(n))return n;const r={};return Object.keys(n).forEach(l=>{r[l]=c0(n[l])}),r}function gn(n,r,l={clone:!0}){const i=l.clone?{...n}:n;return Na(n)&&Na(r)&&Object.keys(r).forEach(u=>{O.isValidElement(r[u])||u0.isValidElementType(r[u])?i[u]=r[u]:Na(r[u])&&Object.prototype.hasOwnProperty.call(n,u)&&Na(n[u])?i[u]=gn(n[u],r[u],l):l.clone?i[u]=Na(r[u])?c0(r[u]):r[u]:i[u]=r[u]}),i}const _x=n=>{const r=Object.keys(n).map(l=>({key:l,val:n[l]}))||[];return r.sort((l,i)=>l.val-i.val),r.reduce((l,i)=>({...l,[i.key]:i.val}),{})};function Lx(n){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:l="px",step:i=5,...u}=n,c=_x(r),d=Object.keys(c);function p(R){return`@media (min-width:${typeof r[R]=="number"?r[R]:R}${l})`}function m(R){return`@media (max-width:${(typeof r[R]=="number"?r[R]:R)-i/100}${l})`}function h(R,M){const E=d.indexOf(M);return`@media (min-width:${typeof r[R]=="number"?r[R]:R}${l}) and (max-width:${(E!==-1&&typeof r[d[E]]=="number"?r[d[E]]:M)-i/100}${l})`}function y(R){return d.indexOf(R)+1<d.length?h(R,d[d.indexOf(R)+1]):p(R)}function x(R){const M=d.indexOf(R);return M===0?p(d[1]):M===d.length-1?m(d[M]):h(R,d[d.indexOf(R)+1]).replace("@media","@media not all and")}return{keys:d,values:c,up:p,down:m,between:h,only:y,not:x,unit:l,...u}}function jy(n,r){if(!n.containerQueries)return r;const l=Object.keys(r).filter(i=>i.startsWith("@container")).sort((i,u)=>{const c=/min-width:\s*([0-9.]+)/;return+(i.match(c)?.[1]||0)-+(u.match(c)?.[1]||0)});return l.length?l.reduce((i,u)=>{const c=r[u];return delete i[u],i[u]=c,i},{...r}):r}function Hx(n,r){return r==="@"||r.startsWith("@")&&(n.some(l=>r.startsWith(`@${l}`))||!!r.match(/^@\d/))}function Px(n,r){const l=r.match(/^@([^/]+)?\/?(.+)?$/);if(!l)return null;const[,i,u]=l,c=Number.isNaN(+i)?i||0:+i;return n.containerQueries(u).up(c)}function Ux(n){const r=(c,d)=>c.replace("@media",d?`@container ${d}`:"@container");function l(c,d){c.up=(...p)=>r(n.breakpoints.up(...p),d),c.down=(...p)=>r(n.breakpoints.down(...p),d),c.between=(...p)=>r(n.breakpoints.between(...p),d),c.only=(...p)=>r(n.breakpoints.only(...p),d),c.not=(...p)=>{const m=r(n.breakpoints.not(...p),d);return m.includes("not all and")?m.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):m}}const i={},u=c=>(l(i,c),i);return l(u),{...n,containerQueries:u}}const qx={borderRadius:4};function Ei(n,r){return r?gn(n,r,{clone:!1}):n}const $u={xs:0,sm:600,md:900,lg:1200,xl:1536},ky={keys:["xs","sm","md","lg","xl"],up:n=>`@media (min-width:${$u[n]}px)`},Ix={containerQueries:n=>({up:r=>{let l=typeof r=="number"?r:$u[r]||r;return typeof l=="number"&&(l=`${l}px`),n?`@container ${n} (min-width:${l})`:`@container (min-width:${l})`}})};function lr(n,r,l){const i=n.theme||{};if(Array.isArray(r)){const c=i.breakpoints||ky;return r.reduce((d,p,m)=>(d[c.up(c.keys[m])]=l(r[m]),d),{})}if(typeof r=="object"){const c=i.breakpoints||ky;return Object.keys(r).reduce((d,p)=>{if(Hx(c.keys,p)){const m=Px(i.containerQueries?i:Ix,p);m&&(d[m]=l(r[p],p))}else if(Object.keys(c.values||$u).includes(p)){const m=c.up(p);d[m]=l(r[p],p)}else{const m=p;d[m]=r[m]}return d},{})}return l(r)}function Vx(n={}){return n.keys?.reduce((l,i)=>{const u=n.up(i);return l[u]={},l},{})||{}}function Ny(n,r){return n.reduce((l,i)=>{const u=l[i];return(!u||Object.keys(u).length===0)&&delete l[i],l},r)}function de(n){if(typeof n!="string")throw new Error(or(7));return n.charAt(0).toUpperCase()+n.slice(1)}function ju(n,r,l=!0){if(!r||typeof r!="string")return null;if(n&&n.vars&&l){const i=`vars.${r}`.split(".").reduce((u,c)=>u&&u[c]?u[c]:null,n);if(i!=null)return i}return r.split(".").reduce((i,u)=>i&&i[u]!=null?i[u]:null,n)}function Cu(n,r,l,i=l){let u;return typeof n=="function"?u=n(l):Array.isArray(n)?u=n[l]||i:u=ju(n,l)||i,r&&(u=r(u,i,n)),u}function Yt(n){const{prop:r,cssProperty:l=n.prop,themeKey:i,transform:u}=n,c=d=>{if(d[r]==null)return null;const p=d[r],m=d.theme,h=ju(m,i)||{};return lr(d,p,x=>{let R=Cu(h,u,x);return x===R&&typeof x=="string"&&(R=Cu(h,u,`${r}${x==="default"?"":de(x)}`,x)),l===!1?R:{[l]:R}})};return c.propTypes={},c.filterProps=[r],c}function Yx(n){const r={};return l=>(r[l]===void 0&&(r[l]=n(l)),r[l])}const Gx={m:"margin",p:"padding"},Xx={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},By={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Kx=Yx(n=>{if(n.length>2)if(By[n])n=By[n];else return[n];const[r,l]=n.split(""),i=Gx[r],u=Xx[l]||"";return Array.isArray(u)?u.map(c=>i+c):[i+u]}),ep=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],tp=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...ep,...tp];function Ui(n,r,l,i){const u=ju(n,r,!0)??l;return typeof u=="number"||typeof u=="string"?c=>typeof c=="string"?c:typeof u=="string"?u.startsWith("var(")&&c===0?0:u.startsWith("var(")&&c===1?u:`calc(${c} * ${u})`:u*c:Array.isArray(u)?c=>{if(typeof c=="string")return c;const d=Math.abs(c),p=u[d];return c>=0?p:typeof p=="number"?-p:typeof p=="string"&&p.startsWith("var(")?`calc(-1 * ${p})`:`-${p}`}:typeof u=="function"?u:()=>{}}function np(n){return Ui(n,"spacing",8)}function qi(n,r){return typeof r=="string"||r==null?r:n(r)}function Qx(n,r){return l=>n.reduce((i,u)=>(i[u]=qi(r,l),i),{})}function Fx(n,r,l,i){if(!r.includes(l))return null;const u=Kx(l),c=Qx(u,i),d=n[l];return lr(n,d,c)}function f0(n,r){const l=np(n.theme);return Object.keys(n).map(i=>Fx(n,r,i,l)).reduce(Ei,{})}function Pt(n){return f0(n,ep)}Pt.propTypes={};Pt.filterProps=ep;function Ut(n){return f0(n,tp)}Ut.propTypes={};Ut.filterProps=tp;function d0(n=8,r=np({spacing:n})){if(n.mui)return n;const l=(...i)=>(i.length===0?[1]:i).map(c=>{const d=r(c);return typeof d=="number"?`${d}px`:d}).join(" ");return l.mui=!0,l}function ku(...n){const r=n.reduce((i,u)=>(u.filterProps.forEach(c=>{i[c]=u}),i),{}),l=i=>Object.keys(i).reduce((u,c)=>r[c]?Ei(u,r[c](i)):u,{});return l.propTypes={},l.filterProps=n.reduce((i,u)=>i.concat(u.filterProps),[]),l}function ua(n){return typeof n!="number"?n:`${n}px solid`}function ma(n,r){return Yt({prop:n,themeKey:"borders",transform:r})}const Wx=ma("border",ua),Zx=ma("borderTop",ua),Jx=ma("borderRight",ua),e2=ma("borderBottom",ua),t2=ma("borderLeft",ua),n2=ma("borderColor"),a2=ma("borderTopColor"),r2=ma("borderRightColor"),o2=ma("borderBottomColor"),l2=ma("borderLeftColor"),i2=ma("outline",ua),s2=ma("outlineColor"),Nu=n=>{if(n.borderRadius!==void 0&&n.borderRadius!==null){const r=Ui(n.theme,"shape.borderRadius",4),l=i=>({borderRadius:qi(r,i)});return lr(n,n.borderRadius,l)}return null};Nu.propTypes={};Nu.filterProps=["borderRadius"];ku(Wx,Zx,Jx,e2,t2,n2,a2,r2,o2,l2,Nu,i2,s2);const Bu=n=>{if(n.gap!==void 0&&n.gap!==null){const r=Ui(n.theme,"spacing",8),l=i=>({gap:qi(r,i)});return lr(n,n.gap,l)}return null};Bu.propTypes={};Bu.filterProps=["gap"];const _u=n=>{if(n.columnGap!==void 0&&n.columnGap!==null){const r=Ui(n.theme,"spacing",8),l=i=>({columnGap:qi(r,i)});return lr(n,n.columnGap,l)}return null};_u.propTypes={};_u.filterProps=["columnGap"];const Lu=n=>{if(n.rowGap!==void 0&&n.rowGap!==null){const r=Ui(n.theme,"spacing",8),l=i=>({rowGap:qi(r,i)});return lr(n,n.rowGap,l)}return null};Lu.propTypes={};Lu.filterProps=["rowGap"];const u2=Yt({prop:"gridColumn"}),c2=Yt({prop:"gridRow"}),f2=Yt({prop:"gridAutoFlow"}),d2=Yt({prop:"gridAutoColumns"}),p2=Yt({prop:"gridAutoRows"}),m2=Yt({prop:"gridTemplateColumns"}),h2=Yt({prop:"gridTemplateRows"}),g2=Yt({prop:"gridTemplateAreas"}),y2=Yt({prop:"gridArea"});ku(Bu,_u,Lu,u2,c2,f2,d2,p2,m2,h2,g2,y2);function ll(n,r){return r==="grey"?r:n}const v2=Yt({prop:"color",themeKey:"palette",transform:ll}),b2=Yt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:ll}),S2=Yt({prop:"backgroundColor",themeKey:"palette",transform:ll});ku(v2,b2,S2);function Un(n){return n<=1&&n!==0?`${n*100}%`:n}const x2=Yt({prop:"width",transform:Un}),ap=n=>{if(n.maxWidth!==void 0&&n.maxWidth!==null){const r=l=>{const i=n.theme?.breakpoints?.values?.[l]||$u[l];return i?n.theme?.breakpoints?.unit!=="px"?{maxWidth:`${i}${n.theme.breakpoints.unit}`}:{maxWidth:i}:{maxWidth:Un(l)}};return lr(n,n.maxWidth,r)}return null};ap.filterProps=["maxWidth"];const C2=Yt({prop:"minWidth",transform:Un}),T2=Yt({prop:"height",transform:Un}),E2=Yt({prop:"maxHeight",transform:Un}),R2=Yt({prop:"minHeight",transform:Un});Yt({prop:"size",cssProperty:"width",transform:Un});Yt({prop:"size",cssProperty:"height",transform:Un});const O2=Yt({prop:"boxSizing"});ku(x2,ap,C2,T2,E2,R2,O2);const Ii={border:{themeKey:"borders",transform:ua},borderTop:{themeKey:"borders",transform:ua},borderRight:{themeKey:"borders",transform:ua},borderBottom:{themeKey:"borders",transform:ua},borderLeft:{themeKey:"borders",transform:ua},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:ua},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Nu},color:{themeKey:"palette",transform:ll},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:ll},backgroundColor:{themeKey:"palette",transform:ll},p:{style:Ut},pt:{style:Ut},pr:{style:Ut},pb:{style:Ut},pl:{style:Ut},px:{style:Ut},py:{style:Ut},padding:{style:Ut},paddingTop:{style:Ut},paddingRight:{style:Ut},paddingBottom:{style:Ut},paddingLeft:{style:Ut},paddingX:{style:Ut},paddingY:{style:Ut},paddingInline:{style:Ut},paddingInlineStart:{style:Ut},paddingInlineEnd:{style:Ut},paddingBlock:{style:Ut},paddingBlockStart:{style:Ut},paddingBlockEnd:{style:Ut},m:{style:Pt},mt:{style:Pt},mr:{style:Pt},mb:{style:Pt},ml:{style:Pt},mx:{style:Pt},my:{style:Pt},margin:{style:Pt},marginTop:{style:Pt},marginRight:{style:Pt},marginBottom:{style:Pt},marginLeft:{style:Pt},marginX:{style:Pt},marginY:{style:Pt},marginInline:{style:Pt},marginInlineStart:{style:Pt},marginInlineEnd:{style:Pt},marginBlock:{style:Pt},marginBlockStart:{style:Pt},marginBlockEnd:{style:Pt},displayPrint:{cssProperty:!1,transform:n=>({"@media print":{display:n}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Bu},rowGap:{style:Lu},columnGap:{style:_u},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Un},maxWidth:{style:ap},minWidth:{transform:Un},height:{transform:Un},maxHeight:{transform:Un},minHeight:{transform:Un},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function M2(...n){const r=n.reduce((i,u)=>i.concat(Object.keys(u)),[]),l=new Set(r);return n.every(i=>l.size===Object.keys(i).length)}function w2(n,r){return typeof n=="function"?n(r):n}function A2(){function n(l,i,u,c){const d={[l]:i,theme:u},p=c[l];if(!p)return{[l]:i};const{cssProperty:m=l,themeKey:h,transform:y,style:x}=p;if(i==null)return null;if(h==="typography"&&i==="inherit")return{[l]:i};const R=ju(u,h)||{};return x?x(d):lr(d,i,E=>{let v=Cu(R,y,E);return E===v&&typeof E=="string"&&(v=Cu(R,y,`${l}${E==="default"?"":de(E)}`,E)),m===!1?v:{[m]:v}})}function r(l){const{sx:i,theme:u={},nested:c}=l||{};if(!i)return null;const d=u.unstable_sxConfig??Ii;function p(m){let h=m;if(typeof m=="function")h=m(u);else if(typeof m!="object")return m;if(!h)return null;const y=Vx(u.breakpoints),x=Object.keys(y);let R=y;return Object.keys(h).forEach(M=>{const E=w2(h[M],u);if(E!=null)if(typeof E=="object")if(d[M])R=Ei(R,n(M,E,u,d));else{const v=lr({theme:u},E,w=>({[M]:w}));M2(v,E)?R[M]=r({sx:E,theme:u,nested:!0}):R=Ei(R,v)}else R=Ei(R,n(M,E,u,d))}),!c&&u.modularCssLayers?{"@layer sx":jy(u,Ny(x,R))}:jy(u,Ny(x,R))}return Array.isArray(i)?i.map(p):p(i)}return r}const Br=A2();Br.filterProps=["sx"];function z2(n,r){const l=this;if(l.vars){if(!l.colorSchemes?.[n]||typeof l.getColorSchemeSelector!="function")return{};let i=l.getColorSchemeSelector(n);return i==="&"?r:((i.includes("data-")||i.includes("."))&&(i=`*:where(${i.replace(/\s*&$/,"")}) &`),{[i]:r})}return l.palette.mode===n?r:{}}function Hu(n={},...r){const{breakpoints:l={},palette:i={},spacing:u,shape:c={},...d}=n,p=Lx(l),m=d0(u);let h=gn({breakpoints:p,direction:"ltr",components:{},palette:{mode:"light",...i},spacing:m,shape:{...qx,...c}},d);return h=Ux(h),h.applyStyles=z2,h=r.reduce((y,x)=>gn(y,x),h),h.unstable_sxConfig={...Ii,...d?.unstable_sxConfig},h.unstable_sx=function(x){return Br({sx:x,theme:this})},h}function D2(n){return Object.keys(n).length===0}function Pu(n=null){const r=O.useContext(Hi);return!r||D2(r)?n:r}const $2=Hu();function Vi(n=$2){return Pu(n)}function pd(n){const r=Nr(n);return n!==r&&r.styles?(r.styles.match(/^@layer\s+[^{]*$/)||(r.styles=`@layer global{${r.styles}}`),r):n}function p0({styles:n,themeId:r,defaultTheme:l={}}){const i=Vi(l),u=r&&i[r]||i;let c=typeof n=="function"?n(u):n;return u.modularCssLayers&&(Array.isArray(c)?c=c.map(d=>pd(typeof d=="function"?d(u):d)):c=pd(c)),S.jsx(i0,{styles:c})}const j2=n=>{const r={systemProps:{},otherProps:{}},l=n?.theme?.unstable_sxConfig??Ii;return Object.keys(n).forEach(i=>{l[i]?r.systemProps[i]=n[i]:r.otherProps[i]=n[i]}),r};function rp(n){const{sx:r,...l}=n,{systemProps:i,otherProps:u}=j2(l);let c;return Array.isArray(r)?c=[i,...r]:typeof r=="function"?c=(...d)=>{const p=r(...d);return Na(p)?{...i,...p}:i}:c={...i,...r},{...u,sx:c}}const _y=n=>n,k2=()=>{let n=_y;return{configure(r){n=r},generate(r){return n(r)},reset(){n=_y}}},m0=k2();function h0(n){var r,l,i="";if(typeof n=="string"||typeof n=="number")i+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(r=0;r<u;r++)n[r]&&(l=h0(n[r]))&&(i&&(i+=" "),i+=l)}else for(l in n)n[l]&&(i&&(i+=" "),i+=l);return i}function Me(){for(var n,r,l=0,i="",u=arguments.length;l<u;l++)(n=arguments[l])&&(r=h0(n))&&(i&&(i+=" "),i+=r);return i}function N2(n={}){const{themeId:r,defaultTheme:l,defaultClassName:i="MuiBox-root",generateClassName:u}=n,c=s0("div",{shouldForwardProp:p=>p!=="theme"&&p!=="sx"&&p!=="as"})(Br);return O.forwardRef(function(m,h){const y=Vi(l),{className:x,component:R="div",...M}=rp(m);return S.jsx(c,{as:R,ref:h,className:Me(x,u?u(i):i),theme:r&&y[r]||y,...M})})}const B2={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Xe(n,r,l="Mui"){const i=B2[r];return i?`${l}-${i}`:`${m0.generate(n)}-${r}`}function Ke(n,r,l="Mui"){const i={};return r.forEach(u=>{i[u]=Xe(n,u,l)}),i}function g0(n){const{variants:r,...l}=n,i={variants:r,style:Nr(l),isProcessed:!0};return i.style===l||r&&r.forEach(u=>{typeof u.style!="function"&&(u.style=Nr(u.style))}),i}const _2=Hu();function md(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}function io(n,r){return r&&n&&typeof n=="object"&&n.styles&&!n.styles.startsWith("@layer")&&(n.styles=`@layer ${r}{${String(n.styles)}}`),n}function L2(n){return n?(r,l)=>l[n]:null}function H2(n,r,l){n.theme=U2(n.theme)?l:n.theme[r]||n.theme}function mu(n,r,l){const i=typeof r=="function"?r(n):r;if(Array.isArray(i))return i.flatMap(u=>mu(n,u,l));if(Array.isArray(i?.variants)){let u;if(i.isProcessed)u=l?io(i.style,l):i.style;else{const{variants:c,...d}=i;u=l?io(Nr(d),l):d}return y0(n,i.variants,[u],l)}return i?.isProcessed?l?io(Nr(i.style),l):i.style:l?io(Nr(i),l):i}function y0(n,r,l=[],i=void 0){let u;e:for(let c=0;c<r.length;c+=1){const d=r[c];if(typeof d.props=="function"){if(u??={...n,...n.ownerState,ownerState:n.ownerState},!d.props(u))continue}else for(const p in d.props)if(n[p]!==d.props[p]&&n.ownerState?.[p]!==d.props[p])continue e;typeof d.style=="function"?(u??={...n,...n.ownerState,ownerState:n.ownerState},l.push(i?io(Nr(d.style(u)),i):d.style(u))):l.push(i?io(Nr(d.style),i):d.style)}return l}function v0(n={}){const{themeId:r,defaultTheme:l=_2,rootShouldForwardProp:i=md,slotShouldForwardProp:u=md}=n;function c(p){H2(p,r,l)}return(p,m={})=>{kx(p,N=>N.filter(I=>I!==Br));const{name:h,slot:y,skipVariantsResolver:x,skipSx:R,overridesResolver:M=L2(I2(y)),...E}=m,v=h&&h.startsWith("Mui")||y?"components":"custom",w=x!==void 0?x:y&&y!=="Root"&&y!=="root"||!1,j=R||!1;let P=md;y==="Root"||y==="root"?P=i:y?P=u:q2(p)&&(P=void 0);const D=s0(p,{shouldForwardProp:P,label:P2(),...E}),$=N=>{if(N.__emotion_real===N)return N;if(typeof N=="function")return function(G){return mu(G,N,G.theme.modularCssLayers?v:void 0)};if(Na(N)){const I=g0(N);return function(K){return I.variants?mu(K,I,K.theme.modularCssLayers?v:void 0):K.theme.modularCssLayers?io(I.style,v):I.style}}return N},z=(...N)=>{const I=[],G=N.map($),K=[];if(I.push(c),h&&M&&K.push(function(ae){const _=ae.theme.components?.[h]?.styleOverrides;if(!_)return null;const T={};for(const L in _)T[L]=mu(ae,_[L],ae.theme.modularCssLayers?"theme":void 0);return M(ae,T)}),h&&!w&&K.push(function(ae){const _=ae.theme?.components?.[h]?.variants;return _?y0(ae,_,[],ae.theme.modularCssLayers?"theme":void 0):null}),j||K.push(Br),Array.isArray(G[0])){const V=G.shift(),ae=new Array(I.length).fill(""),ee=new Array(K.length).fill("");let _;_=[...ae,...V,...ee],_.raw=[...ae,...V.raw,...ee],I.unshift(_)}const b=[...I,...G,...K],B=D(...b);return p.muiName&&(B.muiName=p.muiName),B};return D.withConfig&&(z.withConfig=D.withConfig),z}}function P2(n,r){return void 0}function U2(n){for(const r in n)return!1;return!0}function q2(n){return typeof n=="string"&&n.charCodeAt(0)>96}function I2(n){return n&&n.charAt(0).toLowerCase()+n.slice(1)}const V2=v0();function ji(n,r,l=!1){const i={...r};for(const u in n)if(Object.prototype.hasOwnProperty.call(n,u)){const c=u;if(c==="components"||c==="slots")i[c]={...n[c],...i[c]};else if(c==="componentsProps"||c==="slotProps"){const d=n[c],p=r[c];if(!p)i[c]=d||{};else if(!d)i[c]=p;else{i[c]={...p};for(const m in d)if(Object.prototype.hasOwnProperty.call(d,m)){const h=m;i[c][h]=ji(d[h],p[h],l)}}}else c==="className"&&l&&r.className?i.className=Me(n?.className,r?.className):c==="style"&&l&&r.style?i.style={...n?.style,...r?.style}:i[c]===void 0&&(i[c]=n[c])}return i}function b0(n){const{theme:r,name:l,props:i}=n;return!r||!r.components||!r.components[l]||!r.components[l].defaultProps?i:ji(r.components[l].defaultProps,i)}function Y2({props:n,name:r,defaultTheme:l,themeId:i}){let u=Vi(l);return i&&(u=u[i]||u),b0({theme:u,name:r,props:n})}const fa=typeof window<"u"?O.useLayoutEffect:O.useEffect;function G2(n,r,l,i,u){const[c,d]=O.useState(()=>u&&l?l(n).matches:i?i(n).matches:r);return fa(()=>{if(!l)return;const p=l(n),m=()=>{d(p.matches)};return m(),p.addEventListener("change",m),()=>{p.removeEventListener("change",m)}},[n,l]),c}const X2={...bu},S0=X2.useSyncExternalStore;function K2(n,r,l,i,u){const c=O.useCallback(()=>r,[r]),d=O.useMemo(()=>{if(u&&l)return()=>l(n).matches;if(i!==null){const{matches:y}=i(n);return()=>y}return c},[c,n,i,u,l]),[p,m]=O.useMemo(()=>{if(l===null)return[c,()=>()=>{}];const y=l(n);return[()=>y.matches,x=>(y.addEventListener("change",x),()=>{y.removeEventListener("change",x)})]},[c,l,n]);return S0(m,p,d)}function x0(n={}){const{themeId:r}=n;return function(i,u={}){let c=Pu();c&&r&&(c=c[r]||c);const d=typeof window<"u"&&typeof window.matchMedia<"u",{defaultMatches:p=!1,matchMedia:m=d?window.matchMedia:null,ssrMatchMedia:h=null,noSsr:y=!1}=b0({name:"MuiUseMediaQuery",props:u,theme:c});let x=typeof i=="function"?i(c):i;return x=x.replace(/^@media( ?)/m,""),x.includes("print")&&console.warn(["MUI: You have provided a `print` query to the `useMediaQuery` hook.","Using the print media query to modify print styles can lead to unexpected results.","Consider using the `displayPrint` field in the `sx` prop instead.","More information about `displayPrint` on our docs: https://mui.com/system/display/#display-in-print."].join(`
`)),(S0!==void 0?K2:G2)(x,p,m,h,y)}}x0();function Q2(n,r=Number.MIN_SAFE_INTEGER,l=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(n,l))}function op(n,r=0,l=1){return Q2(n,r,l)}function F2(n){n=n.slice(1);const r=new RegExp(`.{1,${n.length>=6?2:1}}`,"g");let l=n.match(r);return l&&l[0].length===1&&(l=l.map(i=>i+i)),l?`rgb${l.length===4?"a":""}(${l.map((i,u)=>u<3?parseInt(i,16):Math.round(parseInt(i,16)/255*1e3)/1e3).join(", ")})`:""}function _r(n){if(n.type)return n;if(n.charAt(0)==="#")return _r(F2(n));const r=n.indexOf("("),l=n.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(l))throw new Error(or(9,n));let i=n.substring(r+1,n.length-1),u;if(l==="color"){if(i=i.split(" "),u=i.shift(),i.length===4&&i[3].charAt(0)==="/"&&(i[3]=i[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(u))throw new Error(or(10,u))}else i=i.split(",");return i=i.map(c=>parseFloat(c)),{type:l,values:i,colorSpace:u}}const W2=n=>{const r=_r(n);return r.values.slice(0,3).map((l,i)=>r.type.includes("hsl")&&i!==0?`${l}%`:l).join(" ")},Si=(n,r)=>{try{return W2(n)}catch{return n}};function Uu(n){const{type:r,colorSpace:l}=n;let{values:i}=n;return r.includes("rgb")?i=i.map((u,c)=>c<3?parseInt(u,10):u):r.includes("hsl")&&(i[1]=`${i[1]}%`,i[2]=`${i[2]}%`),r.includes("color")?i=`${l} ${i.join(" ")}`:i=`${i.join(", ")}`,`${r}(${i})`}function C0(n){n=_r(n);const{values:r}=n,l=r[0],i=r[1]/100,u=r[2]/100,c=i*Math.min(u,1-u),d=(h,y=(h+l/30)%12)=>u-c*Math.max(Math.min(y-3,9-y,1),-1);let p="rgb";const m=[Math.round(d(0)*255),Math.round(d(8)*255),Math.round(d(4)*255)];return n.type==="hsla"&&(p+="a",m.push(r[3])),Uu({type:p,values:m})}function Od(n){n=_r(n);let r=n.type==="hsl"||n.type==="hsla"?_r(C0(n)).values:n.values;return r=r.map(l=>(n.type!=="color"&&(l/=255),l<=.03928?l/12.92:((l+.055)/1.055)**2.4)),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function Z2(n,r){const l=Od(n),i=Od(r);return(Math.max(l,i)+.05)/(Math.min(l,i)+.05)}function yt(n,r){return n=_r(n),r=op(r),(n.type==="rgb"||n.type==="hsl")&&(n.type+="a"),n.type==="color"?n.values[3]=`/${r}`:n.values[3]=r,Uu(n)}function au(n,r,l){try{return yt(n,r)}catch{return n}}function qu(n,r){if(n=_r(n),r=op(r),n.type.includes("hsl"))n.values[2]*=1-r;else if(n.type.includes("rgb")||n.type.includes("color"))for(let l=0;l<3;l+=1)n.values[l]*=1-r;return Uu(n)}function Et(n,r,l){try{return qu(n,r)}catch{return n}}function Iu(n,r){if(n=_r(n),r=op(r),n.type.includes("hsl"))n.values[2]+=(100-n.values[2])*r;else if(n.type.includes("rgb"))for(let l=0;l<3;l+=1)n.values[l]+=(255-n.values[l])*r;else if(n.type.includes("color"))for(let l=0;l<3;l+=1)n.values[l]+=(1-n.values[l])*r;return Uu(n)}function Rt(n,r,l){try{return Iu(n,r)}catch{return n}}function J2(n,r=.15){return Od(n)>.5?qu(n,r):Iu(n,r)}function ru(n,r,l){try{return J2(n,r)}catch{return n}}const T0=O.createContext(null);function lp(){return O.useContext(T0)}const eC=typeof Symbol=="function"&&Symbol.for,tC=eC?Symbol.for("mui.nested"):"__THEME_NESTED__";function nC(n,r){return typeof r=="function"?r(n):{...n,...r}}function aC(n){const{children:r,theme:l}=n,i=lp(),u=O.useMemo(()=>{const c=i===null?{...l}:nC(i,l);return c!=null&&(c[tC]=i!==null),c},[l,i]);return S.jsx(T0.Provider,{value:u,children:r})}const E0=O.createContext();function rC({value:n,...r}){return S.jsx(E0.Provider,{value:n??!0,...r})}const R0=()=>O.useContext(E0)??!1,O0=O.createContext(void 0);function oC({value:n,children:r}){return S.jsx(O0.Provider,{value:n,children:r})}function lC(n){const{theme:r,name:l,props:i}=n;if(!r||!r.components||!r.components[l])return i;const u=r.components[l];return u.defaultProps?ji(u.defaultProps,i,r.components.mergeClassNameAndStyle):!u.styleOverrides&&!u.variants?ji(u,i,r.components.mergeClassNameAndStyle):i}function iC({props:n,name:r}){const l=O.useContext(O0);return lC({props:n,name:r,theme:{components:l}})}let Ly=0;function sC(n){const[r,l]=O.useState(n),i=n||r;return O.useEffect(()=>{r==null&&(Ly+=1,l(`mui-${Ly}`))},[r]),i}const uC={...bu},Hy=uC.useId;function hl(n){if(Hy!==void 0){const r=Hy();return n??r}return sC(n)}function cC(n){const r=Pu(),l=hl()||"",{modularCssLayers:i}=n;let u="mui.global, mui.components, mui.theme, mui.custom, mui.sx";return!i||r!==null?u="":typeof i=="string"?u=i.replace(/mui(?!\.)/g,u):u=`@layer ${u};`,fa(()=>{const c=document.querySelector("head");if(!c)return;const d=c.firstChild;if(u){if(d&&d.hasAttribute?.("data-mui-layer-order")&&d.getAttribute("data-mui-layer-order")===l)return;const p=document.createElement("style");p.setAttribute("data-mui-layer-order",l),p.textContent=u,c.prepend(p)}else c.querySelector(`style[data-mui-layer-order="${l}"]`)?.remove()},[u,l]),u?S.jsx(p0,{styles:u}):null}const Py={};function Uy(n,r,l,i=!1){return O.useMemo(()=>{const u=n&&r[n]||r;if(typeof l=="function"){const c=l(u),d=n?{...r,[n]:c}:c;return i?()=>d:d}return n?{...r,[n]:l}:{...r,...l}},[n,r,l,i])}function M0(n){const{children:r,theme:l,themeId:i}=n,u=Pu(Py),c=lp()||Py,d=Uy(i,u,l),p=Uy(i,c,l,!0),m=(i?d[i]:d).direction==="rtl",h=cC(d);return S.jsx(aC,{theme:p,children:S.jsx(Hi.Provider,{value:d,children:S.jsx(rC,{value:m,children:S.jsxs(oC,{value:i?d[i].components:d.components,children:[h,r]})})})})}const qy={theme:void 0};function fC(n){let r,l;return function(u){let c=r;return(c===void 0||u.theme!==l)&&(qy.theme=u.theme,c=g0(n(qy)),r=c,l=u.theme),c}}const ip="mode",sp="color-scheme",dC="data-color-scheme";function pC(n){const{defaultMode:r="system",defaultLightColorScheme:l="light",defaultDarkColorScheme:i="dark",modeStorageKey:u=ip,colorSchemeStorageKey:c=sp,attribute:d=dC,colorSchemeNode:p="document.documentElement",nonce:m}=n||{};let h="",y=d;if(d==="class"&&(y=".%s"),d==="data"&&(y="[data-%s]"),y.startsWith(".")){const R=y.substring(1);h+=`${p}.classList.remove('${R}'.replace('%s', light), '${R}'.replace('%s', dark));
      ${p}.classList.add('${R}'.replace('%s', colorScheme));`}const x=y.match(/\[([^\]]+)\]/);if(x){const[R,M]=x[1].split("=");M||(h+=`${p}.removeAttribute('${R}'.replace('%s', light));
      ${p}.removeAttribute('${R}'.replace('%s', dark));`),h+=`
      ${p}.setAttribute('${R}'.replace('%s', colorScheme), ${M?`${M}.replace('%s', colorScheme)`:'""'});`}else h+=`${p}.setAttribute('${y}', colorScheme);`;return S.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?m:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${u}') || '${r}';
  const dark = localStorage.getItem('${c}-dark') || '${i}';
  const light = localStorage.getItem('${c}-light') || '${l}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${h}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function mC(){}const hC=({key:n,storageWindow:r})=>(!r&&typeof window<"u"&&(r=window),{get(l){if(typeof window>"u")return;if(!r)return l;let i;try{i=r.localStorage.getItem(n)}catch{}return i||l},set:l=>{if(r)try{r.localStorage.setItem(n,l)}catch{}},subscribe:l=>{if(!r)return mC;const i=u=>{const c=u.newValue;u.key===n&&l(c)};return r.addEventListener("storage",i),()=>{r.removeEventListener("storage",i)}}});function hd(){}function Iy(n){if(typeof window<"u"&&typeof window.matchMedia=="function"&&n==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function w0(n,r){if(n.mode==="light"||n.mode==="system"&&n.systemMode==="light")return r("light");if(n.mode==="dark"||n.mode==="system"&&n.systemMode==="dark")return r("dark")}function gC(n){return w0(n,r=>{if(r==="light")return n.lightColorScheme;if(r==="dark")return n.darkColorScheme})}function yC(n){const{defaultMode:r="light",defaultLightColorScheme:l,defaultDarkColorScheme:i,supportedColorSchemes:u=[],modeStorageKey:c=ip,colorSchemeStorageKey:d=sp,storageWindow:p=typeof window>"u"?void 0:window,storageManager:m=hC,noSsr:h=!1}=n,y=u.join(","),x=u.length>1,R=O.useMemo(()=>m?.({key:c,storageWindow:p}),[m,c,p]),M=O.useMemo(()=>m?.({key:`${d}-light`,storageWindow:p}),[m,d,p]),E=O.useMemo(()=>m?.({key:`${d}-dark`,storageWindow:p}),[m,d,p]),[v,w]=O.useState(()=>{const G=R?.get(r)||r,K=M?.get(l)||l,b=E?.get(i)||i;return{mode:G,systemMode:Iy(G),lightColorScheme:K,darkColorScheme:b}}),[j,P]=O.useState(h||!x);O.useEffect(()=>{P(!0)},[]);const D=gC(v),$=O.useCallback(G=>{w(K=>{if(G===K.mode)return K;const b=G??r;return R?.set(b),{...K,mode:b,systemMode:Iy(b)}})},[R,r]),z=O.useCallback(G=>{G?typeof G=="string"?G&&!y.includes(G)?console.error(`\`${G}\` does not exist in \`theme.colorSchemes\`.`):w(K=>{const b={...K};return w0(K,B=>{B==="light"&&(M?.set(G),b.lightColorScheme=G),B==="dark"&&(E?.set(G),b.darkColorScheme=G)}),b}):w(K=>{const b={...K},B=G.light===null?l:G.light,V=G.dark===null?i:G.dark;return B&&(y.includes(B)?(b.lightColorScheme=B,M?.set(B)):console.error(`\`${B}\` does not exist in \`theme.colorSchemes\`.`)),V&&(y.includes(V)?(b.darkColorScheme=V,E?.set(V)):console.error(`\`${V}\` does not exist in \`theme.colorSchemes\`.`)),b}):w(K=>(M?.set(l),E?.set(i),{...K,lightColorScheme:l,darkColorScheme:i}))},[y,M,E,l,i]),N=O.useCallback(G=>{v.mode==="system"&&w(K=>{const b=G?.matches?"dark":"light";return K.systemMode===b?K:{...K,systemMode:b}})},[v.mode]),I=O.useRef(N);return I.current=N,O.useEffect(()=>{if(typeof window.matchMedia!="function"||!x)return;const G=(...b)=>I.current(...b),K=window.matchMedia("(prefers-color-scheme: dark)");return K.addListener(G),G(K),()=>{K.removeListener(G)}},[x]),O.useEffect(()=>{if(x){const G=R?.subscribe(B=>{(!B||["light","dark","system"].includes(B))&&$(B||r)})||hd,K=M?.subscribe(B=>{(!B||y.match(B))&&z({light:B})})||hd,b=E?.subscribe(B=>{(!B||y.match(B))&&z({dark:B})})||hd;return()=>{G(),K(),b()}}},[z,$,y,r,p,x,R,M,E]),{...v,mode:j?v.mode:void 0,systemMode:j?v.systemMode:void 0,colorScheme:j?D:void 0,setMode:$,setColorScheme:z}}const vC="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function bC(n){const{themeId:r,theme:l={},modeStorageKey:i=ip,colorSchemeStorageKey:u=sp,disableTransitionOnChange:c=!1,defaultColorScheme:d,resolveTheme:p}=n,m={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},h=O.createContext(void 0),y=()=>O.useContext(h)||m,x={},R={};function M(j){const{children:P,theme:D,modeStorageKey:$=i,colorSchemeStorageKey:z=u,disableTransitionOnChange:N=c,storageManager:I,storageWindow:G=typeof window>"u"?void 0:window,documentNode:K=typeof document>"u"?void 0:document,colorSchemeNode:b=typeof document>"u"?void 0:document.documentElement,disableNestedContext:B=!1,disableStyleSheetGeneration:V=!1,defaultMode:ae="system",forceThemeRerender:ee=!1,noSsr:_}=j,T=O.useRef(!1),L=lp(),Y=O.useContext(h),X=!!Y&&!B,A=O.useMemo(()=>D||(typeof l=="function"?l():l),[D]),U=A[r],J=U||A,{colorSchemes:ne=x,components:fe=R,cssVarPrefix:ue}=J,le=Object.keys(ne).filter(rt=>!!ne[rt]).join(","),ve=O.useMemo(()=>le.split(","),[le]),xe=typeof d=="string"?d:d.light,be=typeof d=="string"?d:d.dark,me=ne[xe]&&ne[be]?ae:ne[J.defaultColorScheme]?.palette?.mode||J.palette?.mode,{mode:Re,setMode:Ne,systemMode:Le,lightColorScheme:$e,darkColorScheme:nt,colorScheme:Ve,setColorScheme:at}=yC({supportedColorSchemes:ve,defaultLightColorScheme:xe,defaultDarkColorScheme:be,modeStorageKey:$,colorSchemeStorageKey:z,defaultMode:me,storageManager:I,storageWindow:G,noSsr:_});let he=Re,st=Ve;X&&(he=Y.mode,st=Y.colorScheme);let Te=st||J.defaultColorScheme;J.vars&&!ee&&(Te=J.defaultColorScheme);const He=O.useMemo(()=>{const rt=J.generateThemeVars?.()||J.vars,ke={...J,components:fe,colorSchemes:ne,cssVarPrefix:ue,vars:rt};if(typeof ke.generateSpacing=="function"&&(ke.spacing=ke.generateSpacing()),Te){const Ye=ne[Te];Ye&&typeof Ye=="object"&&Object.keys(Ye).forEach(et=>{Ye[et]&&typeof Ye[et]=="object"?ke[et]={...ke[et],...Ye[et]}:ke[et]=Ye[et]})}return p?p(ke):ke},[J,Te,fe,ne,ue]),ye=J.colorSchemeSelector;fa(()=>{if(st&&b&&ye&&ye!=="media"){const rt=ye;let ke=ye;if(rt==="class"&&(ke=".%s"),rt==="data"&&(ke="[data-%s]"),rt?.startsWith("data-")&&!rt.includes("%s")&&(ke=`[${rt}="%s"]`),ke.startsWith("."))b.classList.remove(...ve.map(Ye=>ke.substring(1).replace("%s",Ye))),b.classList.add(ke.substring(1).replace("%s",st));else{const Ye=ke.replace("%s",st).match(/\[([^\]]+)\]/);if(Ye){const[et,vt]=Ye[1].split("=");vt||ve.forEach(Ae=>{b.removeAttribute(et.replace(st,Ae))}),b.setAttribute(et,vt?vt.replace(/"|'/g,""):"")}else b.setAttribute(ke,st)}}},[st,ye,b,ve]),O.useEffect(()=>{let rt;if(N&&T.current&&K){const ke=K.createElement("style");ke.appendChild(K.createTextNode(vC)),K.head.appendChild(ke),window.getComputedStyle(K.body),rt=setTimeout(()=>{K.head.removeChild(ke)},1)}return()=>{clearTimeout(rt)}},[st,N,K]),O.useEffect(()=>(T.current=!0,()=>{T.current=!1}),[]);const qt=O.useMemo(()=>({allColorSchemes:ve,colorScheme:st,darkColorScheme:nt,lightColorScheme:$e,mode:he,setColorScheme:at,setMode:Ne,systemMode:Le}),[ve,st,nt,$e,he,at,Ne,Le,He.colorSchemeSelector]);let ut=!0;(V||J.cssVariables===!1||X&&L?.cssVarPrefix===ue)&&(ut=!1);const At=S.jsxs(O.Fragment,{children:[S.jsx(M0,{themeId:U?r:void 0,theme:He,children:P}),ut&&S.jsx(i0,{styles:He.generateStyleSheets?.()||[]})]});return X?At:S.jsx(h.Provider,{value:qt,children:At})}const E=typeof d=="string"?d:d.light,v=typeof d=="string"?d:d.dark;return{CssVarsProvider:M,useColorScheme:y,getInitColorSchemeScript:j=>pC({colorSchemeStorageKey:u,defaultLightColorScheme:E,defaultDarkColorScheme:v,modeStorageKey:i,...j})}}function SC(n=""){function r(...i){if(!i.length)return"";const u=i[0];return typeof u=="string"&&!u.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${n?`${n}-`:""}${u}${r(...i.slice(1))})`:`, ${u}`}return(i,...u)=>`var(--${n?`${n}-`:""}${i}${r(...u)})`}const Vy=(n,r,l,i=[])=>{let u=n;r.forEach((c,d)=>{d===r.length-1?Array.isArray(u)?u[Number(c)]=l:u&&typeof u=="object"&&(u[c]=l):u&&typeof u=="object"&&(u[c]||(u[c]=i.includes(c)?[]:{}),u=u[c])})},xC=(n,r,l)=>{function i(u,c=[],d=[]){Object.entries(u).forEach(([p,m])=>{(!l||l&&!l([...c,p]))&&m!=null&&(typeof m=="object"&&Object.keys(m).length>0?i(m,[...c,p],Array.isArray(m)?[...d,p]:d):r([...c,p],m,d))})}i(n)},CC=(n,r)=>typeof r=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(i=>n.includes(i))||n[n.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r;function gd(n,r){const{prefix:l,shouldSkipGeneratingVar:i}=r||{},u={},c={},d={};return xC(n,(p,m,h)=>{if((typeof m=="string"||typeof m=="number")&&(!i||!i(p,m))){const y=`--${l?`${l}-`:""}${p.join("-")}`,x=CC(p,m);Object.assign(u,{[y]:x}),Vy(c,p,`var(${y})`,h),Vy(d,p,`var(${y}, ${x})`,h)}},p=>p[0]==="vars"),{css:u,vars:c,varsWithDefaults:d}}function TC(n,r={}){const{getSelector:l=w,disableCssColorScheme:i,colorSchemeSelector:u}=r,{colorSchemes:c={},components:d,defaultColorScheme:p="light",...m}=n,{vars:h,css:y,varsWithDefaults:x}=gd(m,r);let R=x;const M={},{[p]:E,...v}=c;if(Object.entries(v||{}).forEach(([D,$])=>{const{vars:z,css:N,varsWithDefaults:I}=gd($,r);R=gn(R,I),M[D]={css:N,vars:z}}),E){const{css:D,vars:$,varsWithDefaults:z}=gd(E,r);R=gn(R,z),M[p]={css:D,vars:$}}function w(D,$){let z=u;if(u==="class"&&(z=".%s"),u==="data"&&(z="[data-%s]"),u?.startsWith("data-")&&!u.includes("%s")&&(z=`[${u}="%s"]`),D){if(z==="media")return n.defaultColorScheme===D?":root":{[`@media (prefers-color-scheme: ${c[D]?.palette?.mode||D})`]:{":root":$}};if(z)return n.defaultColorScheme===D?`:root, ${z.replace("%s",String(D))}`:z.replace("%s",String(D))}return":root"}return{vars:R,generateThemeVars:()=>{let D={...h};return Object.entries(M).forEach(([,{vars:$}])=>{D=gn(D,$)}),D},generateStyleSheets:()=>{const D=[],$=n.defaultColorScheme||"light";function z(G,K){Object.keys(K).length&&D.push(typeof G=="string"?{[G]:{...K}}:G)}z(l(void 0,{...y}),y);const{[$]:N,...I}=M;if(N){const{css:G}=N,K=c[$]?.palette?.mode,b=!i&&K?{colorScheme:K,...G}:{...G};z(l($,{...b}),b)}return Object.entries(I).forEach(([G,{css:K}])=>{const b=c[G]?.palette?.mode,B=!i&&b?{colorScheme:b,...K}:{...K};z(l(G,{...B}),B)}),D}}}function EC(n){return function(l){return n==="media"?`@media (prefers-color-scheme: ${l})`:n?n.startsWith("data-")&&!n.includes("%s")?`[${n}="${l}"] &`:n==="class"?`.${l} &`:n==="data"?`[data-${l}] &`:`${n.replace("%s",l)} &`:"&"}}function Qe(n,r,l=void 0){const i={};for(const u in n){const c=n[u];let d="",p=!0;for(let m=0;m<c.length;m+=1){const h=c[m];h&&(d+=(p===!0?"":" ")+r(h),p=!1,l&&l[h]&&(d+=" "+l[h]))}i[u]=d}return i}function hu(n,r){return O.isValidElement(n)&&r.indexOf(n.type.muiName??n.type?._payload?.value?.muiName)!==-1}const RC=(n,r)=>n.filter(l=>r.includes(l)),gl=(n,r,l)=>{const i=n.keys[0];Array.isArray(r)?r.forEach((u,c)=>{l((d,p)=>{c<=n.keys.length-1&&(c===0?Object.assign(d,p):d[n.up(n.keys[c])]=p)},u)}):r&&typeof r=="object"?(Object.keys(r).length>n.keys.length?n.keys:RC(n.keys,Object.keys(r))).forEach(c=>{if(n.keys.includes(c)){const d=r[c];d!==void 0&&l((p,m)=>{i===c?Object.assign(p,m):p[n.up(c)]=m},d)}}):(typeof r=="number"||typeof r=="string")&&l((u,c)=>{Object.assign(u,c)},r)};function Tu(n){return`--Grid-${n}Spacing`}function Vu(n){return`--Grid-parent-${n}Spacing`}const Yy="--Grid-columns",il="--Grid-parent-columns",OC=({theme:n,ownerState:r})=>{const l={};return gl(n.breakpoints,r.size,(i,u)=>{let c={};u==="grow"&&(c={flexBasis:0,flexGrow:1,maxWidth:"100%"}),u==="auto"&&(c={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"}),typeof u=="number"&&(c={flexGrow:0,flexBasis:"auto",width:`calc(100% * ${u} / var(${il}) - (var(${il}) - ${u}) * (var(${Vu("column")}) / var(${il})))`}),i(l,c)}),l},MC=({theme:n,ownerState:r})=>{const l={};return gl(n.breakpoints,r.offset,(i,u)=>{let c={};u==="auto"&&(c={marginLeft:"auto"}),typeof u=="number"&&(c={marginLeft:u===0?"0px":`calc(100% * ${u} / var(${il}) + var(${Vu("column")}) * ${u} / var(${il}))`}),i(l,c)}),l},wC=({theme:n,ownerState:r})=>{if(!r.container)return{};const l={[Yy]:12};return gl(n.breakpoints,r.columns,(i,u)=>{const c=u??12;i(l,{[Yy]:c,"> *":{[il]:c}})}),l},AC=({theme:n,ownerState:r})=>{if(!r.container)return{};const l={};return gl(n.breakpoints,r.rowSpacing,(i,u)=>{const c=typeof u=="string"?u:n.spacing?.(u);i(l,{[Tu("row")]:c,"> *":{[Vu("row")]:c}})}),l},zC=({theme:n,ownerState:r})=>{if(!r.container)return{};const l={};return gl(n.breakpoints,r.columnSpacing,(i,u)=>{const c=typeof u=="string"?u:n.spacing?.(u);i(l,{[Tu("column")]:c,"> *":{[Vu("column")]:c}})}),l},DC=({theme:n,ownerState:r})=>{if(!r.container)return{};const l={};return gl(n.breakpoints,r.direction,(i,u)=>{i(l,{flexDirection:u})}),l},$C=({ownerState:n})=>({minWidth:0,boxSizing:"border-box",...n.container&&{display:"flex",flexWrap:"wrap",...n.wrap&&n.wrap!=="wrap"&&{flexWrap:n.wrap},gap:`var(${Tu("row")}) var(${Tu("column")})`}}),jC=n=>{const r=[];return Object.entries(n).forEach(([l,i])=>{i!==!1&&i!==void 0&&r.push(`grid-${l}-${String(i)}`)}),r},kC=(n,r="xs")=>{function l(i){return i===void 0?!1:typeof i=="string"&&!Number.isNaN(Number(i))||typeof i=="number"&&i>0}if(l(n))return[`spacing-${r}-${String(n)}`];if(typeof n=="object"&&!Array.isArray(n)){const i=[];return Object.entries(n).forEach(([u,c])=>{l(c)&&i.push(`spacing-${u}-${String(c)}`)}),i}return[]},NC=n=>n===void 0?[]:typeof n=="object"?Object.entries(n).map(([r,l])=>`direction-${r}-${l}`):[`direction-xs-${String(n)}`];function BC(n,r){n.item!==void 0&&delete n.item,n.zeroMinWidth!==void 0&&delete n.zeroMinWidth,r.keys.forEach(l=>{n[l]!==void 0&&delete n[l]})}const _C=Hu(),LC=V2("div",{name:"MuiGrid",slot:"Root"});function HC(n){return Y2({props:n,name:"MuiGrid",defaultTheme:_C})}function PC(n={}){const{createStyledComponent:r=LC,useThemeProps:l=HC,useTheme:i=Vi,componentName:u="MuiGrid"}=n,c=(h,y)=>{const{container:x,direction:R,spacing:M,wrap:E,size:v}=h,w={root:["root",x&&"container",E!=="wrap"&&`wrap-xs-${String(E)}`,...NC(R),...jC(v),...x?kC(M,y.breakpoints.keys[0]):[]]};return Qe(w,j=>Xe(u,j),{})};function d(h,y,x=()=>!0){const R={};return h===null||(Array.isArray(h)?h.forEach((M,E)=>{M!==null&&x(M)&&y.keys[E]&&(R[y.keys[E]]=M)}):typeof h=="object"?Object.keys(h).forEach(M=>{const E=h[M];E!=null&&x(E)&&(R[M]=E)}):R[y.keys[0]]=h),R}const p=r(wC,zC,AC,OC,DC,$C,MC),m=O.forwardRef(function(y,x){const R=i(),M=l(y),E=rp(M);BC(E,R.breakpoints);const{className:v,children:w,columns:j=12,container:P=!1,component:D="div",direction:$="row",wrap:z="wrap",size:N={},offset:I={},spacing:G=0,rowSpacing:K=G,columnSpacing:b=G,unstable_level:B=0,...V}=E,ae=d(N,R.breakpoints,U=>U!==!1),ee=d(I,R.breakpoints),_=y.columns??(B?void 0:j),T=y.spacing??(B?void 0:G),L=y.rowSpacing??y.spacing??(B?void 0:K),Y=y.columnSpacing??y.spacing??(B?void 0:b),X={...E,level:B,columns:_,container:P,direction:$,wrap:z,spacing:T,rowSpacing:L,columnSpacing:Y,size:ae,offset:ee},A=c(X,R);return S.jsx(p,{ref:x,as:D,ownerState:X,className:Me(A.root,v),...V,children:O.Children.map(w,U=>O.isValidElement(U)&&hu(U,["Grid"])&&P&&U.props.container?O.cloneElement(U,{unstable_level:U.props?.unstable_level??B+1}):U)})});return m.muiName="Grid",m}function A0(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Ai.white,default:Ai.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const UC=A0();function z0(){return{text:{primary:Ai.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Ai.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const Gy=z0();function Xy(n,r,l,i){const u=i.light||i,c=i.dark||i*1.5;n[r]||(n.hasOwnProperty(l)?n[r]=n[l]:r==="light"?n.light=Iu(n.main,u):r==="dark"&&(n.dark=qu(n.main,c)))}function qC(n="light"){return n==="dark"?{main:Zo[200],light:Zo[50],dark:Zo[400]}:{main:Zo[700],light:Zo[400],dark:Zo[800]}}function IC(n="light"){return n==="dark"?{main:Wo[200],light:Wo[50],dark:Wo[400]}:{main:Wo[500],light:Wo[300],dark:Wo[700]}}function VC(n="light"){return n==="dark"?{main:Fo[500],light:Fo[300],dark:Fo[700]}:{main:Fo[700],light:Fo[400],dark:Fo[800]}}function YC(n="light"){return n==="dark"?{main:Jo[400],light:Jo[300],dark:Jo[700]}:{main:Jo[700],light:Jo[500],dark:Jo[900]}}function GC(n="light"){return n==="dark"?{main:el[400],light:el[300],dark:el[700]}:{main:el[800],light:el[500],dark:el[900]}}function XC(n="light"){return n==="dark"?{main:hi[400],light:hi[300],dark:hi[700]}:{main:"#ed6c02",light:hi[500],dark:hi[900]}}function up(n){const{mode:r="light",contrastThreshold:l=3,tonalOffset:i=.2,...u}=n,c=n.primary||qC(r),d=n.secondary||IC(r),p=n.error||VC(r),m=n.info||YC(r),h=n.success||GC(r),y=n.warning||XC(r);function x(v){return Z2(v,Gy.text.primary)>=l?Gy.text.primary:UC.text.primary}const R=({color:v,name:w,mainShade:j=500,lightShade:P=300,darkShade:D=700})=>{if(v={...v},!v.main&&v[j]&&(v.main=v[j]),!v.hasOwnProperty("main"))throw new Error(or(11,w?` (${w})`:"",j));if(typeof v.main!="string")throw new Error(or(12,w?` (${w})`:"",JSON.stringify(v.main)));return Xy(v,"light",P,i),Xy(v,"dark",D,i),v.contrastText||(v.contrastText=x(v.main)),v};let M;return r==="light"?M=A0():r==="dark"&&(M=z0()),gn({common:{...Ai},mode:r,primary:R({color:c,name:"primary"}),secondary:R({color:d,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:R({color:p,name:"error"}),warning:R({color:y,name:"warning"}),info:R({color:m,name:"info"}),success:R({color:h,name:"success"}),grey:NS,contrastThreshold:l,getContrastText:x,augmentColor:R,tonalOffset:i,...M},u)}function KC(n){const r={};return Object.entries(n).forEach(i=>{const[u,c]=i;typeof c=="object"&&(r[u]=`${c.fontStyle?`${c.fontStyle} `:""}${c.fontVariant?`${c.fontVariant} `:""}${c.fontWeight?`${c.fontWeight} `:""}${c.fontStretch?`${c.fontStretch} `:""}${c.fontSize||""}${c.lineHeight?`/${c.lineHeight} `:""}${c.fontFamily||""}`)}),r}function QC(n,r){return{toolbar:{minHeight:56,[n.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[n.up("sm")]:{minHeight:64}},...r}}function FC(n){return Math.round(n*1e5)/1e5}const Ky={textTransform:"uppercase"},Qy='"Roboto", "Helvetica", "Arial", sans-serif';function D0(n,r){const{fontFamily:l=Qy,fontSize:i=14,fontWeightLight:u=300,fontWeightRegular:c=400,fontWeightMedium:d=500,fontWeightBold:p=700,htmlFontSize:m=16,allVariants:h,pxToRem:y,...x}=typeof r=="function"?r(n):r,R=i/14,M=y||(w=>`${w/m*R}rem`),E=(w,j,P,D,$)=>({fontFamily:l,fontWeight:w,fontSize:M(j),lineHeight:P,...l===Qy?{letterSpacing:`${FC(D/j)}em`}:{},...$,...h}),v={h1:E(u,96,1.167,-1.5),h2:E(u,60,1.2,-.5),h3:E(c,48,1.167,0),h4:E(c,34,1.235,.25),h5:E(c,24,1.334,0),h6:E(d,20,1.6,.15),subtitle1:E(c,16,1.75,.15),subtitle2:E(d,14,1.57,.1),body1:E(c,16,1.5,.15),body2:E(c,14,1.43,.15),button:E(d,14,1.75,.4,Ky),caption:E(c,12,1.66,.4),overline:E(c,12,2.66,1,Ky),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return gn({htmlFontSize:m,pxToRem:M,fontFamily:l,fontSize:i,fontWeightLight:u,fontWeightRegular:c,fontWeightMedium:d,fontWeightBold:p,...v},x,{clone:!1})}const WC=.2,ZC=.14,JC=.12;function jt(...n){return[`${n[0]}px ${n[1]}px ${n[2]}px ${n[3]}px rgba(0,0,0,${WC})`,`${n[4]}px ${n[5]}px ${n[6]}px ${n[7]}px rgba(0,0,0,${ZC})`,`${n[8]}px ${n[9]}px ${n[10]}px ${n[11]}px rgba(0,0,0,${JC})`].join(",")}const eT=["none",jt(0,2,1,-1,0,1,1,0,0,1,3,0),jt(0,3,1,-2,0,2,2,0,0,1,5,0),jt(0,3,3,-2,0,3,4,0,0,1,8,0),jt(0,2,4,-1,0,4,5,0,0,1,10,0),jt(0,3,5,-1,0,5,8,0,0,1,14,0),jt(0,3,5,-1,0,6,10,0,0,1,18,0),jt(0,4,5,-2,0,7,10,1,0,2,16,1),jt(0,5,5,-3,0,8,10,1,0,3,14,2),jt(0,5,6,-3,0,9,12,1,0,3,16,2),jt(0,6,6,-3,0,10,14,1,0,4,18,3),jt(0,6,7,-4,0,11,15,1,0,4,20,3),jt(0,7,8,-4,0,12,17,2,0,5,22,4),jt(0,7,8,-4,0,13,19,2,0,5,24,4),jt(0,7,9,-4,0,14,21,2,0,5,26,4),jt(0,8,9,-5,0,15,22,2,0,6,28,5),jt(0,8,10,-5,0,16,24,2,0,6,30,5),jt(0,8,11,-5,0,17,26,2,0,6,32,5),jt(0,9,11,-5,0,18,28,2,0,7,34,6),jt(0,9,12,-6,0,19,29,2,0,7,36,6),jt(0,10,13,-6,0,20,31,3,0,8,38,7),jt(0,10,13,-6,0,21,33,3,0,8,40,7),jt(0,10,14,-6,0,22,35,3,0,8,42,7),jt(0,11,14,-7,0,23,36,3,0,9,44,8),jt(0,11,15,-7,0,24,38,3,0,9,46,8)],tT={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},nT={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Fy(n){return`${Math.round(n)}ms`}function aT(n){if(!n)return 0;const r=n/36;return Math.min(Math.round((4+15*r**.25+r/5)*10),3e3)}function rT(n){const r={...tT,...n.easing},l={...nT,...n.duration};return{getAutoHeightDuration:aT,create:(u=["all"],c={})=>{const{duration:d=l.standard,easing:p=r.easeInOut,delay:m=0,...h}=c;return(Array.isArray(u)?u:[u]).map(y=>`${y} ${typeof d=="string"?d:Fy(d)} ${p} ${typeof m=="string"?m:Fy(m)}`).join(",")},...n,easing:r,duration:l}}const oT={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function lT(n){return Na(n)||typeof n>"u"||typeof n=="string"||typeof n=="boolean"||typeof n=="number"||Array.isArray(n)}function $0(n={}){const r={...n};function l(i){const u=Object.entries(i);for(let c=0;c<u.length;c++){const[d,p]=u[c];!lT(p)||d.startsWith("unstable_")?delete i[d]:Na(p)&&(i[d]={...p},l(i[d]))}}return l(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(r,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Md(n={},...r){const{breakpoints:l,mixins:i={},spacing:u,palette:c={},transitions:d={},typography:p={},shape:m,...h}=n;if(n.vars&&n.generateThemeVars===void 0)throw new Error(or(20));const y=up(c),x=Hu(n);let R=gn(x,{mixins:QC(x.breakpoints,i),palette:y,shadows:eT.slice(),typography:D0(y,p),transitions:rT(d),zIndex:{...oT}});return R=gn(R,h),R=r.reduce((M,E)=>gn(M,E),R),R.unstable_sxConfig={...Ii,...h?.unstable_sxConfig},R.unstable_sx=function(E){return Br({sx:E,theme:this})},R.toRuntimeSource=$0,R}function wd(n){let r;return n<1?r=5.11916*n**2:r=4.5*Math.log(n+1)+2,Math.round(r*10)/1e3}const iT=[...Array(25)].map((n,r)=>{if(r===0)return"none";const l=wd(r);return`linear-gradient(rgba(255 255 255 / ${l}), rgba(255 255 255 / ${l}))`});function j0(n){return{inputPlaceholder:n==="dark"?.5:.42,inputUnderline:n==="dark"?.7:.42,switchTrackDisabled:n==="dark"?.2:.12,switchTrack:n==="dark"?.3:.38}}function k0(n){return n==="dark"?iT:[]}function sT(n){const{palette:r={mode:"light"},opacity:l,overlays:i,...u}=n,c=up(r);return{palette:c,opacity:{...j0(c.mode),...l},overlays:i||k0(c.mode),...u}}function uT(n){return!!n[0].match(/(cssVarPrefix|colorSchemeSelector|modularCssLayers|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!n[0].match(/sxConfig$/)||n[0]==="palette"&&!!n[1]?.match(/(mode|contrastThreshold|tonalOffset)/)}const cT=n=>[...[...Array(25)].map((r,l)=>`--${n?`${n}-`:""}overlays-${l}`),`--${n?`${n}-`:""}palette-AppBar-darkBg`,`--${n?`${n}-`:""}palette-AppBar-darkColor`],fT=n=>(r,l)=>{const i=n.rootSelector||":root",u=n.colorSchemeSelector;let c=u;if(u==="class"&&(c=".%s"),u==="data"&&(c="[data-%s]"),u?.startsWith("data-")&&!u.includes("%s")&&(c=`[${u}="%s"]`),n.defaultColorScheme===r){if(r==="dark"){const d={};return cT(n.cssVarPrefix).forEach(p=>{d[p]=l[p],delete l[p]}),c==="media"?{[i]:l,"@media (prefers-color-scheme: dark)":{[i]:d}}:c?{[c.replace("%s",r)]:d,[`${i}, ${c.replace("%s",r)}`]:l}:{[i]:{...l,...d}}}if(c&&c!=="media")return`${i}, ${c.replace("%s",String(r))}`}else if(r){if(c==="media")return{[`@media (prefers-color-scheme: ${String(r)})`]:{[i]:l}};if(c)return c.replace("%s",String(r))}return i};function dT(n,r){r.forEach(l=>{n[l]||(n[l]={})})}function oe(n,r,l){!n[r]&&l&&(n[r]=l)}function xi(n){return typeof n!="string"||!n.startsWith("hsl")?n:C0(n)}function ar(n,r){`${r}Channel`in n||(n[`${r}Channel`]=Si(xi(n[r])))}function pT(n){return typeof n=="number"?`${n}px`:typeof n=="string"||typeof n=="function"||Array.isArray(n)?n:"8px"}const za=n=>{try{return n()}catch{}},mT=(n="mui")=>SC(n);function yd(n,r,l,i){if(!r)return;r=r===!0?{}:r;const u=i==="dark"?"dark":"light";if(!l){n[i]=sT({...r,palette:{mode:u,...r?.palette}});return}const{palette:c,...d}=Md({...l,palette:{mode:u,...r?.palette}});return n[i]={...r,palette:c,opacity:{...j0(u),...r?.opacity},overlays:r?.overlays||k0(u)},d}function hT(n={},...r){const{colorSchemes:l={light:!0},defaultColorScheme:i,disableCssColorScheme:u=!1,cssVarPrefix:c="mui",shouldSkipGeneratingVar:d=uT,colorSchemeSelector:p=l.light&&l.dark?"media":void 0,rootSelector:m=":root",...h}=n,y=Object.keys(l)[0],x=i||(l.light&&y!=="light"?"light":y),R=mT(c),{[x]:M,light:E,dark:v,...w}=l,j={...w};let P=M;if((x==="dark"&&!("dark"in l)||x==="light"&&!("light"in l))&&(P=!0),!P)throw new Error(or(21,x));const D=yd(j,P,h,x);E&&!j.light&&yd(j,E,void 0,"light"),v&&!j.dark&&yd(j,v,void 0,"dark");let $={defaultColorScheme:x,...D,cssVarPrefix:c,colorSchemeSelector:p,rootSelector:m,getCssVar:R,colorSchemes:j,font:{...KC(D.typography),...D.font},spacing:pT(h.spacing)};Object.keys($.colorSchemes).forEach(K=>{const b=$.colorSchemes[K].palette,B=V=>{const ae=V.split("-"),ee=ae[1],_=ae[2];return R(V,b[ee][_])};if(b.mode==="light"&&(oe(b.common,"background","#fff"),oe(b.common,"onBackground","#000")),b.mode==="dark"&&(oe(b.common,"background","#000"),oe(b.common,"onBackground","#fff")),dT(b,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),b.mode==="light"){oe(b.Alert,"errorColor",Et(b.error.light,.6)),oe(b.Alert,"infoColor",Et(b.info.light,.6)),oe(b.Alert,"successColor",Et(b.success.light,.6)),oe(b.Alert,"warningColor",Et(b.warning.light,.6)),oe(b.Alert,"errorFilledBg",B("palette-error-main")),oe(b.Alert,"infoFilledBg",B("palette-info-main")),oe(b.Alert,"successFilledBg",B("palette-success-main")),oe(b.Alert,"warningFilledBg",B("palette-warning-main")),oe(b.Alert,"errorFilledColor",za(()=>b.getContrastText(b.error.main))),oe(b.Alert,"infoFilledColor",za(()=>b.getContrastText(b.info.main))),oe(b.Alert,"successFilledColor",za(()=>b.getContrastText(b.success.main))),oe(b.Alert,"warningFilledColor",za(()=>b.getContrastText(b.warning.main))),oe(b.Alert,"errorStandardBg",Rt(b.error.light,.9)),oe(b.Alert,"infoStandardBg",Rt(b.info.light,.9)),oe(b.Alert,"successStandardBg",Rt(b.success.light,.9)),oe(b.Alert,"warningStandardBg",Rt(b.warning.light,.9)),oe(b.Alert,"errorIconColor",B("palette-error-main")),oe(b.Alert,"infoIconColor",B("palette-info-main")),oe(b.Alert,"successIconColor",B("palette-success-main")),oe(b.Alert,"warningIconColor",B("palette-warning-main")),oe(b.AppBar,"defaultBg",B("palette-grey-100")),oe(b.Avatar,"defaultBg",B("palette-grey-400")),oe(b.Button,"inheritContainedBg",B("palette-grey-300")),oe(b.Button,"inheritContainedHoverBg",B("palette-grey-A100")),oe(b.Chip,"defaultBorder",B("palette-grey-400")),oe(b.Chip,"defaultAvatarColor",B("palette-grey-700")),oe(b.Chip,"defaultIconColor",B("palette-grey-700")),oe(b.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),oe(b.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),oe(b.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),oe(b.LinearProgress,"primaryBg",Rt(b.primary.main,.62)),oe(b.LinearProgress,"secondaryBg",Rt(b.secondary.main,.62)),oe(b.LinearProgress,"errorBg",Rt(b.error.main,.62)),oe(b.LinearProgress,"infoBg",Rt(b.info.main,.62)),oe(b.LinearProgress,"successBg",Rt(b.success.main,.62)),oe(b.LinearProgress,"warningBg",Rt(b.warning.main,.62)),oe(b.Skeleton,"bg",`rgba(${B("palette-text-primaryChannel")} / 0.11)`),oe(b.Slider,"primaryTrack",Rt(b.primary.main,.62)),oe(b.Slider,"secondaryTrack",Rt(b.secondary.main,.62)),oe(b.Slider,"errorTrack",Rt(b.error.main,.62)),oe(b.Slider,"infoTrack",Rt(b.info.main,.62)),oe(b.Slider,"successTrack",Rt(b.success.main,.62)),oe(b.Slider,"warningTrack",Rt(b.warning.main,.62));const V=ru(b.background.default,.8);oe(b.SnackbarContent,"bg",V),oe(b.SnackbarContent,"color",za(()=>b.getContrastText(V))),oe(b.SpeedDialAction,"fabHoverBg",ru(b.background.paper,.15)),oe(b.StepConnector,"border",B("palette-grey-400")),oe(b.StepContent,"border",B("palette-grey-400")),oe(b.Switch,"defaultColor",B("palette-common-white")),oe(b.Switch,"defaultDisabledColor",B("palette-grey-100")),oe(b.Switch,"primaryDisabledColor",Rt(b.primary.main,.62)),oe(b.Switch,"secondaryDisabledColor",Rt(b.secondary.main,.62)),oe(b.Switch,"errorDisabledColor",Rt(b.error.main,.62)),oe(b.Switch,"infoDisabledColor",Rt(b.info.main,.62)),oe(b.Switch,"successDisabledColor",Rt(b.success.main,.62)),oe(b.Switch,"warningDisabledColor",Rt(b.warning.main,.62)),oe(b.TableCell,"border",Rt(au(b.divider,1),.88)),oe(b.Tooltip,"bg",au(b.grey[700],.92))}if(b.mode==="dark"){oe(b.Alert,"errorColor",Rt(b.error.light,.6)),oe(b.Alert,"infoColor",Rt(b.info.light,.6)),oe(b.Alert,"successColor",Rt(b.success.light,.6)),oe(b.Alert,"warningColor",Rt(b.warning.light,.6)),oe(b.Alert,"errorFilledBg",B("palette-error-dark")),oe(b.Alert,"infoFilledBg",B("palette-info-dark")),oe(b.Alert,"successFilledBg",B("palette-success-dark")),oe(b.Alert,"warningFilledBg",B("palette-warning-dark")),oe(b.Alert,"errorFilledColor",za(()=>b.getContrastText(b.error.dark))),oe(b.Alert,"infoFilledColor",za(()=>b.getContrastText(b.info.dark))),oe(b.Alert,"successFilledColor",za(()=>b.getContrastText(b.success.dark))),oe(b.Alert,"warningFilledColor",za(()=>b.getContrastText(b.warning.dark))),oe(b.Alert,"errorStandardBg",Et(b.error.light,.9)),oe(b.Alert,"infoStandardBg",Et(b.info.light,.9)),oe(b.Alert,"successStandardBg",Et(b.success.light,.9)),oe(b.Alert,"warningStandardBg",Et(b.warning.light,.9)),oe(b.Alert,"errorIconColor",B("palette-error-main")),oe(b.Alert,"infoIconColor",B("palette-info-main")),oe(b.Alert,"successIconColor",B("palette-success-main")),oe(b.Alert,"warningIconColor",B("palette-warning-main")),oe(b.AppBar,"defaultBg",B("palette-grey-900")),oe(b.AppBar,"darkBg",B("palette-background-paper")),oe(b.AppBar,"darkColor",B("palette-text-primary")),oe(b.Avatar,"defaultBg",B("palette-grey-600")),oe(b.Button,"inheritContainedBg",B("palette-grey-800")),oe(b.Button,"inheritContainedHoverBg",B("palette-grey-700")),oe(b.Chip,"defaultBorder",B("palette-grey-700")),oe(b.Chip,"defaultAvatarColor",B("palette-grey-300")),oe(b.Chip,"defaultIconColor",B("palette-grey-300")),oe(b.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),oe(b.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),oe(b.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),oe(b.LinearProgress,"primaryBg",Et(b.primary.main,.5)),oe(b.LinearProgress,"secondaryBg",Et(b.secondary.main,.5)),oe(b.LinearProgress,"errorBg",Et(b.error.main,.5)),oe(b.LinearProgress,"infoBg",Et(b.info.main,.5)),oe(b.LinearProgress,"successBg",Et(b.success.main,.5)),oe(b.LinearProgress,"warningBg",Et(b.warning.main,.5)),oe(b.Skeleton,"bg",`rgba(${B("palette-text-primaryChannel")} / 0.13)`),oe(b.Slider,"primaryTrack",Et(b.primary.main,.5)),oe(b.Slider,"secondaryTrack",Et(b.secondary.main,.5)),oe(b.Slider,"errorTrack",Et(b.error.main,.5)),oe(b.Slider,"infoTrack",Et(b.info.main,.5)),oe(b.Slider,"successTrack",Et(b.success.main,.5)),oe(b.Slider,"warningTrack",Et(b.warning.main,.5));const V=ru(b.background.default,.98);oe(b.SnackbarContent,"bg",V),oe(b.SnackbarContent,"color",za(()=>b.getContrastText(V))),oe(b.SpeedDialAction,"fabHoverBg",ru(b.background.paper,.15)),oe(b.StepConnector,"border",B("palette-grey-600")),oe(b.StepContent,"border",B("palette-grey-600")),oe(b.Switch,"defaultColor",B("palette-grey-300")),oe(b.Switch,"defaultDisabledColor",B("palette-grey-600")),oe(b.Switch,"primaryDisabledColor",Et(b.primary.main,.55)),oe(b.Switch,"secondaryDisabledColor",Et(b.secondary.main,.55)),oe(b.Switch,"errorDisabledColor",Et(b.error.main,.55)),oe(b.Switch,"infoDisabledColor",Et(b.info.main,.55)),oe(b.Switch,"successDisabledColor",Et(b.success.main,.55)),oe(b.Switch,"warningDisabledColor",Et(b.warning.main,.55)),oe(b.TableCell,"border",Et(au(b.divider,1),.68)),oe(b.Tooltip,"bg",au(b.grey[700],.92))}ar(b.background,"default"),ar(b.background,"paper"),ar(b.common,"background"),ar(b.common,"onBackground"),ar(b,"divider"),Object.keys(b).forEach(V=>{const ae=b[V];V!=="tonalOffset"&&ae&&typeof ae=="object"&&(ae.main&&oe(b[V],"mainChannel",Si(xi(ae.main))),ae.light&&oe(b[V],"lightChannel",Si(xi(ae.light))),ae.dark&&oe(b[V],"darkChannel",Si(xi(ae.dark))),ae.contrastText&&oe(b[V],"contrastTextChannel",Si(xi(ae.contrastText))),V==="text"&&(ar(b[V],"primary"),ar(b[V],"secondary")),V==="action"&&(ae.active&&ar(b[V],"active"),ae.selected&&ar(b[V],"selected")))})}),$=r.reduce((K,b)=>gn(K,b),$);const z={prefix:c,disableCssColorScheme:u,shouldSkipGeneratingVar:d,getSelector:fT($)},{vars:N,generateThemeVars:I,generateStyleSheets:G}=TC($,z);return $.vars=N,Object.entries($.colorSchemes[$.defaultColorScheme]).forEach(([K,b])=>{$[K]=b}),$.generateThemeVars=I,$.generateStyleSheets=G,$.generateSpacing=function(){return d0(h.spacing,np(this))},$.getColorSchemeSelector=EC(p),$.spacing=$.generateSpacing(),$.shouldSkipGeneratingVar=d,$.unstable_sxConfig={...Ii,...h?.unstable_sxConfig},$.unstable_sx=function(b){return Br({sx:b,theme:this})},$.toRuntimeSource=$0,$}function Wy(n,r,l){n.colorSchemes&&l&&(n.colorSchemes[r]={...l!==!0&&l,palette:up({...l===!0?{}:l.palette,mode:r})})}function Yu(n={},...r){const{palette:l,cssVariables:i=!1,colorSchemes:u=l?void 0:{light:!0},defaultColorScheme:c=l?.mode,...d}=n,p=c||"light",m=u?.[p],h={...u,...l?{[p]:{...typeof m!="boolean"&&m,palette:l}}:void 0};if(i===!1){if(!("colorSchemes"in n))return Md(n,...r);let y=l;"palette"in n||h[p]&&(h[p]!==!0?y=h[p].palette:p==="dark"&&(y={mode:"dark"}));const x=Md({...n,palette:y},...r);return x.defaultColorScheme=p,x.colorSchemes=h,x.palette.mode==="light"&&(x.colorSchemes.light={...h.light!==!0&&h.light,palette:x.palette},Wy(x,"dark",h.dark)),x.palette.mode==="dark"&&(x.colorSchemes.dark={...h.dark!==!0&&h.dark,palette:x.palette},Wy(x,"light",h.light)),x}return!l&&!("light"in h)&&p==="light"&&(h.light=!0),hT({...d,colorSchemes:h,defaultColorScheme:p,...typeof i!="boolean"&&i},...r)}const cp=Yu();function Gu(){const n=Vi(cp);return n[Ta]||n}function N0(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}const Yn=n=>N0(n)&&n!=="classes",pe=v0({themeId:Ta,defaultTheme:cp,rootShouldForwardProp:Yn});function gT({theme:n,...r}){const l=Ta in n?n[Ta]:void 0;return S.jsx(M0,{...r,themeId:l?Ta:void 0,theme:l||n})}const ou={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:yT}=bC({themeId:Ta,theme:()=>Yu({cssVariables:!0}),colorSchemeStorageKey:ou.colorSchemeStorageKey,modeStorageKey:ou.modeStorageKey,defaultColorScheme:{light:ou.defaultLightColorScheme,dark:ou.defaultDarkColorScheme},resolveTheme:n=>{const r={...n,typography:D0(n.palette,n.typography)};return r.unstable_sx=function(i){return Br({sx:i,theme:this})},r}}),vT=yT;function bT({theme:n,...r}){const l=O.useMemo(()=>{if(typeof n=="function")return n;const i=Ta in n?n[Ta]:n;return"colorSchemes"in i?null:"vars"in i?n:{...n,vars:null}},[n]);return l?S.jsx(gT,{theme:l,...r}):S.jsx(vT,{theme:n,...r})}function Zy(...n){return n.reduce((r,l)=>l==null?r:function(...u){r.apply(this,u),l.apply(this,u)},()=>{})}function ST(n){return S.jsx(p0,{...n,defaultTheme:cp,themeId:Ta})}function fp(n){return function(l){return S.jsx(ST,{styles:typeof n=="function"?i=>n({theme:i,...l}):n})}}function xT(){return rp}const tt=fC;function Je(n){return iC(n)}function CT(n){return Xe("MuiSvgIcon",n)}Ke("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const TT=n=>{const{color:r,fontSize:l,classes:i}=n,u={root:["root",r!=="inherit"&&`color${de(r)}`,`fontSize${de(l)}`]};return Qe(u,CT,i)},ET=pe("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.color!=="inherit"&&r[`color${de(l.color)}`],r[`fontSize${de(l.fontSize)}`]]}})(tt(({theme:n})=>({userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:n.transitions?.create?.("fill",{duration:(n.vars??n).transitions?.duration?.shorter}),variants:[{props:r=>!r.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:n.typography?.pxToRem?.(20)||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:n.typography?.pxToRem?.(24)||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:n.typography?.pxToRem?.(35)||"2.1875rem"}},...Object.entries((n.vars??n).palette).filter(([,r])=>r&&r.main).map(([r])=>({props:{color:r},style:{color:(n.vars??n).palette?.[r]?.main}})),{props:{color:"action"},style:{color:(n.vars??n).palette?.action?.active}},{props:{color:"disabled"},style:{color:(n.vars??n).palette?.action?.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}))),Ad=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiSvgIcon"}),{children:u,className:c,color:d="inherit",component:p="svg",fontSize:m="medium",htmlColor:h,inheritViewBox:y=!1,titleAccess:x,viewBox:R="0 0 24 24",...M}=i,E=O.isValidElement(u)&&u.type==="svg",v={...i,color:d,component:p,fontSize:m,instanceFontSize:r.fontSize,inheritViewBox:y,viewBox:R,hasSvgAsChild:E},w={};y||(w.viewBox=R);const j=TT(v);return S.jsxs(ET,{as:p,className:Me(j.root,c),focusable:"false",color:h,"aria-hidden":x?void 0:!0,role:x?"img":void 0,ref:l,...w,...M,...E&&u.props,ownerState:v,children:[E?u.props.children:u,x?S.jsx("title",{children:x}):null]})});Ad.muiName="SvgIcon";function fn(n,r){function l(i,u){return S.jsx(Ad,{"data-testid":void 0,ref:u,...i,children:n})}return l.muiName=Ad.muiName,O.memo(O.forwardRef(l))}function B0(n,r=166){let l;function i(...u){const c=()=>{n.apply(this,u)};clearTimeout(l),l=setTimeout(c,r)}return i.clear=()=>{clearTimeout(l)},i}function In(n){return n&&n.ownerDocument||document}function ir(n){return In(n).defaultView||window}function zd(n,r){typeof n=="function"?n(r):n&&(n.current=r)}function sl(n){const{controlled:r,default:l,name:i,state:u="value"}=n,{current:c}=O.useRef(r!==void 0),[d,p]=O.useState(l),m=c?r:d,h=O.useCallback(y=>{c||p(y)},[]);return[m,h]}function Ca(n){const r=O.useRef(n);return fa(()=>{r.current=n}),O.useRef((...l)=>(0,r.current)(...l)).current}function cn(...n){const r=O.useRef(void 0),l=O.useCallback(i=>{const u=n.map(c=>{if(c==null)return null;if(typeof c=="function"){const d=c,p=d(i);return typeof p=="function"?p:()=>{d(null)}}return c.current=i,()=>{c.current=null}});return()=>{u.forEach(c=>c?.())}},n);return O.useMemo(()=>n.every(i=>i==null)?null:i=>{r.current&&(r.current(),r.current=void 0),i!=null&&(r.current=l(i))},n)}function RT(n,r){const l=n.charCodeAt(2);return n[0]==="o"&&n[1]==="n"&&l>=65&&l<=90&&typeof r=="function"}function _0(n,r){if(!n)return r;function l(d,p){const m={};return Object.keys(p).forEach(h=>{RT(h,p[h])&&typeof d[h]=="function"&&(m[h]=(...y)=>{d[h](...y),p[h](...y)})}),m}if(typeof n=="function"||typeof r=="function")return d=>{const p=typeof r=="function"?r(d):r,m=typeof n=="function"?n({...d,...p}):n,h=Me(d?.className,p?.className,m?.className),y=l(m,p);return{...p,...m,...y,...!!h&&{className:h},...p?.style&&m?.style&&{style:{...p.style,...m.style}},...p?.sx&&m?.sx&&{sx:[...Array.isArray(p.sx)?p.sx:[p.sx],...Array.isArray(m.sx)?m.sx:[m.sx]]}}};const i=r,u=l(n,i),c=Me(i?.className,n?.className);return{...r,...n,...u,...!!c&&{className:c},...i?.style&&n?.style&&{style:{...i.style,...n.style}},...i?.sx&&n?.sx&&{sx:[...Array.isArray(i.sx)?i.sx:[i.sx],...Array.isArray(n.sx)?n.sx:[n.sx]]}}}function L0(n,r){if(n==null)return{};var l={};for(var i in n)if({}.hasOwnProperty.call(n,i)){if(r.indexOf(i)!==-1)continue;l[i]=n[i]}return l}function Dd(n,r){return Dd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(l,i){return l.__proto__=i,l},Dd(n,r)}function H0(n,r){n.prototype=Object.create(r.prototype),n.prototype.constructor=n,Dd(n,r)}var P0=Xv();const lu=Vd(P0),Jy={disabled:!1},Eu=Ba.createContext(null);var OT=function(r){return r.scrollTop},Ci="unmounted",oo="exited",lo="entering",al="entered",$d="exiting",Pa=function(n){H0(r,n);function r(i,u){var c;c=n.call(this,i,u)||this;var d=u,p=d&&!d.isMounting?i.enter:i.appear,m;return c.appearStatus=null,i.in?p?(m=oo,c.appearStatus=lo):m=al:i.unmountOnExit||i.mountOnEnter?m=Ci:m=oo,c.state={status:m},c.nextCallback=null,c}r.getDerivedStateFromProps=function(u,c){var d=u.in;return d&&c.status===Ci?{status:oo}:null};var l=r.prototype;return l.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},l.componentDidUpdate=function(u){var c=null;if(u!==this.props){var d=this.state.status;this.props.in?d!==lo&&d!==al&&(c=lo):(d===lo||d===al)&&(c=$d)}this.updateStatus(!1,c)},l.componentWillUnmount=function(){this.cancelNextCallback()},l.getTimeouts=function(){var u=this.props.timeout,c,d,p;return c=d=p=u,u!=null&&typeof u!="number"&&(c=u.exit,d=u.enter,p=u.appear!==void 0?u.appear:d),{exit:c,enter:d,appear:p}},l.updateStatus=function(u,c){if(u===void 0&&(u=!1),c!==null)if(this.cancelNextCallback(),c===lo){if(this.props.unmountOnExit||this.props.mountOnEnter){var d=this.props.nodeRef?this.props.nodeRef.current:lu.findDOMNode(this);d&&OT(d)}this.performEnter(u)}else this.performExit();else this.props.unmountOnExit&&this.state.status===oo&&this.setState({status:Ci})},l.performEnter=function(u){var c=this,d=this.props.enter,p=this.context?this.context.isMounting:u,m=this.props.nodeRef?[p]:[lu.findDOMNode(this),p],h=m[0],y=m[1],x=this.getTimeouts(),R=p?x.appear:x.enter;if(!u&&!d||Jy.disabled){this.safeSetState({status:al},function(){c.props.onEntered(h)});return}this.props.onEnter(h,y),this.safeSetState({status:lo},function(){c.props.onEntering(h,y),c.onTransitionEnd(R,function(){c.safeSetState({status:al},function(){c.props.onEntered(h,y)})})})},l.performExit=function(){var u=this,c=this.props.exit,d=this.getTimeouts(),p=this.props.nodeRef?void 0:lu.findDOMNode(this);if(!c||Jy.disabled){this.safeSetState({status:oo},function(){u.props.onExited(p)});return}this.props.onExit(p),this.safeSetState({status:$d},function(){u.props.onExiting(p),u.onTransitionEnd(d.exit,function(){u.safeSetState({status:oo},function(){u.props.onExited(p)})})})},l.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},l.safeSetState=function(u,c){c=this.setNextCallback(c),this.setState(u,c)},l.setNextCallback=function(u){var c=this,d=!0;return this.nextCallback=function(p){d&&(d=!1,c.nextCallback=null,u(p))},this.nextCallback.cancel=function(){d=!1},this.nextCallback},l.onTransitionEnd=function(u,c){this.setNextCallback(c);var d=this.props.nodeRef?this.props.nodeRef.current:lu.findDOMNode(this),p=u==null&&!this.props.addEndListener;if(!d||p){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var m=this.props.nodeRef?[this.nextCallback]:[d,this.nextCallback],h=m[0],y=m[1];this.props.addEndListener(h,y)}u!=null&&setTimeout(this.nextCallback,u)},l.render=function(){var u=this.state.status;if(u===Ci)return null;var c=this.props,d=c.children;c.in,c.mountOnEnter,c.unmountOnExit,c.appear,c.enter,c.exit,c.timeout,c.addEndListener,c.onEnter,c.onEntering,c.onEntered,c.onExit,c.onExiting,c.onExited,c.nodeRef;var p=L0(c,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return Ba.createElement(Eu.Provider,{value:null},typeof d=="function"?d(u,p):Ba.cloneElement(Ba.Children.only(d),p))},r}(Ba.Component);Pa.contextType=Eu;Pa.propTypes={};function tl(){}Pa.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:tl,onEntering:tl,onEntered:tl,onExit:tl,onExiting:tl,onExited:tl};Pa.UNMOUNTED=Ci;Pa.EXITED=oo;Pa.ENTERING=lo;Pa.ENTERED=al;Pa.EXITING=$d;function MT(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function dp(n,r){var l=function(c){return r&&O.isValidElement(c)?r(c):c},i=Object.create(null);return n&&O.Children.map(n,function(u){return u}).forEach(function(u){i[u.key]=l(u)}),i}function wT(n,r){n=n||{},r=r||{};function l(y){return y in r?r[y]:n[y]}var i=Object.create(null),u=[];for(var c in n)c in r?u.length&&(i[c]=u,u=[]):u.push(c);var d,p={};for(var m in r){if(i[m])for(d=0;d<i[m].length;d++){var h=i[m][d];p[i[m][d]]=l(h)}p[m]=l(m)}for(d=0;d<u.length;d++)p[u[d]]=l(u[d]);return p}function so(n,r,l){return l[r]!=null?l[r]:n.props[r]}function AT(n,r){return dp(n.children,function(l){return O.cloneElement(l,{onExited:r.bind(null,l),in:!0,appear:so(l,"appear",n),enter:so(l,"enter",n),exit:so(l,"exit",n)})})}function zT(n,r,l){var i=dp(n.children),u=wT(r,i);return Object.keys(u).forEach(function(c){var d=u[c];if(O.isValidElement(d)){var p=c in r,m=c in i,h=r[c],y=O.isValidElement(h)&&!h.props.in;m&&(!p||y)?u[c]=O.cloneElement(d,{onExited:l.bind(null,d),in:!0,exit:so(d,"exit",n),enter:so(d,"enter",n)}):!m&&p&&!y?u[c]=O.cloneElement(d,{in:!1}):m&&p&&O.isValidElement(h)&&(u[c]=O.cloneElement(d,{onExited:l.bind(null,d),in:h.props.in,exit:so(d,"exit",n),enter:so(d,"enter",n)}))}}),u}var DT=Object.values||function(n){return Object.keys(n).map(function(r){return n[r]})},$T={component:"div",childFactory:function(r){return r}},pp=function(n){H0(r,n);function r(i,u){var c;c=n.call(this,i,u)||this;var d=c.handleExited.bind(MT(c));return c.state={contextValue:{isMounting:!0},handleExited:d,firstRender:!0},c}var l=r.prototype;return l.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},l.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(u,c){var d=c.children,p=c.handleExited,m=c.firstRender;return{children:m?AT(u,p):zT(u,d,p),firstRender:!1}},l.handleExited=function(u,c){var d=dp(this.props.children);u.key in d||(u.props.onExited&&u.props.onExited(c),this.mounted&&this.setState(function(p){var m=Su({},p.children);return delete m[u.key],{children:m}}))},l.render=function(){var u=this.props,c=u.component,d=u.childFactory,p=L0(u,["component","childFactory"]),m=this.state.contextValue,h=DT(this.state.children).map(d);return delete p.appear,delete p.enter,delete p.exit,c===null?Ba.createElement(Eu.Provider,{value:m},h):Ba.createElement(Eu.Provider,{value:m},Ba.createElement(c,p,h))},r}(Ba.Component);pp.propTypes={};pp.defaultProps=$T;const ev={};function U0(n,r){const l=O.useRef(ev);return l.current===ev&&(l.current=n(r)),l}const jT=[];function kT(n){O.useEffect(n,jT)}class mp{static create(){return new mp}currentId=null;start(r,l){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,l()},r)}clear=()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)};disposeEffect=()=>this.clear}function q0(){const n=U0(mp.create).current;return kT(n.disposeEffect),n}const I0=n=>n.scrollTop;function Ru(n,r){const{timeout:l,easing:i,style:u={}}=n;return{duration:u.transitionDuration??(typeof l=="number"?l:l[r.mode]||0),easing:u.transitionTimingFunction??(typeof i=="object"?i[r.mode]:i),delay:u.transitionDelay}}function NT(n){return Xe("MuiPaper",n)}Ke("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const BT=n=>{const{square:r,elevation:l,variant:i,classes:u}=n,c={root:["root",i,!r&&"rounded",i==="elevation"&&`elevation${l}`]};return Qe(c,NT,u)},_T=pe("div",{name:"MuiPaper",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[l.variant],!l.square&&r.rounded,l.variant==="elevation"&&r[`elevation${l.elevation}`]]}})(tt(({theme:n})=>({backgroundColor:(n.vars||n).palette.background.paper,color:(n.vars||n).palette.text.primary,transition:n.transitions.create("box-shadow"),variants:[{props:({ownerState:r})=>!r.square,style:{borderRadius:n.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(n.vars||n).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),hn=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiPaper"}),u=Gu(),{className:c,component:d="div",elevation:p=1,square:m=!1,variant:h="elevation",...y}=i,x={...i,component:d,elevation:p,square:m,variant:h},R=BT(x);return S.jsx(_T,{as:d,ownerState:x,className:Me(R.root,c),ref:l,...y,style:{...h==="elevation"&&{"--Paper-shadow":(u.vars||u).shadows[p],...u.vars&&{"--Paper-overlay":u.vars.overlays?.[p]},...!u.vars&&u.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${yt("#fff",wd(p))}, ${yt("#fff",wd(p))})`}},...y.style}})});function LT(n){return typeof n=="string"}function V0(n,r,l){return n===void 0||LT(n)?r:{...r,ownerState:{...r.ownerState,...l}}}function Y0(n,r,l){return typeof n=="function"?n(r,l):n}function G0(n,r=[]){if(n===void 0)return{};const l={};return Object.keys(n).filter(i=>i.match(/^on[A-Z]/)&&typeof n[i]=="function"&&!r.includes(i)).forEach(i=>{l[i]=n[i]}),l}function tv(n){if(n===void 0)return{};const r={};return Object.keys(n).filter(l=>!(l.match(/^on[A-Z]/)&&typeof n[l]=="function")).forEach(l=>{r[l]=n[l]}),r}function X0(n){const{getSlotProps:r,additionalProps:l,externalSlotProps:i,externalForwardedProps:u,className:c}=n;if(!r){const M=Me(l?.className,c,u?.className,i?.className),E={...l?.style,...u?.style,...i?.style},v={...l,...u,...i};return M.length>0&&(v.className=M),Object.keys(E).length>0&&(v.style=E),{props:v,internalRef:void 0}}const d=G0({...u,...i}),p=tv(i),m=tv(u),h=r(d),y=Me(h?.className,l?.className,c,u?.className,i?.className),x={...h?.style,...l?.style,...u?.style,...i?.style},R={...h,...l,...m,...p};return y.length>0&&(R.className=y),Object.keys(x).length>0&&(R.style=x),{props:R,internalRef:h.ref}}function wt(n,r){const{className:l,elementType:i,ownerState:u,externalForwardedProps:c,internalForwardedProps:d,shouldForwardComponentProp:p=!1,...m}=r,{component:h,slots:y={[n]:void 0},slotProps:x={[n]:void 0},...R}=c,M=y[n]||i,E=Y0(x[n],u),{props:{component:v,...w},internalRef:j}=X0({className:l,...m,externalForwardedProps:n==="root"?R:void 0,externalSlotProps:E}),P=cn(j,E?.ref,r.ref),D=n==="root"?v||h:v,$=V0(M,{...n==="root"&&!h&&!y[n]&&d,...n!=="root"&&!y[n]&&d,...w,...D&&!p&&{as:D},...D&&p&&{component:D},ref:P},u);return[M,$]}function nv(n){try{return n.matches(":focus-visible")}catch{}return!1}class Ou{static create(){return new Ou}static use(){const r=U0(Ou.create).current,[l,i]=O.useState(!1);return r.shouldMount=l,r.setShouldMount=i,O.useEffect(r.mountEffect,[l]),r}constructor(){this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}mount(){return this.mounted||(this.mounted=PT(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}mountEffect=()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())};start(...r){this.mount().then(()=>this.ref.current?.start(...r))}stop(...r){this.mount().then(()=>this.ref.current?.stop(...r))}pulsate(...r){this.mount().then(()=>this.ref.current?.pulsate(...r))}}function HT(){return Ou.use()}function PT(){let n,r;const l=new Promise((i,u)=>{n=i,r=u});return l.resolve=n,l.reject=r,l}function UT(n){const{className:r,classes:l,pulsate:i=!1,rippleX:u,rippleY:c,rippleSize:d,in:p,onExited:m,timeout:h}=n,[y,x]=O.useState(!1),R=Me(r,l.ripple,l.rippleVisible,i&&l.ripplePulsate),M={width:d,height:d,top:-(d/2)+c,left:-(d/2)+u},E=Me(l.child,y&&l.childLeaving,i&&l.childPulsate);return!p&&!y&&x(!0),O.useEffect(()=>{if(!p&&m!=null){const v=setTimeout(m,h);return()=>{clearTimeout(v)}}},[m,p,h]),S.jsx("span",{className:R,style:M,children:S.jsx("span",{className:E})})}const sa=Ke("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),jd=550,qT=80,IT=Pi`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,VT=Pi`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,YT=Pi`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,GT=pe("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),XT=pe(UT,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${sa.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${IT};
    animation-duration: ${jd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  &.${sa.ripplePulsate} {
    animation-duration: ${({theme:n})=>n.transitions.duration.shorter}ms;
  }

  & .${sa.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${sa.childLeaving} {
    opacity: 0;
    animation-name: ${VT};
    animation-duration: ${jd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  & .${sa.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${YT};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,KT=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTouchRipple"}),{center:u=!1,classes:c={},className:d,...p}=i,[m,h]=O.useState([]),y=O.useRef(0),x=O.useRef(null);O.useEffect(()=>{x.current&&(x.current(),x.current=null)},[m]);const R=O.useRef(!1),M=q0(),E=O.useRef(null),v=O.useRef(null),w=O.useCallback($=>{const{pulsate:z,rippleX:N,rippleY:I,rippleSize:G,cb:K}=$;h(b=>[...b,S.jsx(XT,{classes:{ripple:Me(c.ripple,sa.ripple),rippleVisible:Me(c.rippleVisible,sa.rippleVisible),ripplePulsate:Me(c.ripplePulsate,sa.ripplePulsate),child:Me(c.child,sa.child),childLeaving:Me(c.childLeaving,sa.childLeaving),childPulsate:Me(c.childPulsate,sa.childPulsate)},timeout:jd,pulsate:z,rippleX:N,rippleY:I,rippleSize:G},y.current)]),y.current+=1,x.current=K},[c]),j=O.useCallback(($={},z={},N=()=>{})=>{const{pulsate:I=!1,center:G=u||z.pulsate,fakeElement:K=!1}=z;if($?.type==="mousedown"&&R.current){R.current=!1;return}$?.type==="touchstart"&&(R.current=!0);const b=K?null:v.current,B=b?b.getBoundingClientRect():{width:0,height:0,left:0,top:0};let V,ae,ee;if(G||$===void 0||$.clientX===0&&$.clientY===0||!$.clientX&&!$.touches)V=Math.round(B.width/2),ae=Math.round(B.height/2);else{const{clientX:_,clientY:T}=$.touches&&$.touches.length>0?$.touches[0]:$;V=Math.round(_-B.left),ae=Math.round(T-B.top)}if(G)ee=Math.sqrt((2*B.width**2+B.height**2)/3),ee%2===0&&(ee+=1);else{const _=Math.max(Math.abs((b?b.clientWidth:0)-V),V)*2+2,T=Math.max(Math.abs((b?b.clientHeight:0)-ae),ae)*2+2;ee=Math.sqrt(_**2+T**2)}$?.touches?E.current===null&&(E.current=()=>{w({pulsate:I,rippleX:V,rippleY:ae,rippleSize:ee,cb:N})},M.start(qT,()=>{E.current&&(E.current(),E.current=null)})):w({pulsate:I,rippleX:V,rippleY:ae,rippleSize:ee,cb:N})},[u,w,M]),P=O.useCallback(()=>{j({},{pulsate:!0})},[j]),D=O.useCallback(($,z)=>{if(M.clear(),$?.type==="touchend"&&E.current){E.current(),E.current=null,M.start(0,()=>{D($,z)});return}E.current=null,h(N=>N.length>0?N.slice(1):N),x.current=z},[M]);return O.useImperativeHandle(l,()=>({pulsate:P,start:j,stop:D}),[P,j,D]),S.jsx(GT,{className:Me(sa.root,c.root,d),ref:v,...p,children:S.jsx(pp,{component:null,exit:!0,children:m})})});function QT(n){return Xe("MuiButtonBase",n)}const FT=Ke("MuiButtonBase",["root","disabled","focusVisible"]),WT=n=>{const{disabled:r,focusVisible:l,focusVisibleClassName:i,classes:u}=n,d=Qe({root:["root",r&&"disabled",l&&"focusVisible"]},QT,u);return l&&i&&(d.root+=` ${i}`),d},ZT=pe("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${FT.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),ki=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiButtonBase"}),{action:u,centerRipple:c=!1,children:d,className:p,component:m="button",disabled:h=!1,disableRipple:y=!1,disableTouchRipple:x=!1,focusRipple:R=!1,focusVisibleClassName:M,LinkComponent:E="a",onBlur:v,onClick:w,onContextMenu:j,onDragLeave:P,onFocus:D,onFocusVisible:$,onKeyDown:z,onKeyUp:N,onMouseDown:I,onMouseLeave:G,onMouseUp:K,onTouchEnd:b,onTouchMove:B,onTouchStart:V,tabIndex:ae=0,TouchRippleProps:ee,touchRippleRef:_,type:T,...L}=i,Y=O.useRef(null),X=HT(),A=cn(X.ref,_),[U,J]=O.useState(!1);h&&U&&J(!1),O.useImperativeHandle(u,()=>({focusVisible:()=>{J(!0),Y.current.focus()}}),[]);const ne=X.shouldMount&&!y&&!h;O.useEffect(()=>{U&&R&&!y&&X.pulsate()},[y,R,U,X]);const fe=rr(X,"start",I,x),ue=rr(X,"stop",j,x),le=rr(X,"stop",P,x),ve=rr(X,"stop",K,x),xe=rr(X,"stop",ye=>{U&&ye.preventDefault(),G&&G(ye)},x),be=rr(X,"start",V,x),me=rr(X,"stop",b,x),Re=rr(X,"stop",B,x),Ne=rr(X,"stop",ye=>{nv(ye.target)||J(!1),v&&v(ye)},!1),Le=Ca(ye=>{Y.current||(Y.current=ye.currentTarget),nv(ye.target)&&(J(!0),$&&$(ye)),D&&D(ye)}),$e=()=>{const ye=Y.current;return m&&m!=="button"&&!(ye.tagName==="A"&&ye.href)},nt=Ca(ye=>{R&&!ye.repeat&&U&&ye.key===" "&&X.stop(ye,()=>{X.start(ye)}),ye.target===ye.currentTarget&&$e()&&ye.key===" "&&ye.preventDefault(),z&&z(ye),ye.target===ye.currentTarget&&$e()&&ye.key==="Enter"&&!h&&(ye.preventDefault(),w&&w(ye))}),Ve=Ca(ye=>{R&&ye.key===" "&&U&&!ye.defaultPrevented&&X.stop(ye,()=>{X.pulsate(ye)}),N&&N(ye),w&&ye.target===ye.currentTarget&&$e()&&ye.key===" "&&!ye.defaultPrevented&&w(ye)});let at=m;at==="button"&&(L.href||L.to)&&(at=E);const he={};at==="button"?(he.type=T===void 0?"button":T,he.disabled=h):(!L.href&&!L.to&&(he.role="button"),h&&(he["aria-disabled"]=h));const st=cn(l,Y),Te={...i,centerRipple:c,component:m,disabled:h,disableRipple:y,disableTouchRipple:x,focusRipple:R,tabIndex:ae,focusVisible:U},He=WT(Te);return S.jsxs(ZT,{as:at,className:Me(He.root,p),ownerState:Te,onBlur:Ne,onClick:w,onContextMenu:ue,onFocus:Le,onKeyDown:nt,onKeyUp:Ve,onMouseDown:fe,onMouseLeave:xe,onMouseUp:ve,onDragLeave:le,onTouchEnd:me,onTouchMove:Re,onTouchStart:be,ref:st,tabIndex:h?-1:ae,type:T,...he,...L,children:[d,ne?S.jsx(KT,{ref:A,center:c,...ee}):null]})});function rr(n,r,l,i=!1){return Ca(u=>(l&&l(u),i||n[r](u),!0))}function JT(n){return typeof n.main=="string"}function eE(n,r=[]){if(!JT(n))return!1;for(const l of r)if(!n.hasOwnProperty(l)||typeof n[l]!="string")return!1;return!0}function xn(n=[]){return([,r])=>r&&eE(r,n)}function tE(n){return Xe("MuiCircularProgress",n)}Ke("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const jr=44,kd=Pi`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,Nd=Pi`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,nE=typeof kd!="string"?Jd`
        animation: ${kd} 1.4s linear infinite;
      `:null,aE=typeof Nd!="string"?Jd`
        animation: ${Nd} 1.4s ease-in-out infinite;
      `:null,rE=n=>{const{classes:r,variant:l,color:i,disableShrink:u}=n,c={root:["root",l,`color${de(i)}`],svg:["svg"],circle:["circle",`circle${de(l)}`,u&&"circleDisableShrink"]};return Qe(c,tE,r)},oE=pe("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[l.variant],r[`color${de(l.color)}`]]}})(tt(({theme:n})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("transform")}},{props:{variant:"indeterminate"},style:nE||{animation:`${kd} 1.4s linear infinite`}},...Object.entries(n.palette).filter(xn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}}))]}))),lE=pe("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),iE=pe("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.circle,r[`circle${de(l.variant)}`],l.disableShrink&&r.circleDisableShrink]}})(tt(({theme:n})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:r})=>r.variant==="indeterminate"&&!r.disableShrink,style:aE||{animation:`${Nd} 1.4s ease-in-out infinite`}}]}))),K0=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiCircularProgress"}),{className:u,color:c="primary",disableShrink:d=!1,size:p=40,style:m,thickness:h=3.6,value:y=0,variant:x="indeterminate",...R}=i,M={...i,color:c,disableShrink:d,size:p,thickness:h,value:y,variant:x},E=rE(M),v={},w={},j={};if(x==="determinate"){const P=2*Math.PI*((jr-h)/2);v.strokeDasharray=P.toFixed(3),j["aria-valuenow"]=Math.round(y),v.strokeDashoffset=`${((100-y)/100*P).toFixed(3)}px`,w.transform="rotate(-90deg)"}return S.jsx(oE,{className:Me(E.root,u),style:{width:p,height:p,...w,...m},ownerState:M,ref:l,role:"progressbar",...j,...R,children:S.jsx(lE,{className:E.svg,ownerState:M,viewBox:`${jr/2} ${jr/2} ${jr} ${jr}`,children:S.jsx(iE,{className:E.circle,style:v,ownerState:M,cx:jr,cy:jr,r:(jr-h)/2,fill:"none",strokeWidth:h})})})});function sE(n){return Xe("MuiIconButton",n)}const av=Ke("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),uE=n=>{const{classes:r,disabled:l,color:i,edge:u,size:c,loading:d}=n,p={root:["root",d&&"loading",l&&"disabled",i!=="default"&&`color${de(i)}`,u&&`edge${de(u)}`,`size${de(c)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return Qe(p,sE,r)},cE=pe(ki,{name:"MuiIconButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.loading&&r.loading,l.color!=="default"&&r[`color${de(l.color)}`],l.edge&&r[`edge${de(l.edge)}`],r[`size${de(l.size)}`]]}})(tt(({theme:n})=>({textAlign:"center",flex:"0 0 auto",fontSize:n.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(n.vars||n).palette.action.active,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shortest}),variants:[{props:r=>!r.disableRipple,style:{"--IconButton-hoverBg":n.vars?`rgba(${n.vars.palette.action.activeChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette.action.active,n.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),tt(({theme:n})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(n.palette).filter(xn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}})),...Object.entries(n.palette).filter(xn()).map(([r])=>({props:{color:r},style:{"--IconButton-hoverBg":n.vars?`rgba(${(n.vars||n).palette[r].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt((n.vars||n).palette[r].main,n.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:n.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:n.typography.pxToRem(28)}}],[`&.${av.disabled}`]:{backgroundColor:"transparent",color:(n.vars||n).palette.action.disabled},[`&.${av.loading}`]:{color:"transparent"}}))),fE=pe("span",{name:"MuiIconButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(n.vars||n).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),rl=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiIconButton"}),{edge:u=!1,children:c,className:d,color:p="default",disabled:m=!1,disableFocusRipple:h=!1,size:y="medium",id:x,loading:R=null,loadingIndicator:M,...E}=i,v=hl(x),w=M??S.jsx(K0,{"aria-labelledby":v,color:"inherit",size:16}),j={...i,edge:u,color:p,disabled:m,disableFocusRipple:h,loading:R,loadingIndicator:w,size:y},P=uE(j);return S.jsxs(cE,{id:R?v:x,className:Me(P.root,d),centerRipple:!0,focusRipple:!h,disabled:m||R,ref:l,...E,ownerState:j,children:[typeof R=="boolean"&&S.jsx("span",{className:P.loadingWrapper,style:{display:"contents"},children:S.jsx(fE,{className:P.loadingIndicator,ownerState:j,children:R&&w})}),c]})}),dE=fn(S.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}));function pE(n){return Xe("MuiTypography",n)}Ke("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const mE={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},hE=xT(),gE=n=>{const{align:r,gutterBottom:l,noWrap:i,paragraph:u,variant:c,classes:d}=n,p={root:["root",c,n.align!=="inherit"&&`align${de(r)}`,l&&"gutterBottom",i&&"noWrap",u&&"paragraph"]};return Qe(p,pE,d)},yE=pe("span",{name:"MuiTypography",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.variant&&r[l.variant],l.align!=="inherit"&&r[`align${de(l.align)}`],l.noWrap&&r.noWrap,l.gutterBottom&&r.gutterBottom,l.paragraph&&r.paragraph]}})(tt(({theme:n})=>({margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(n.typography).filter(([r,l])=>r!=="inherit"&&l&&typeof l=="object").map(([r,l])=>({props:{variant:r},style:l})),...Object.entries(n.palette).filter(xn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}})),...Object.entries(n.palette?.text||{}).filter(([,r])=>typeof r=="string").map(([r])=>({props:{color:`text${de(r)}`},style:{color:(n.vars||n).palette.text[r]}})),{props:({ownerState:r})=>r.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:r})=>r.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:r})=>r.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:r})=>r.paragraph,style:{marginBottom:16}}]}))),rv={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},_e=O.forwardRef(function(r,l){const{color:i,...u}=Je({props:r,name:"MuiTypography"}),c=!mE[i],d=hE({...u,...c&&{color:i}}),{align:p="inherit",className:m,component:h,gutterBottom:y=!1,noWrap:x=!1,paragraph:R=!1,variant:M="body1",variantMapping:E=rv,...v}=d,w={...d,align:p,color:i,className:m,component:h,gutterBottom:y,noWrap:x,paragraph:R,variant:M,variantMapping:E},j=h||(R?"p":E[M]||rv[M])||"span",P=gE(w);return S.jsx(yE,{as:j,ref:l,className:Me(P.root,m),...v,ownerState:w,style:{...p!=="inherit"&&{"--Typography-textAlign":p},...v.style}})}),vE=n=>{const r=O.useRef({});return O.useEffect(()=>{r.current=n}),r.current};function ov(n){return n.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function bE(n={}){const{ignoreAccents:r=!0,ignoreCase:l=!0,limit:i,matchFrom:u="any",stringify:c,trim:d=!1}=n;return(p,{inputValue:m,getOptionLabel:h})=>{let y=d?m.trim():m;l&&(y=y.toLowerCase()),r&&(y=ov(y));const x=y?p.filter(R=>{let M=(c||h)(R);return l&&(M=M.toLowerCase()),r&&(M=ov(M)),u==="start"?M.startsWith(y):M.includes(y)}):p;return typeof i=="number"?x.slice(0,i):x}}const SE=bE(),lv=5,xE=n=>n.current!==null&&n.current.parentElement?.contains(document.activeElement),CE=[];function iv(n,r,l,i){if(r||n==null||i)return"";const u=l(n);return typeof u=="string"?u:""}function TE(n){const{unstable_isActiveElementInListbox:r=xE,unstable_classNamePrefix:l="Mui",autoComplete:i=!1,autoHighlight:u=!1,autoSelect:c=!1,blurOnSelect:d=!1,clearOnBlur:p=!n.freeSolo,clearOnEscape:m=!1,componentName:h="useAutocomplete",defaultValue:y=n.multiple?CE:null,disableClearable:x=!1,disableCloseOnSelect:R=!1,disabled:M,disabledItemsFocusable:E=!1,disableListWrap:v=!1,filterOptions:w=SE,filterSelectedOptions:j=!1,freeSolo:P=!1,getOptionDisabled:D,getOptionKey:$,getOptionLabel:z=te=>te.label??te,groupBy:N,handleHomeEndKeys:I=!n.freeSolo,id:G,includeInputInList:K=!1,inputValue:b,isOptionEqualToValue:B=(te,re)=>te===re,multiple:V=!1,onChange:ae,onClose:ee,onHighlightChange:_,onInputChange:T,onOpen:L,open:Y,openOnFocus:X=!1,options:A,readOnly:U=!1,renderValue:J,selectOnFocus:ne=!n.freeSolo,value:fe}=n,ue=hl(G);let le=z;le=te=>{const re=z(te);return typeof re!="string"?String(re):re};const ve=O.useRef(!1),xe=O.useRef(!0),be=O.useRef(null),me=O.useRef(null),[Re,Ne]=O.useState(null),[Le,$e]=O.useState(-1),nt=u?0:-1,Ve=O.useRef(nt),at=O.useRef(iv(y??fe,V,le)).current,[he,st]=sl({controlled:fe,default:y,name:h}),[Te,He]=sl({controlled:b,default:at,name:h,state:"inputValue"}),[ye,qt]=O.useState(!1),ut=O.useCallback((te,re,ge)=>{if(!(V?he.length<re.length:re!==null)&&!p)return;const Ee=iv(re,V,le,J);Te!==Ee&&(He(Ee),T&&T(te,Ee,ge))},[le,Te,V,T,He,p,he,J]),[At,rt]=sl({controlled:Y,default:!1,name:h,state:"open"}),[ke,Ye]=O.useState(!0),et=!V&&he!=null&&Te===le(he),vt=At&&!U,Ae=vt?w(A.filter(te=>!(j&&(V?he:[he]).some(re=>re!==null&&B(te,re)))),{inputValue:et&&ke?"":Te,getOptionLabel:le}):[],Gt=vE({filteredOptions:Ae,value:he,inputValue:Te});O.useEffect(()=>{const te=he!==Gt.value;ye&&!te||P&&!te||ut(null,he,"reset")},[he,ut,ye,Gt.value,P]);const Cn=At&&Ae.length>0&&!U,Zt=Ca(te=>{if(te===-1)be.current.focus();else{const re=J?"data-item-index":"data-tag-index";Re.querySelector(`[${re}="${te}"]`).focus()}});O.useEffect(()=>{V&&Le>he.length-1&&($e(-1),Zt(-1))},[he,V,Le,Zt]);function Ce(te,re){if(!me.current||te<0||te>=Ae.length)return-1;let ge=te;for(;;){const Be=me.current.querySelector(`[data-option-index="${ge}"]`),Ee=E?!1:!Be||Be.disabled||Be.getAttribute("aria-disabled")==="true";if(Be&&Be.hasAttribute("tabindex")&&!Ee)return ge;if(re==="next"?ge=(ge+1)%Ae.length:ge=(ge-1+Ae.length)%Ae.length,ge===te)return-1}}const Ue=Ca(({event:te,index:re,reason:ge})=>{if(Ve.current=re,re===-1?be.current.removeAttribute("aria-activedescendant"):be.current.setAttribute("aria-activedescendant",`${ue}-option-${re}`),_&&["mouse","keyboard","touch"].includes(ge)&&_(te,re===-1?null:Ae[re],ge),!me.current)return;const Be=me.current.querySelector(`[role="option"].${l}-focused`);Be&&(Be.classList.remove(`${l}-focused`),Be.classList.remove(`${l}-focusVisible`));let Ee=me.current;if(me.current.getAttribute("role")!=="listbox"&&(Ee=me.current.parentElement.querySelector('[role="listbox"]')),!Ee)return;if(re===-1){Ee.scrollTop=0;return}const Dt=me.current.querySelector(`[data-option-index="${re}"]`);if(Dt&&(Dt.classList.add(`${l}-focused`),ge==="keyboard"&&Dt.classList.add(`${l}-focusVisible`),Ee.scrollHeight>Ee.clientHeight&&ge!=="mouse"&&ge!=="touch")){const kt=Dt,vn=Ee.clientHeight+Ee.scrollTop,Sl=kt.offsetTop+kt.offsetHeight;Sl>vn?Ee.scrollTop=Sl-Ee.clientHeight:kt.offsetTop-kt.offsetHeight*(N?1.3:0)<Ee.scrollTop&&(Ee.scrollTop=kt.offsetTop-kt.offsetHeight*(N?1.3:0))}}),ot=Ca(({event:te,diff:re,direction:ge="next",reason:Be})=>{if(!vt)return;const Dt=Ce((()=>{const kt=Ae.length-1;if(re==="reset")return nt;if(re==="start")return 0;if(re==="end")return kt;const vn=Ve.current+re;return vn<0?vn===-1&&K?-1:v&&Ve.current!==-1||Math.abs(re)>1?0:kt:vn>kt?vn===kt+1&&K?-1:v||Math.abs(re)>1?kt:0:vn})(),ge);if(Ue({index:Dt,reason:Be,event:te}),i&&re!=="reset")if(Dt===-1)be.current.value=Te;else{const kt=le(Ae[Dt]);be.current.value=kt,kt.toLowerCase().indexOf(Te.toLowerCase())===0&&Te.length>0&&be.current.setSelectionRange(Te.length,kt.length)}}),Gn=()=>{const te=(re,ge)=>{const Be=re?le(re):"",Ee=ge?le(ge):"";return Be===Ee};if(Ve.current!==-1&&Gt.filteredOptions&&Gt.filteredOptions.length!==Ae.length&&Gt.inputValue===Te&&(V?he.length===Gt.value.length&&Gt.value.every((re,ge)=>le(he[ge])===le(re)):te(Gt.value,he))){const re=Gt.filteredOptions[Ve.current];if(re)return Ae.findIndex(ge=>le(ge)===le(re))}return-1},ha=O.useCallback(()=>{if(!vt)return;const te=Gn();if(te!==-1){Ve.current=te;return}const re=V?he[0]:he;if(Ae.length===0||re==null){ot({diff:"reset"});return}if(me.current){if(re!=null){const ge=Ae[Ve.current];if(V&&ge&&he.findIndex(Ee=>B(ge,Ee))!==-1)return;const Be=Ae.findIndex(Ee=>B(Ee,re));Be===-1?ot({diff:"reset"}):Ue({index:Be});return}if(Ve.current>=Ae.length-1){Ue({index:Ae.length-1});return}Ue({index:Ve.current})}},[Ae.length,V?!1:he,j,ot,Ue,vt,Te,V]),po=Ca(te=>{zd(me,te),te&&ha()});O.useEffect(()=>{ha()},[ha]);const tn=te=>{At||(rt(!0),Ye(!0),L&&L(te))},zn=(te,re)=>{At&&(rt(!1),ee&&ee(te,re))},ga=(te,re,ge,Be)=>{if(V){if(he.length===re.length&&he.every((Ee,Dt)=>Ee===re[Dt]))return}else if(he===re)return;ae&&ae(te,re,ge,Be),st(re)},Ua=O.useRef(!1),Dn=(te,re,ge="selectOption",Be="options")=>{let Ee=ge,Dt=re;if(V){Dt=Array.isArray(he)?he.slice():[];const kt=Dt.findIndex(vn=>B(re,vn));kt===-1?Dt.push(re):Be!=="freeSolo"&&(Dt.splice(kt,1),Ee="removeOption")}ut(te,Dt,Ee),ga(te,Dt,Ee,{option:re}),!R&&(!te||!te.ctrlKey&&!te.metaKey)&&zn(te,Ee),(d===!0||d==="touch"&&Ua.current||d==="mouse"&&!Ua.current)&&be.current.blur()};function ur(te,re){if(te===-1)return-1;let ge=te;for(;;){if(re==="next"&&ge===he.length||re==="previous"&&ge===-1)return-1;const Be=J?"data-item-index":"data-tag-index",Ee=Re.querySelector(`[${Be}="${ge}"]`);if(!Ee||!Ee.hasAttribute("tabindex")||Ee.disabled||Ee.getAttribute("aria-disabled")==="true")ge+=re==="next"?1:-1;else return ge}}const cr=(te,re)=>{if(!V)return;Te===""&&zn(te,"toggleInput");let ge=Le;Le===-1?Te===""&&re==="previous"&&(ge=he.length-1):(ge+=re==="next"?1:-1,ge<0&&(ge=0),ge===he.length&&(ge=-1)),ge=ur(ge,re),$e(ge),Zt(ge)},Ea=te=>{ve.current=!0,He(""),T&&T(te,"","clear"),ga(te,V?[]:null,"clear")},yl=te=>re=>{if(te.onKeyDown&&te.onKeyDown(re),!re.defaultMuiPrevented&&(Le!==-1&&!["ArrowLeft","ArrowRight"].includes(re.key)&&($e(-1),Zt(-1)),re.which!==229))switch(re.key){case"Home":vt&&I&&(re.preventDefault(),ot({diff:"start",direction:"next",reason:"keyboard",event:re}));break;case"End":vt&&I&&(re.preventDefault(),ot({diff:"end",direction:"previous",reason:"keyboard",event:re}));break;case"PageUp":re.preventDefault(),ot({diff:-lv,direction:"previous",reason:"keyboard",event:re}),tn(re);break;case"PageDown":re.preventDefault(),ot({diff:lv,direction:"next",reason:"keyboard",event:re}),tn(re);break;case"ArrowDown":re.preventDefault(),ot({diff:1,direction:"next",reason:"keyboard",event:re}),tn(re);break;case"ArrowUp":re.preventDefault(),ot({diff:-1,direction:"previous",reason:"keyboard",event:re}),tn(re);break;case"ArrowLeft":!V&&J?Zt(0):cr(re,"previous");break;case"ArrowRight":!V&&J?Zt(-1):cr(re,"next");break;case"Enter":if(Ve.current!==-1&&vt){const ge=Ae[Ve.current],Be=D?D(ge):!1;if(re.preventDefault(),Be)return;Dn(re,ge,"selectOption"),i&&be.current.setSelectionRange(be.current.value.length,be.current.value.length)}else P&&Te!==""&&et===!1&&(V&&re.preventDefault(),Dn(re,Te,"createOption","freeSolo"));break;case"Escape":vt?(re.preventDefault(),re.stopPropagation(),zn(re,"escape")):m&&(Te!==""||V&&he.length>0||J)&&(re.preventDefault(),re.stopPropagation(),Ea(re));break;case"Backspace":if(V&&!U&&Te===""&&he.length>0){const ge=Le===-1?he.length-1:Le,Be=he.slice();Be.splice(ge,1),ga(re,Be,"removeOption",{option:he[ge]})}!V&&J&&!U&&(st(null),Zt(-1));break;case"Delete":if(V&&!U&&Te===""&&he.length>0&&Le!==-1){const ge=Le,Be=he.slice();Be.splice(ge,1),ga(re,Be,"removeOption",{option:he[ge]})}!V&&J&&!U&&(st(null),Zt(-1));break}},Xn=te=>{qt(!0),X&&!ve.current&&tn(te)},zt=te=>{if(r(me)){be.current.focus();return}qt(!1),xe.current=!0,ve.current=!1,c&&Ve.current!==-1&&vt?Dn(te,Ae[Ve.current],"blur"):c&&P&&Te!==""?Dn(te,Te,"blur","freeSolo"):p&&ut(te,he,"blur"),zn(te,"blur")},Xt=te=>{const re=te.target.value;Te!==re&&(He(re),Ye(!1),T&&T(te,re,"input")),re===""?!x&&!V&&ga(te,null,"clear"):tn(te)},Kn=te=>{const re=Number(te.currentTarget.getAttribute("data-option-index"));Ve.current!==re&&Ue({event:te,index:re,reason:"mouse"})},Pr=te=>{Ue({event:te,index:Number(te.currentTarget.getAttribute("data-option-index")),reason:"touch"}),Ua.current=!0},vl=te=>{const re=Number(te.currentTarget.getAttribute("data-option-index"));Dn(te,Ae[re],"selectOption"),Ua.current=!1},mo=te=>re=>{const ge=he.slice();ge.splice(te,1),ga(re,ge,"removeOption",{option:he[te]})},bl=te=>{ga(te,null,"removeOption",{option:he})},Qn=te=>{At?zn(te,"toggleInput"):tn(te)},Bt=te=>{te.currentTarget.contains(te.target)&&te.target.getAttribute("id")!==ue&&te.preventDefault()},Mt=te=>{te.currentTarget.contains(te.target)&&(be.current.focus(),ne&&xe.current&&be.current.selectionEnd-be.current.selectionStart===0&&be.current.select(),xe.current=!1)},yn=te=>{!M&&(Te===""||!At)&&Qn(te)};let ya=P&&Te.length>0;ya=ya||(V?he.length>0:he!==null);let va=Ae;return N&&(va=Ae.reduce((te,re,ge)=>{const Be=N(re);return te.length>0&&te[te.length-1].group===Be?te[te.length-1].options.push(re):te.push({key:ge,index:ge,group:Be,options:[re]}),te},[])),M&&ye&&zt(),{getRootProps:(te={})=>({...te,onKeyDown:yl(te),onMouseDown:Bt,onClick:Mt}),getInputLabelProps:()=>({id:`${ue}-label`,htmlFor:ue}),getInputProps:()=>({id:ue,value:Te,onBlur:zt,onFocus:Xn,onChange:Xt,onMouseDown:yn,"aria-activedescendant":vt?"":null,"aria-autocomplete":i?"both":"list","aria-controls":Cn?`${ue}-listbox`:void 0,"aria-expanded":Cn,autoComplete:"off",ref:be,autoCapitalize:"none",spellCheck:"false",role:"combobox",disabled:M}),getClearProps:()=>({tabIndex:-1,type:"button",onClick:Ea}),getItemProps:({index:te=0}={})=>({...V&&{key:te},...J?{"data-item-index":te}:{"data-tag-index":te},tabIndex:-1,...!U&&{onDelete:V?mo(te):bl}}),getPopupIndicatorProps:()=>({tabIndex:-1,type:"button",onClick:Qn}),getTagProps:({index:te})=>({key:te,"data-tag-index":te,tabIndex:-1,...!U&&{onDelete:mo(te)}}),getListboxProps:()=>({role:"listbox",id:`${ue}-listbox`,"aria-labelledby":`${ue}-label`,ref:po,onMouseDown:te=>{te.preventDefault()}}),getOptionProps:({index:te,option:re})=>{const ge=(V?he:[he]).some(Ee=>Ee!=null&&B(re,Ee)),Be=D?D(re):!1;return{key:$?.(re)??le(re),tabIndex:-1,role:"option",id:`${ue}-option-${te}`,onMouseMove:Kn,onClick:vl,onTouchStart:Pr,"data-option-index":te,"aria-disabled":Be,"aria-selected":ge}},id:ue,inputValue:Te,value:he,dirty:ya,expanded:vt&&Re,popupOpen:vt,focused:ye||Le!==-1,anchorEl:Re,setAnchorEl:Ne,focusedItem:Le,focusedTag:Le,groupedOptions:va}}var Mn="top",da="bottom",pa="right",wn="left",hp="auto",Yi=[Mn,da,pa,wn],cl="start",Ni="end",EE="clippingParents",Q0="viewport",yi="popper",RE="reference",sv=Yi.reduce(function(n,r){return n.concat([r+"-"+cl,r+"-"+Ni])},[]),F0=[].concat(Yi,[hp]).reduce(function(n,r){return n.concat([r,r+"-"+cl,r+"-"+Ni])},[]),OE="beforeRead",ME="read",wE="afterRead",AE="beforeMain",zE="main",DE="afterMain",$E="beforeWrite",jE="write",kE="afterWrite",NE=[OE,ME,wE,AE,zE,DE,$E,jE,kE];function Ha(n){return n?(n.nodeName||"").toLowerCase():null}function Vn(n){if(n==null)return window;if(n.toString()!=="[object Window]"){var r=n.ownerDocument;return r&&r.defaultView||window}return n}function co(n){var r=Vn(n).Element;return n instanceof r||n instanceof Element}function ca(n){var r=Vn(n).HTMLElement;return n instanceof r||n instanceof HTMLElement}function gp(n){if(typeof ShadowRoot>"u")return!1;var r=Vn(n).ShadowRoot;return n instanceof r||n instanceof ShadowRoot}function BE(n){var r=n.state;Object.keys(r.elements).forEach(function(l){var i=r.styles[l]||{},u=r.attributes[l]||{},c=r.elements[l];!ca(c)||!Ha(c)||(Object.assign(c.style,i),Object.keys(u).forEach(function(d){var p=u[d];p===!1?c.removeAttribute(d):c.setAttribute(d,p===!0?"":p)}))})}function _E(n){var r=n.state,l={popper:{position:r.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(r.elements.popper.style,l.popper),r.styles=l,r.elements.arrow&&Object.assign(r.elements.arrow.style,l.arrow),function(){Object.keys(r.elements).forEach(function(i){var u=r.elements[i],c=r.attributes[i]||{},d=Object.keys(r.styles.hasOwnProperty(i)?r.styles[i]:l[i]),p=d.reduce(function(m,h){return m[h]="",m},{});!ca(u)||!Ha(u)||(Object.assign(u.style,p),Object.keys(c).forEach(function(m){u.removeAttribute(m)}))})}}const LE={name:"applyStyles",enabled:!0,phase:"write",fn:BE,effect:_E,requires:["computeStyles"]};function La(n){return n.split("-")[0]}var uo=Math.max,Mu=Math.min,fl=Math.round;function Bd(){var n=navigator.userAgentData;return n!=null&&n.brands&&Array.isArray(n.brands)?n.brands.map(function(r){return r.brand+"/"+r.version}).join(" "):navigator.userAgent}function W0(){return!/^((?!chrome|android).)*safari/i.test(Bd())}function dl(n,r,l){r===void 0&&(r=!1),l===void 0&&(l=!1);var i=n.getBoundingClientRect(),u=1,c=1;r&&ca(n)&&(u=n.offsetWidth>0&&fl(i.width)/n.offsetWidth||1,c=n.offsetHeight>0&&fl(i.height)/n.offsetHeight||1);var d=co(n)?Vn(n):window,p=d.visualViewport,m=!W0()&&l,h=(i.left+(m&&p?p.offsetLeft:0))/u,y=(i.top+(m&&p?p.offsetTop:0))/c,x=i.width/u,R=i.height/c;return{width:x,height:R,top:y,right:h+x,bottom:y+R,left:h,x:h,y}}function yp(n){var r=dl(n),l=n.offsetWidth,i=n.offsetHeight;return Math.abs(r.width-l)<=1&&(l=r.width),Math.abs(r.height-i)<=1&&(i=r.height),{x:n.offsetLeft,y:n.offsetTop,width:l,height:i}}function Z0(n,r){var l=r.getRootNode&&r.getRootNode();if(n.contains(r))return!0;if(l&&gp(l)){var i=r;do{if(i&&n.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function sr(n){return Vn(n).getComputedStyle(n)}function HE(n){return["table","td","th"].indexOf(Ha(n))>=0}function Lr(n){return((co(n)?n.ownerDocument:n.document)||window.document).documentElement}function Xu(n){return Ha(n)==="html"?n:n.assignedSlot||n.parentNode||(gp(n)?n.host:null)||Lr(n)}function uv(n){return!ca(n)||sr(n).position==="fixed"?null:n.offsetParent}function PE(n){var r=/firefox/i.test(Bd()),l=/Trident/i.test(Bd());if(l&&ca(n)){var i=sr(n);if(i.position==="fixed")return null}var u=Xu(n);for(gp(u)&&(u=u.host);ca(u)&&["html","body"].indexOf(Ha(u))<0;){var c=sr(u);if(c.transform!=="none"||c.perspective!=="none"||c.contain==="paint"||["transform","perspective"].indexOf(c.willChange)!==-1||r&&c.willChange==="filter"||r&&c.filter&&c.filter!=="none")return u;u=u.parentNode}return null}function Gi(n){for(var r=Vn(n),l=uv(n);l&&HE(l)&&sr(l).position==="static";)l=uv(l);return l&&(Ha(l)==="html"||Ha(l)==="body"&&sr(l).position==="static")?r:l||PE(n)||r}function vp(n){return["top","bottom"].indexOf(n)>=0?"x":"y"}function Ri(n,r,l){return uo(n,Mu(r,l))}function UE(n,r,l){var i=Ri(n,r,l);return i>l?l:i}function J0(){return{top:0,right:0,bottom:0,left:0}}function eb(n){return Object.assign({},J0(),n)}function tb(n,r){return r.reduce(function(l,i){return l[i]=n,l},{})}var qE=function(r,l){return r=typeof r=="function"?r(Object.assign({},l.rects,{placement:l.placement})):r,eb(typeof r!="number"?r:tb(r,Yi))};function IE(n){var r,l=n.state,i=n.name,u=n.options,c=l.elements.arrow,d=l.modifiersData.popperOffsets,p=La(l.placement),m=vp(p),h=[wn,pa].indexOf(p)>=0,y=h?"height":"width";if(!(!c||!d)){var x=qE(u.padding,l),R=yp(c),M=m==="y"?Mn:wn,E=m==="y"?da:pa,v=l.rects.reference[y]+l.rects.reference[m]-d[m]-l.rects.popper[y],w=d[m]-l.rects.reference[m],j=Gi(c),P=j?m==="y"?j.clientHeight||0:j.clientWidth||0:0,D=v/2-w/2,$=x[M],z=P-R[y]-x[E],N=P/2-R[y]/2+D,I=Ri($,N,z),G=m;l.modifiersData[i]=(r={},r[G]=I,r.centerOffset=I-N,r)}}function VE(n){var r=n.state,l=n.options,i=l.element,u=i===void 0?"[data-popper-arrow]":i;u!=null&&(typeof u=="string"&&(u=r.elements.popper.querySelector(u),!u)||Z0(r.elements.popper,u)&&(r.elements.arrow=u))}const YE={name:"arrow",enabled:!0,phase:"main",fn:IE,effect:VE,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function pl(n){return n.split("-")[1]}var GE={top:"auto",right:"auto",bottom:"auto",left:"auto"};function XE(n,r){var l=n.x,i=n.y,u=r.devicePixelRatio||1;return{x:fl(l*u)/u||0,y:fl(i*u)/u||0}}function cv(n){var r,l=n.popper,i=n.popperRect,u=n.placement,c=n.variation,d=n.offsets,p=n.position,m=n.gpuAcceleration,h=n.adaptive,y=n.roundOffsets,x=n.isFixed,R=d.x,M=R===void 0?0:R,E=d.y,v=E===void 0?0:E,w=typeof y=="function"?y({x:M,y:v}):{x:M,y:v};M=w.x,v=w.y;var j=d.hasOwnProperty("x"),P=d.hasOwnProperty("y"),D=wn,$=Mn,z=window;if(h){var N=Gi(l),I="clientHeight",G="clientWidth";if(N===Vn(l)&&(N=Lr(l),sr(N).position!=="static"&&p==="absolute"&&(I="scrollHeight",G="scrollWidth")),N=N,u===Mn||(u===wn||u===pa)&&c===Ni){$=da;var K=x&&N===z&&z.visualViewport?z.visualViewport.height:N[I];v-=K-i.height,v*=m?1:-1}if(u===wn||(u===Mn||u===da)&&c===Ni){D=pa;var b=x&&N===z&&z.visualViewport?z.visualViewport.width:N[G];M-=b-i.width,M*=m?1:-1}}var B=Object.assign({position:p},h&&GE),V=y===!0?XE({x:M,y:v},Vn(l)):{x:M,y:v};if(M=V.x,v=V.y,m){var ae;return Object.assign({},B,(ae={},ae[$]=P?"0":"",ae[D]=j?"0":"",ae.transform=(z.devicePixelRatio||1)<=1?"translate("+M+"px, "+v+"px)":"translate3d("+M+"px, "+v+"px, 0)",ae))}return Object.assign({},B,(r={},r[$]=P?v+"px":"",r[D]=j?M+"px":"",r.transform="",r))}function KE(n){var r=n.state,l=n.options,i=l.gpuAcceleration,u=i===void 0?!0:i,c=l.adaptive,d=c===void 0?!0:c,p=l.roundOffsets,m=p===void 0?!0:p,h={placement:La(r.placement),variation:pl(r.placement),popper:r.elements.popper,popperRect:r.rects.popper,gpuAcceleration:u,isFixed:r.options.strategy==="fixed"};r.modifiersData.popperOffsets!=null&&(r.styles.popper=Object.assign({},r.styles.popper,cv(Object.assign({},h,{offsets:r.modifiersData.popperOffsets,position:r.options.strategy,adaptive:d,roundOffsets:m})))),r.modifiersData.arrow!=null&&(r.styles.arrow=Object.assign({},r.styles.arrow,cv(Object.assign({},h,{offsets:r.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:m})))),r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-placement":r.placement})}const QE={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:KE,data:{}};var iu={passive:!0};function FE(n){var r=n.state,l=n.instance,i=n.options,u=i.scroll,c=u===void 0?!0:u,d=i.resize,p=d===void 0?!0:d,m=Vn(r.elements.popper),h=[].concat(r.scrollParents.reference,r.scrollParents.popper);return c&&h.forEach(function(y){y.addEventListener("scroll",l.update,iu)}),p&&m.addEventListener("resize",l.update,iu),function(){c&&h.forEach(function(y){y.removeEventListener("scroll",l.update,iu)}),p&&m.removeEventListener("resize",l.update,iu)}}const WE={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:FE,data:{}};var ZE={left:"right",right:"left",bottom:"top",top:"bottom"};function gu(n){return n.replace(/left|right|bottom|top/g,function(r){return ZE[r]})}var JE={start:"end",end:"start"};function fv(n){return n.replace(/start|end/g,function(r){return JE[r]})}function bp(n){var r=Vn(n),l=r.pageXOffset,i=r.pageYOffset;return{scrollLeft:l,scrollTop:i}}function Sp(n){return dl(Lr(n)).left+bp(n).scrollLeft}function eR(n,r){var l=Vn(n),i=Lr(n),u=l.visualViewport,c=i.clientWidth,d=i.clientHeight,p=0,m=0;if(u){c=u.width,d=u.height;var h=W0();(h||!h&&r==="fixed")&&(p=u.offsetLeft,m=u.offsetTop)}return{width:c,height:d,x:p+Sp(n),y:m}}function tR(n){var r,l=Lr(n),i=bp(n),u=(r=n.ownerDocument)==null?void 0:r.body,c=uo(l.scrollWidth,l.clientWidth,u?u.scrollWidth:0,u?u.clientWidth:0),d=uo(l.scrollHeight,l.clientHeight,u?u.scrollHeight:0,u?u.clientHeight:0),p=-i.scrollLeft+Sp(n),m=-i.scrollTop;return sr(u||l).direction==="rtl"&&(p+=uo(l.clientWidth,u?u.clientWidth:0)-c),{width:c,height:d,x:p,y:m}}function xp(n){var r=sr(n),l=r.overflow,i=r.overflowX,u=r.overflowY;return/auto|scroll|overlay|hidden/.test(l+u+i)}function nb(n){return["html","body","#document"].indexOf(Ha(n))>=0?n.ownerDocument.body:ca(n)&&xp(n)?n:nb(Xu(n))}function Oi(n,r){var l;r===void 0&&(r=[]);var i=nb(n),u=i===((l=n.ownerDocument)==null?void 0:l.body),c=Vn(i),d=u?[c].concat(c.visualViewport||[],xp(i)?i:[]):i,p=r.concat(d);return u?p:p.concat(Oi(Xu(d)))}function _d(n){return Object.assign({},n,{left:n.x,top:n.y,right:n.x+n.width,bottom:n.y+n.height})}function nR(n,r){var l=dl(n,!1,r==="fixed");return l.top=l.top+n.clientTop,l.left=l.left+n.clientLeft,l.bottom=l.top+n.clientHeight,l.right=l.left+n.clientWidth,l.width=n.clientWidth,l.height=n.clientHeight,l.x=l.left,l.y=l.top,l}function dv(n,r,l){return r===Q0?_d(eR(n,l)):co(r)?nR(r,l):_d(tR(Lr(n)))}function aR(n){var r=Oi(Xu(n)),l=["absolute","fixed"].indexOf(sr(n).position)>=0,i=l&&ca(n)?Gi(n):n;return co(i)?r.filter(function(u){return co(u)&&Z0(u,i)&&Ha(u)!=="body"}):[]}function rR(n,r,l,i){var u=r==="clippingParents"?aR(n):[].concat(r),c=[].concat(u,[l]),d=c[0],p=c.reduce(function(m,h){var y=dv(n,h,i);return m.top=uo(y.top,m.top),m.right=Mu(y.right,m.right),m.bottom=Mu(y.bottom,m.bottom),m.left=uo(y.left,m.left),m},dv(n,d,i));return p.width=p.right-p.left,p.height=p.bottom-p.top,p.x=p.left,p.y=p.top,p}function ab(n){var r=n.reference,l=n.element,i=n.placement,u=i?La(i):null,c=i?pl(i):null,d=r.x+r.width/2-l.width/2,p=r.y+r.height/2-l.height/2,m;switch(u){case Mn:m={x:d,y:r.y-l.height};break;case da:m={x:d,y:r.y+r.height};break;case pa:m={x:r.x+r.width,y:p};break;case wn:m={x:r.x-l.width,y:p};break;default:m={x:r.x,y:r.y}}var h=u?vp(u):null;if(h!=null){var y=h==="y"?"height":"width";switch(c){case cl:m[h]=m[h]-(r[y]/2-l[y]/2);break;case Ni:m[h]=m[h]+(r[y]/2-l[y]/2);break}}return m}function Bi(n,r){r===void 0&&(r={});var l=r,i=l.placement,u=i===void 0?n.placement:i,c=l.strategy,d=c===void 0?n.strategy:c,p=l.boundary,m=p===void 0?EE:p,h=l.rootBoundary,y=h===void 0?Q0:h,x=l.elementContext,R=x===void 0?yi:x,M=l.altBoundary,E=M===void 0?!1:M,v=l.padding,w=v===void 0?0:v,j=eb(typeof w!="number"?w:tb(w,Yi)),P=R===yi?RE:yi,D=n.rects.popper,$=n.elements[E?P:R],z=rR(co($)?$:$.contextElement||Lr(n.elements.popper),m,y,d),N=dl(n.elements.reference),I=ab({reference:N,element:D,placement:u}),G=_d(Object.assign({},D,I)),K=R===yi?G:N,b={top:z.top-K.top+j.top,bottom:K.bottom-z.bottom+j.bottom,left:z.left-K.left+j.left,right:K.right-z.right+j.right},B=n.modifiersData.offset;if(R===yi&&B){var V=B[u];Object.keys(b).forEach(function(ae){var ee=[pa,da].indexOf(ae)>=0?1:-1,_=[Mn,da].indexOf(ae)>=0?"y":"x";b[ae]+=V[_]*ee})}return b}function oR(n,r){r===void 0&&(r={});var l=r,i=l.placement,u=l.boundary,c=l.rootBoundary,d=l.padding,p=l.flipVariations,m=l.allowedAutoPlacements,h=m===void 0?F0:m,y=pl(i),x=y?p?sv:sv.filter(function(E){return pl(E)===y}):Yi,R=x.filter(function(E){return h.indexOf(E)>=0});R.length===0&&(R=x);var M=R.reduce(function(E,v){return E[v]=Bi(n,{placement:v,boundary:u,rootBoundary:c,padding:d})[La(v)],E},{});return Object.keys(M).sort(function(E,v){return M[E]-M[v]})}function lR(n){if(La(n)===hp)return[];var r=gu(n);return[fv(n),r,fv(r)]}function iR(n){var r=n.state,l=n.options,i=n.name;if(!r.modifiersData[i]._skip){for(var u=l.mainAxis,c=u===void 0?!0:u,d=l.altAxis,p=d===void 0?!0:d,m=l.fallbackPlacements,h=l.padding,y=l.boundary,x=l.rootBoundary,R=l.altBoundary,M=l.flipVariations,E=M===void 0?!0:M,v=l.allowedAutoPlacements,w=r.options.placement,j=La(w),P=j===w,D=m||(P||!E?[gu(w)]:lR(w)),$=[w].concat(D).reduce(function(fe,ue){return fe.concat(La(ue)===hp?oR(r,{placement:ue,boundary:y,rootBoundary:x,padding:h,flipVariations:E,allowedAutoPlacements:v}):ue)},[]),z=r.rects.reference,N=r.rects.popper,I=new Map,G=!0,K=$[0],b=0;b<$.length;b++){var B=$[b],V=La(B),ae=pl(B)===cl,ee=[Mn,da].indexOf(V)>=0,_=ee?"width":"height",T=Bi(r,{placement:B,boundary:y,rootBoundary:x,altBoundary:R,padding:h}),L=ee?ae?pa:wn:ae?da:Mn;z[_]>N[_]&&(L=gu(L));var Y=gu(L),X=[];if(c&&X.push(T[V]<=0),p&&X.push(T[L]<=0,T[Y]<=0),X.every(function(fe){return fe})){K=B,G=!1;break}I.set(B,X)}if(G)for(var A=E?3:1,U=function(ue){var le=$.find(function(ve){var xe=I.get(ve);if(xe)return xe.slice(0,ue).every(function(be){return be})});if(le)return K=le,"break"},J=A;J>0;J--){var ne=U(J);if(ne==="break")break}r.placement!==K&&(r.modifiersData[i]._skip=!0,r.placement=K,r.reset=!0)}}const sR={name:"flip",enabled:!0,phase:"main",fn:iR,requiresIfExists:["offset"],data:{_skip:!1}};function pv(n,r,l){return l===void 0&&(l={x:0,y:0}),{top:n.top-r.height-l.y,right:n.right-r.width+l.x,bottom:n.bottom-r.height+l.y,left:n.left-r.width-l.x}}function mv(n){return[Mn,pa,da,wn].some(function(r){return n[r]>=0})}function uR(n){var r=n.state,l=n.name,i=r.rects.reference,u=r.rects.popper,c=r.modifiersData.preventOverflow,d=Bi(r,{elementContext:"reference"}),p=Bi(r,{altBoundary:!0}),m=pv(d,i),h=pv(p,u,c),y=mv(m),x=mv(h);r.modifiersData[l]={referenceClippingOffsets:m,popperEscapeOffsets:h,isReferenceHidden:y,hasPopperEscaped:x},r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-reference-hidden":y,"data-popper-escaped":x})}const cR={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:uR};function fR(n,r,l){var i=La(n),u=[wn,Mn].indexOf(i)>=0?-1:1,c=typeof l=="function"?l(Object.assign({},r,{placement:n})):l,d=c[0],p=c[1];return d=d||0,p=(p||0)*u,[wn,pa].indexOf(i)>=0?{x:p,y:d}:{x:d,y:p}}function dR(n){var r=n.state,l=n.options,i=n.name,u=l.offset,c=u===void 0?[0,0]:u,d=F0.reduce(function(y,x){return y[x]=fR(x,r.rects,c),y},{}),p=d[r.placement],m=p.x,h=p.y;r.modifiersData.popperOffsets!=null&&(r.modifiersData.popperOffsets.x+=m,r.modifiersData.popperOffsets.y+=h),r.modifiersData[i]=d}const pR={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:dR};function mR(n){var r=n.state,l=n.name;r.modifiersData[l]=ab({reference:r.rects.reference,element:r.rects.popper,placement:r.placement})}const hR={name:"popperOffsets",enabled:!0,phase:"read",fn:mR,data:{}};function gR(n){return n==="x"?"y":"x"}function yR(n){var r=n.state,l=n.options,i=n.name,u=l.mainAxis,c=u===void 0?!0:u,d=l.altAxis,p=d===void 0?!1:d,m=l.boundary,h=l.rootBoundary,y=l.altBoundary,x=l.padding,R=l.tether,M=R===void 0?!0:R,E=l.tetherOffset,v=E===void 0?0:E,w=Bi(r,{boundary:m,rootBoundary:h,padding:x,altBoundary:y}),j=La(r.placement),P=pl(r.placement),D=!P,$=vp(j),z=gR($),N=r.modifiersData.popperOffsets,I=r.rects.reference,G=r.rects.popper,K=typeof v=="function"?v(Object.assign({},r.rects,{placement:r.placement})):v,b=typeof K=="number"?{mainAxis:K,altAxis:K}:Object.assign({mainAxis:0,altAxis:0},K),B=r.modifiersData.offset?r.modifiersData.offset[r.placement]:null,V={x:0,y:0};if(N){if(c){var ae,ee=$==="y"?Mn:wn,_=$==="y"?da:pa,T=$==="y"?"height":"width",L=N[$],Y=L+w[ee],X=L-w[_],A=M?-G[T]/2:0,U=P===cl?I[T]:G[T],J=P===cl?-G[T]:-I[T],ne=r.elements.arrow,fe=M&&ne?yp(ne):{width:0,height:0},ue=r.modifiersData["arrow#persistent"]?r.modifiersData["arrow#persistent"].padding:J0(),le=ue[ee],ve=ue[_],xe=Ri(0,I[T],fe[T]),be=D?I[T]/2-A-xe-le-b.mainAxis:U-xe-le-b.mainAxis,me=D?-I[T]/2+A+xe+ve+b.mainAxis:J+xe+ve+b.mainAxis,Re=r.elements.arrow&&Gi(r.elements.arrow),Ne=Re?$==="y"?Re.clientTop||0:Re.clientLeft||0:0,Le=(ae=B?.[$])!=null?ae:0,$e=L+be-Le-Ne,nt=L+me-Le,Ve=Ri(M?Mu(Y,$e):Y,L,M?uo(X,nt):X);N[$]=Ve,V[$]=Ve-L}if(p){var at,he=$==="x"?Mn:wn,st=$==="x"?da:pa,Te=N[z],He=z==="y"?"height":"width",ye=Te+w[he],qt=Te-w[st],ut=[Mn,wn].indexOf(j)!==-1,At=(at=B?.[z])!=null?at:0,rt=ut?ye:Te-I[He]-G[He]-At+b.altAxis,ke=ut?Te+I[He]+G[He]-At-b.altAxis:qt,Ye=M&&ut?UE(rt,Te,ke):Ri(M?rt:ye,Te,M?ke:qt);N[z]=Ye,V[z]=Ye-Te}r.modifiersData[i]=V}}const vR={name:"preventOverflow",enabled:!0,phase:"main",fn:yR,requiresIfExists:["offset"]};function bR(n){return{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}}function SR(n){return n===Vn(n)||!ca(n)?bp(n):bR(n)}function xR(n){var r=n.getBoundingClientRect(),l=fl(r.width)/n.offsetWidth||1,i=fl(r.height)/n.offsetHeight||1;return l!==1||i!==1}function CR(n,r,l){l===void 0&&(l=!1);var i=ca(r),u=ca(r)&&xR(r),c=Lr(r),d=dl(n,u,l),p={scrollLeft:0,scrollTop:0},m={x:0,y:0};return(i||!i&&!l)&&((Ha(r)!=="body"||xp(c))&&(p=SR(r)),ca(r)?(m=dl(r,!0),m.x+=r.clientLeft,m.y+=r.clientTop):c&&(m.x=Sp(c))),{x:d.left+p.scrollLeft-m.x,y:d.top+p.scrollTop-m.y,width:d.width,height:d.height}}function TR(n){var r=new Map,l=new Set,i=[];n.forEach(function(c){r.set(c.name,c)});function u(c){l.add(c.name);var d=[].concat(c.requires||[],c.requiresIfExists||[]);d.forEach(function(p){if(!l.has(p)){var m=r.get(p);m&&u(m)}}),i.push(c)}return n.forEach(function(c){l.has(c.name)||u(c)}),i}function ER(n){var r=TR(n);return NE.reduce(function(l,i){return l.concat(r.filter(function(u){return u.phase===i}))},[])}function RR(n){var r;return function(){return r||(r=new Promise(function(l){Promise.resolve().then(function(){r=void 0,l(n())})})),r}}function OR(n){var r=n.reduce(function(l,i){var u=l[i.name];return l[i.name]=u?Object.assign({},u,i,{options:Object.assign({},u.options,i.options),data:Object.assign({},u.data,i.data)}):i,l},{});return Object.keys(r).map(function(l){return r[l]})}var hv={placement:"bottom",modifiers:[],strategy:"absolute"};function gv(){for(var n=arguments.length,r=new Array(n),l=0;l<n;l++)r[l]=arguments[l];return!r.some(function(i){return!(i&&typeof i.getBoundingClientRect=="function")})}function MR(n){n===void 0&&(n={});var r=n,l=r.defaultModifiers,i=l===void 0?[]:l,u=r.defaultOptions,c=u===void 0?hv:u;return function(p,m,h){h===void 0&&(h=c);var y={placement:"bottom",orderedModifiers:[],options:Object.assign({},hv,c),modifiersData:{},elements:{reference:p,popper:m},attributes:{},styles:{}},x=[],R=!1,M={state:y,setOptions:function(j){var P=typeof j=="function"?j(y.options):j;v(),y.options=Object.assign({},c,y.options,P),y.scrollParents={reference:co(p)?Oi(p):p.contextElement?Oi(p.contextElement):[],popper:Oi(m)};var D=ER(OR([].concat(i,y.options.modifiers)));return y.orderedModifiers=D.filter(function($){return $.enabled}),E(),M.update()},forceUpdate:function(){if(!R){var j=y.elements,P=j.reference,D=j.popper;if(gv(P,D)){y.rects={reference:CR(P,Gi(D),y.options.strategy==="fixed"),popper:yp(D)},y.reset=!1,y.placement=y.options.placement,y.orderedModifiers.forEach(function(b){return y.modifiersData[b.name]=Object.assign({},b.data)});for(var $=0;$<y.orderedModifiers.length;$++){if(y.reset===!0){y.reset=!1,$=-1;continue}var z=y.orderedModifiers[$],N=z.fn,I=z.options,G=I===void 0?{}:I,K=z.name;typeof N=="function"&&(y=N({state:y,options:G,name:K,instance:M})||y)}}}},update:RR(function(){return new Promise(function(w){M.forceUpdate(),w(y)})}),destroy:function(){v(),R=!0}};if(!gv(p,m))return M;M.setOptions(h).then(function(w){!R&&h.onFirstUpdate&&h.onFirstUpdate(w)});function E(){y.orderedModifiers.forEach(function(w){var j=w.name,P=w.options,D=P===void 0?{}:P,$=w.effect;if(typeof $=="function"){var z=$({state:y,name:j,instance:M,options:D}),N=function(){};x.push(z||N)}})}function v(){x.forEach(function(w){return w()}),x=[]}return M}}var wR=[WE,hR,QE,LE,pR,sR,vR,YE,cR],AR=MR({defaultModifiers:wR});function rb(n){const{elementType:r,externalSlotProps:l,ownerState:i,skipResolvingSlotProps:u=!1,...c}=n,d=u?{}:Y0(l,i),{props:p,internalRef:m}=X0({...c,externalSlotProps:d}),h=cn(m,d?.ref,n.additionalProps?.ref);return V0(r,{...p,ref:h},i)}function Xi(n){return parseInt(O.version,10)>=19?n?.props?.ref||null:n?.ref||null}function zR(n){return typeof n=="function"?n():n}const ob=O.forwardRef(function(r,l){const{children:i,container:u,disablePortal:c=!1}=r,[d,p]=O.useState(null),m=cn(O.isValidElement(i)?Xi(i):null,l);if(fa(()=>{c||p(zR(u)||document.body)},[u,c]),fa(()=>{if(d&&!c)return zd(l,d),()=>{zd(l,null)}},[l,d,c]),c){if(O.isValidElement(i)){const h={ref:m};return O.cloneElement(i,h)}return i}return d&&P0.createPortal(i,d)});function DR(n){return Xe("MuiPopper",n)}Ke("MuiPopper",["root"]);function $R(n,r){if(r==="ltr")return n;switch(n){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return n}}function Ld(n){return typeof n=="function"?n():n}function jR(n){return n.nodeType!==void 0}const kR=n=>{const{classes:r}=n;return Qe({root:["root"]},DR,r)},NR={},BR=O.forwardRef(function(r,l){const{anchorEl:i,children:u,direction:c,disablePortal:d,modifiers:p,open:m,placement:h,popperOptions:y,popperRef:x,slotProps:R={},slots:M={},TransitionProps:E,ownerState:v,...w}=r,j=O.useRef(null),P=cn(j,l),D=O.useRef(null),$=cn(D,x),z=O.useRef($);fa(()=>{z.current=$},[$]),O.useImperativeHandle(x,()=>D.current,[]);const N=$R(h,c),[I,G]=O.useState(N),[K,b]=O.useState(Ld(i));O.useEffect(()=>{D.current&&D.current.forceUpdate()}),O.useEffect(()=>{i&&b(Ld(i))},[i]),fa(()=>{if(!K||!m)return;const _=Y=>{G(Y.placement)};let T=[{name:"preventOverflow",options:{altBoundary:d}},{name:"flip",options:{altBoundary:d}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:Y})=>{_(Y)}}];p!=null&&(T=T.concat(p)),y&&y.modifiers!=null&&(T=T.concat(y.modifiers));const L=AR(K,j.current,{placement:N,...y,modifiers:T});return z.current(L),()=>{L.destroy(),z.current(null)}},[K,d,p,m,y,N]);const B={placement:I};E!==null&&(B.TransitionProps=E);const V=kR(r),ae=M.root??"div",ee=rb({elementType:ae,externalSlotProps:R.root,externalForwardedProps:w,additionalProps:{role:"tooltip",ref:P},ownerState:r,className:V.root});return S.jsx(ae,{...ee,children:typeof u=="function"?u(B):u})}),_R=O.forwardRef(function(r,l){const{anchorEl:i,children:u,container:c,direction:d="ltr",disablePortal:p=!1,keepMounted:m=!1,modifiers:h,open:y,placement:x="bottom",popperOptions:R=NR,popperRef:M,style:E,transition:v=!1,slotProps:w={},slots:j={},...P}=r,[D,$]=O.useState(!0),z=()=>{$(!1)},N=()=>{$(!0)};if(!m&&!y&&(!v||D))return null;let I;if(c)I=c;else if(i){const b=Ld(i);I=b&&jR(b)?In(b).body:In(null).body}const G=!y&&m&&(!v||D)?"none":void 0,K=v?{in:y,onEnter:z,onExited:N}:void 0;return S.jsx(ob,{disablePortal:p,container:I,children:S.jsx(BR,{anchorEl:i,direction:d,disablePortal:p,modifiers:h,ref:l,open:v?!D:y,placement:x,popperOptions:R,popperRef:M,slotProps:w,slots:j,...P,style:{position:"fixed",top:0,left:0,display:G,...E},TransitionProps:K,children:u})})}),LR=pe(_R,{name:"MuiPopper",slot:"Root"})({}),lb=O.forwardRef(function(r,l){const i=R0(),u=Je({props:r,name:"MuiPopper"}),{anchorEl:c,component:d,components:p,componentsProps:m,container:h,disablePortal:y,keepMounted:x,modifiers:R,open:M,placement:E,popperOptions:v,popperRef:w,transition:j,slots:P,slotProps:D,...$}=u,z=P?.root??p?.Root,N={anchorEl:c,container:h,disablePortal:y,keepMounted:x,modifiers:R,open:M,placement:E,popperOptions:v,popperRef:w,transition:j,...$};return S.jsx(LR,{as:d,direction:i?"rtl":"ltr",slots:{root:z},slotProps:D??m,...N,ref:l})});function HR(n){return Xe("MuiListSubheader",n)}Ke("MuiListSubheader",["root","colorPrimary","colorInherit","gutters","inset","sticky"]);const PR=n=>{const{classes:r,color:l,disableGutters:i,inset:u,disableSticky:c}=n,d={root:["root",l!=="default"&&`color${de(l)}`,!i&&"gutters",u&&"inset",!c&&"sticky"]};return Qe(d,HR,r)},UR=pe("li",{name:"MuiListSubheader",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.color!=="default"&&r[`color${de(l.color)}`],!l.disableGutters&&r.gutters,l.inset&&r.inset,!l.disableSticky&&r.sticky]}})(tt(({theme:n})=>({boxSizing:"border-box",lineHeight:"48px",listStyle:"none",color:(n.vars||n).palette.text.secondary,fontFamily:n.typography.fontFamily,fontWeight:n.typography.fontWeightMedium,fontSize:n.typography.pxToRem(14),variants:[{props:{color:"primary"},style:{color:(n.vars||n).palette.primary.main}},{props:{color:"inherit"},style:{color:"inherit"}},{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:r})=>r.inset,style:{paddingLeft:72}},{props:({ownerState:r})=>!r.disableSticky,style:{position:"sticky",top:0,zIndex:1,backgroundColor:(n.vars||n).palette.background.paper}}]}))),Hd=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiListSubheader"}),{className:u,color:c="default",component:d="li",disableGutters:p=!1,disableSticky:m=!1,inset:h=!1,...y}=i,x={...i,color:c,component:d,disableGutters:p,disableSticky:m,inset:h},R=PR(x);return S.jsx(UR,{as:d,className:Me(R.root,u),ref:l,ownerState:x,...y})});Hd&&(Hd.muiSkipListHighlight=!0);const qR=fn(S.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}));function IR(n){return Xe("MuiChip",n)}const Ze=Ke("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),VR=n=>{const{classes:r,disabled:l,size:i,color:u,iconColor:c,onDelete:d,clickable:p,variant:m}=n,h={root:["root",m,l&&"disabled",`size${de(i)}`,`color${de(u)}`,p&&"clickable",p&&`clickableColor${de(u)}`,d&&"deletable",d&&`deletableColor${de(u)}`,`${m}${de(u)}`],label:["label",`label${de(i)}`],avatar:["avatar",`avatar${de(i)}`,`avatarColor${de(u)}`],icon:["icon",`icon${de(i)}`,`iconColor${de(c)}`],deleteIcon:["deleteIcon",`deleteIcon${de(i)}`,`deleteIconColor${de(u)}`,`deleteIcon${de(m)}Color${de(u)}`]};return Qe(h,IR,r)},YR=pe("div",{name:"MuiChip",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n,{color:i,iconColor:u,clickable:c,onDelete:d,size:p,variant:m}=l;return[{[`& .${Ze.avatar}`]:r.avatar},{[`& .${Ze.avatar}`]:r[`avatar${de(p)}`]},{[`& .${Ze.avatar}`]:r[`avatarColor${de(i)}`]},{[`& .${Ze.icon}`]:r.icon},{[`& .${Ze.icon}`]:r[`icon${de(p)}`]},{[`& .${Ze.icon}`]:r[`iconColor${de(u)}`]},{[`& .${Ze.deleteIcon}`]:r.deleteIcon},{[`& .${Ze.deleteIcon}`]:r[`deleteIcon${de(p)}`]},{[`& .${Ze.deleteIcon}`]:r[`deleteIconColor${de(i)}`]},{[`& .${Ze.deleteIcon}`]:r[`deleteIcon${de(m)}Color${de(i)}`]},r.root,r[`size${de(p)}`],r[`color${de(i)}`],c&&r.clickable,c&&i!=="default"&&r[`clickableColor${de(i)})`],d&&r.deletable,d&&i!=="default"&&r[`deletableColor${de(i)}`],r[m],r[`${m}${de(i)}`]]}})(tt(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[700]:n.palette.grey[300];return{maxWidth:"100%",fontFamily:n.typography.fontFamily,fontSize:n.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,lineHeight:1.5,color:(n.vars||n).palette.text.primary,backgroundColor:(n.vars||n).palette.action.selected,borderRadius:32/2,whiteSpace:"nowrap",transition:n.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${Ze.disabled}`]:{opacity:(n.vars||n).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${Ze.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:n.vars?n.vars.palette.Chip.defaultAvatarColor:r,fontSize:n.typography.pxToRem(12)},[`& .${Ze.avatarColorPrimary}`]:{color:(n.vars||n).palette.primary.contrastText,backgroundColor:(n.vars||n).palette.primary.dark},[`& .${Ze.avatarColorSecondary}`]:{color:(n.vars||n).palette.secondary.contrastText,backgroundColor:(n.vars||n).palette.secondary.dark},[`& .${Ze.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:n.typography.pxToRem(10)},[`& .${Ze.icon}`]:{marginLeft:5,marginRight:-6},[`& .${Ze.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:n.vars?`rgba(${n.vars.palette.text.primaryChannel} / 0.26)`:yt(n.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:n.vars?`rgba(${n.vars.palette.text.primaryChannel} / 0.4)`:yt(n.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${Ze.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${Ze.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(n.palette).filter(xn(["contrastText"])).map(([l])=>({props:{color:l},style:{backgroundColor:(n.vars||n).palette[l].main,color:(n.vars||n).palette[l].contrastText,[`& .${Ze.deleteIcon}`]:{color:n.vars?`rgba(${n.vars.palette[l].contrastTextChannel} / 0.7)`:yt(n.palette[l].contrastText,.7),"&:hover, &:active":{color:(n.vars||n).palette[l].contrastText}}}})),{props:l=>l.iconColor===l.color,style:{[`& .${Ze.icon}`]:{color:n.vars?n.vars.palette.Chip.defaultIconColor:r}}},{props:l=>l.iconColor===l.color&&l.color!=="default",style:{[`& .${Ze.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${Ze.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:yt(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)}}},...Object.entries(n.palette).filter(xn(["dark"])).map(([l])=>({props:{color:l,onDelete:!0},style:{[`&.${Ze.focusVisible}`]:{background:(n.vars||n).palette[l].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:yt(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity)},[`&.${Ze.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:yt(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)},"&:active":{boxShadow:(n.vars||n).shadows[1]}}},...Object.entries(n.palette).filter(xn(["dark"])).map(([l])=>({props:{color:l,clickable:!0},style:{[`&:hover, &.${Ze.focusVisible}`]:{backgroundColor:(n.vars||n).palette[l].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:n.vars?`1px solid ${n.vars.palette.Chip.defaultBorder}`:`1px solid ${n.palette.mode==="light"?n.palette.grey[400]:n.palette.grey[700]}`,[`&.${Ze.clickable}:hover`]:{backgroundColor:(n.vars||n).palette.action.hover},[`&.${Ze.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},[`& .${Ze.avatar}`]:{marginLeft:4},[`& .${Ze.avatarSmall}`]:{marginLeft:2},[`& .${Ze.icon}`]:{marginLeft:4},[`& .${Ze.iconSmall}`]:{marginLeft:2},[`& .${Ze.deleteIcon}`]:{marginRight:5},[`& .${Ze.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(n.palette).filter(xn()).map(([l])=>({props:{variant:"outlined",color:l},style:{color:(n.vars||n).palette[l].main,border:`1px solid ${n.vars?`rgba(${n.vars.palette[l].mainChannel} / 0.7)`:yt(n.palette[l].main,.7)}`,[`&.${Ze.clickable}:hover`]:{backgroundColor:n.vars?`rgba(${n.vars.palette[l].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette[l].main,n.palette.action.hoverOpacity)},[`&.${Ze.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette[l].mainChannel} / ${n.vars.palette.action.focusOpacity})`:yt(n.palette[l].main,n.palette.action.focusOpacity)},[`& .${Ze.deleteIcon}`]:{color:n.vars?`rgba(${n.vars.palette[l].mainChannel} / 0.7)`:yt(n.palette[l].main,.7),"&:hover, &:active":{color:(n.vars||n).palette[l].main}}}}))]}})),GR=pe("span",{name:"MuiChip",slot:"Label",overridesResolver:(n,r)=>{const{ownerState:l}=n,{size:i}=l;return[r.label,r[`label${de(i)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function yv(n){return n.key==="Backspace"||n.key==="Delete"}const XR=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiChip"}),{avatar:u,className:c,clickable:d,color:p="default",component:m,deleteIcon:h,disabled:y=!1,icon:x,label:R,onClick:M,onDelete:E,onKeyDown:v,onKeyUp:w,size:j="medium",variant:P="filled",tabIndex:D,skipFocusWhenDisabled:$=!1,slots:z={},slotProps:N={},...I}=i,G=O.useRef(null),K=cn(G,l),b=le=>{le.stopPropagation(),E&&E(le)},B=le=>{le.currentTarget===le.target&&yv(le)&&le.preventDefault(),v&&v(le)},V=le=>{le.currentTarget===le.target&&E&&yv(le)&&E(le),w&&w(le)},ae=d!==!1&&M?!0:d,ee=ae||E?ki:m||"div",_={...i,component:ee,disabled:y,size:j,color:p,iconColor:O.isValidElement(x)&&x.props.color||p,onDelete:!!E,clickable:ae,variant:P},T=VR(_),L=ee===ki?{component:m||"div",focusVisibleClassName:T.focusVisible,...E&&{disableRipple:!0}}:{};let Y=null;E&&(Y=h&&O.isValidElement(h)?O.cloneElement(h,{className:Me(h.props.className,T.deleteIcon),onClick:b}):S.jsx(qR,{className:T.deleteIcon,onClick:b}));let X=null;u&&O.isValidElement(u)&&(X=O.cloneElement(u,{className:Me(T.avatar,u.props.className)}));let A=null;x&&O.isValidElement(x)&&(A=O.cloneElement(x,{className:Me(T.icon,x.props.className)}));const U={slots:z,slotProps:N},[J,ne]=wt("root",{elementType:YR,externalForwardedProps:{...U,...I},ownerState:_,shouldForwardComponentProp:!0,ref:K,className:Me(T.root,c),additionalProps:{disabled:ae&&y?!0:void 0,tabIndex:$&&y?-1:D,...L},getSlotProps:le=>({...le,onClick:ve=>{le.onClick?.(ve),M?.(ve)},onKeyDown:ve=>{le.onKeyDown?.(ve),B?.(ve)},onKeyUp:ve=>{le.onKeyUp?.(ve),V?.(ve)}})}),[fe,ue]=wt("label",{elementType:GR,externalForwardedProps:U,ownerState:_,className:T.label});return S.jsxs(J,{as:ee,...ne,children:[X||A,S.jsx(fe,{...ue,children:R}),Y]})});function su(n){return parseInt(n,10)||0}const KR={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function QR(n){for(const r in n)return!1;return!0}function vv(n){return QR(n)||n.outerHeightStyle===0&&!n.overflowing}const FR=O.forwardRef(function(r,l){const{onChange:i,maxRows:u,minRows:c=1,style:d,value:p,...m}=r,{current:h}=O.useRef(p!=null),y=O.useRef(null),x=cn(l,y),R=O.useRef(null),M=O.useRef(null),E=O.useCallback(()=>{const D=y.current,$=M.current;if(!D||!$)return;const N=ir(D).getComputedStyle(D);if(N.width==="0px")return{outerHeightStyle:0,overflowing:!1};$.style.width=N.width,$.value=D.value||r.placeholder||"x",$.value.slice(-1)===`
`&&($.value+=" ");const I=N.boxSizing,G=su(N.paddingBottom)+su(N.paddingTop),K=su(N.borderBottomWidth)+su(N.borderTopWidth),b=$.scrollHeight;$.value="x";const B=$.scrollHeight;let V=b;c&&(V=Math.max(Number(c)*B,V)),u&&(V=Math.min(Number(u)*B,V)),V=Math.max(V,B);const ae=V+(I==="border-box"?G+K:0),ee=Math.abs(V-b)<=1;return{outerHeightStyle:ae,overflowing:ee}},[u,c,r.placeholder]),v=Ca(()=>{const D=y.current,$=E();if(!D||!$||vv($))return!1;const z=$.outerHeightStyle;return R.current!=null&&R.current!==z}),w=O.useCallback(()=>{const D=y.current,$=E();if(!D||!$||vv($))return;const z=$.outerHeightStyle;R.current!==z&&(R.current=z,D.style.height=`${z}px`),D.style.overflow=$.overflowing?"hidden":""},[E]),j=O.useRef(-1);fa(()=>{const D=B0(w),$=y?.current;if(!$)return;const z=ir($);z.addEventListener("resize",D);let N;return typeof ResizeObserver<"u"&&(N=new ResizeObserver(()=>{v()&&(N.unobserve($),cancelAnimationFrame(j.current),w(),j.current=requestAnimationFrame(()=>{N.observe($)}))}),N.observe($)),()=>{D.clear(),cancelAnimationFrame(j.current),z.removeEventListener("resize",D),N&&N.disconnect()}},[E,w,v]),fa(()=>{w()});const P=D=>{h||w();const $=D.target,z=$.value.length,N=$.value.endsWith(`
`),I=$.selectionStart===z;N&&I&&$.setSelectionRange(z,z),i&&i(D)};return S.jsxs(O.Fragment,{children:[S.jsx("textarea",{value:p,onChange:P,ref:x,rows:c,style:d,...m}),S.jsx("textarea",{"aria-hidden":!0,className:r.className,readOnly:!0,ref:M,tabIndex:-1,style:{...KR.shadow,...d,paddingTop:0,paddingBottom:0}})]})});function Pd(n){return typeof n=="string"}function fo({props:n,states:r,muiFormControl:l}){return r.reduce((i,u)=>(i[u]=n[u],l&&typeof n[u]>"u"&&(i[u]=l[u]),i),{})}const Cp=O.createContext(void 0);function Hr(){return O.useContext(Cp)}function bv(n){return n!=null&&!(Array.isArray(n)&&n.length===0)}function wu(n,r=!1){return n&&(bv(n.value)&&n.value!==""||r&&bv(n.defaultValue)&&n.defaultValue!=="")}function WR(n){return n.startAdornment}function ZR(n){return Xe("MuiInputBase",n)}const Hn=Ke("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]);var Sv;const Ku=(n,r)=>{const{ownerState:l}=n;return[r.root,l.formControl&&r.formControl,l.startAdornment&&r.adornedStart,l.endAdornment&&r.adornedEnd,l.error&&r.error,l.size==="small"&&r.sizeSmall,l.multiline&&r.multiline,l.color&&r[`color${de(l.color)}`],l.fullWidth&&r.fullWidth,l.hiddenLabel&&r.hiddenLabel]},Qu=(n,r)=>{const{ownerState:l}=n;return[r.input,l.size==="small"&&r.inputSizeSmall,l.multiline&&r.inputMultiline,l.type==="search"&&r.inputTypeSearch,l.startAdornment&&r.inputAdornedStart,l.endAdornment&&r.inputAdornedEnd,l.hiddenLabel&&r.inputHiddenLabel]},JR=n=>{const{classes:r,color:l,disabled:i,error:u,endAdornment:c,focused:d,formControl:p,fullWidth:m,hiddenLabel:h,multiline:y,readOnly:x,size:R,startAdornment:M,type:E}=n,v={root:["root",`color${de(l)}`,i&&"disabled",u&&"error",m&&"fullWidth",d&&"focused",p&&"formControl",R&&R!=="medium"&&`size${de(R)}`,y&&"multiline",M&&"adornedStart",c&&"adornedEnd",h&&"hiddenLabel",x&&"readOnly"],input:["input",i&&"disabled",E==="search"&&"inputTypeSearch",y&&"inputMultiline",R==="small"&&"inputSizeSmall",h&&"inputHiddenLabel",M&&"inputAdornedStart",c&&"inputAdornedEnd",x&&"readOnly"]};return Qe(v,ZR,r)},Fu=pe("div",{name:"MuiInputBase",slot:"Root",overridesResolver:Ku})(tt(({theme:n})=>({...n.typography.body1,color:(n.vars||n).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${Hn.disabled}`]:{color:(n.vars||n).palette.text.disabled,cursor:"default"},variants:[{props:({ownerState:r})=>r.multiline,style:{padding:"4px 0 5px"}},{props:({ownerState:r,size:l})=>r.multiline&&l==="small",style:{paddingTop:1}},{props:({ownerState:r})=>r.fullWidth,style:{width:"100%"}}]}))),Wu=pe("input",{name:"MuiInputBase",slot:"Input",overridesResolver:Qu})(tt(({theme:n})=>{const r=n.palette.mode==="light",l={color:"currentColor",...n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5},transition:n.transitions.create("opacity",{duration:n.transitions.duration.shorter})},i={opacity:"0 !important"},u=n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":l,"&::-moz-placeholder":l,"&::-ms-input-placeholder":l,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${Hn.formControl} &`]:{"&::-webkit-input-placeholder":i,"&::-moz-placeholder":i,"&::-ms-input-placeholder":i,"&:focus::-webkit-input-placeholder":u,"&:focus::-moz-placeholder":u,"&:focus::-ms-input-placeholder":u},[`&.${Hn.disabled}`]:{opacity:1,WebkitTextFillColor:(n.vars||n).palette.text.disabled},variants:[{props:({ownerState:c})=>!c.disableInjectingGlobalStyles,style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:({ownerState:c})=>c.multiline,style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}})),xv=fp({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),Tp=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiInputBase"}),{"aria-describedby":u,autoComplete:c,autoFocus:d,className:p,color:m,components:h={},componentsProps:y={},defaultValue:x,disabled:R,disableInjectingGlobalStyles:M,endAdornment:E,error:v,fullWidth:w=!1,id:j,inputComponent:P="input",inputProps:D={},inputRef:$,margin:z,maxRows:N,minRows:I,multiline:G=!1,name:K,onBlur:b,onChange:B,onClick:V,onFocus:ae,onKeyDown:ee,onKeyUp:_,placeholder:T,readOnly:L,renderSuffix:Y,rows:X,size:A,slotProps:U={},slots:J={},startAdornment:ne,type:fe="text",value:ue,...le}=i,ve=D.value!=null?D.value:ue,{current:xe}=O.useRef(ve!=null),be=O.useRef(),me=O.useCallback(Ae=>{},[]),Re=cn(be,$,D.ref,me),[Ne,Le]=O.useState(!1),$e=Hr(),nt=fo({props:i,muiFormControl:$e,states:["color","disabled","error","hiddenLabel","size","required","filled"]});nt.focused=$e?$e.focused:Ne,O.useEffect(()=>{!$e&&R&&Ne&&(Le(!1),b&&b())},[$e,R,Ne,b]);const Ve=$e&&$e.onFilled,at=$e&&$e.onEmpty,he=O.useCallback(Ae=>{wu(Ae)?Ve&&Ve():at&&at()},[Ve,at]);fa(()=>{xe&&he({value:ve})},[ve,he,xe]);const st=Ae=>{ae&&ae(Ae),D.onFocus&&D.onFocus(Ae),$e&&$e.onFocus?$e.onFocus(Ae):Le(!0)},Te=Ae=>{b&&b(Ae),D.onBlur&&D.onBlur(Ae),$e&&$e.onBlur?$e.onBlur(Ae):Le(!1)},He=(Ae,...Gt)=>{if(!xe){const Cn=Ae.target||be.current;if(Cn==null)throw new Error(or(1));he({value:Cn.value})}D.onChange&&D.onChange(Ae,...Gt),B&&B(Ae,...Gt)};O.useEffect(()=>{he(be.current)},[]);const ye=Ae=>{be.current&&Ae.currentTarget===Ae.target&&be.current.focus(),V&&V(Ae)};let qt=P,ut=D;G&&qt==="input"&&(X?ut={type:void 0,minRows:X,maxRows:X,...ut}:ut={type:void 0,maxRows:N,minRows:I,...ut},qt=FR);const At=Ae=>{he(Ae.animationName==="mui-auto-fill-cancel"?be.current:{value:"x"})};O.useEffect(()=>{$e&&$e.setAdornedStart(!!ne)},[$e,ne]);const rt={...i,color:nt.color||"primary",disabled:nt.disabled,endAdornment:E,error:nt.error,focused:nt.focused,formControl:$e,fullWidth:w,hiddenLabel:nt.hiddenLabel,multiline:G,size:nt.size,startAdornment:ne,type:fe},ke=JR(rt),Ye=J.root||h.Root||Fu,et=U.root||y.root||{},vt=J.input||h.Input||Wu;return ut={...ut,...U.input??y.input},S.jsxs(O.Fragment,{children:[!M&&typeof xv=="function"&&(Sv||(Sv=S.jsx(xv,{}))),S.jsxs(Ye,{...et,ref:l,onClick:ye,...le,...!Pd(Ye)&&{ownerState:{...rt,...et.ownerState}},className:Me(ke.root,et.className,p,L&&"MuiInputBase-readOnly"),children:[ne,S.jsx(Cp.Provider,{value:null,children:S.jsx(vt,{"aria-invalid":nt.error,"aria-describedby":u,autoComplete:c,autoFocus:d,defaultValue:x,disabled:nt.disabled,id:j,onAnimationStart:At,name:K,placeholder:T,readOnly:L,required:nt.required,rows:X,value:ve,onKeyDown:ee,onKeyUp:_,type:fe,...ut,...!Pd(vt)&&{as:qt,ownerState:{...rt,...ut.ownerState}},ref:Re,className:Me(ke.input,ut.className,L&&"MuiInputBase-readOnly"),onBlur:Te,onChange:He,onFocus:st})}),E,Y?Y({...nt,startAdornment:ne}):null]})]})});function eO(n){return Xe("MuiInput",n)}const kr={...Hn,...Ke("MuiInput",["root","underline","input"])};function tO(n){return Xe("MuiOutlinedInput",n)}const ia={...Hn,...Ke("MuiOutlinedInput",["root","notchedOutline","input"])};function nO(n){return Xe("MuiFilledInput",n)}const Pn={...Hn,...Ke("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])},ib=fn(S.jsx("path",{d:"M7 10l5 5 5-5z"}));function aO(n){return Xe("MuiAutocomplete",n)}const qe=Ke("MuiAutocomplete",["root","expanded","fullWidth","focused","focusVisible","tag","tagSizeSmall","tagSizeMedium","hasPopupIcon","hasClearIcon","inputRoot","input","inputFocused","endAdornment","clearIndicator","popupIndicator","popupIndicatorOpen","popper","popperDisablePortal","paper","listbox","loading","noOptions","option","groupLabel","groupUl"]);var Cv,Tv;const rO=n=>{const{classes:r,disablePortal:l,expanded:i,focused:u,fullWidth:c,hasClearIcon:d,hasPopupIcon:p,inputFocused:m,popupOpen:h,size:y}=n,x={root:["root",i&&"expanded",u&&"focused",c&&"fullWidth",d&&"hasClearIcon",p&&"hasPopupIcon"],inputRoot:["inputRoot"],input:["input",m&&"inputFocused"],tag:["tag",`tagSize${de(y)}`],endAdornment:["endAdornment"],clearIndicator:["clearIndicator"],popupIndicator:["popupIndicator",h&&"popupIndicatorOpen"],popper:["popper",l&&"popperDisablePortal"],paper:["paper"],listbox:["listbox"],loading:["loading"],noOptions:["noOptions"],option:["option"],groupLabel:["groupLabel"],groupUl:["groupUl"]};return Qe(x,aO,r)},oO=pe("div",{name:"MuiAutocomplete",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n,{fullWidth:i,hasClearIcon:u,hasPopupIcon:c,inputFocused:d,size:p}=l;return[{[`& .${qe.tag}`]:r.tag},{[`& .${qe.tag}`]:r[`tagSize${de(p)}`]},{[`& .${qe.inputRoot}`]:r.inputRoot},{[`& .${qe.input}`]:r.input},{[`& .${qe.input}`]:d&&r.inputFocused},r.root,i&&r.fullWidth,c&&r.hasPopupIcon,u&&r.hasClearIcon]}})({[`&.${qe.focused} .${qe.clearIndicator}`]:{visibility:"visible"},"@media (pointer: fine)":{[`&:hover .${qe.clearIndicator}`]:{visibility:"visible"}},[`& .${qe.tag}`]:{margin:3,maxWidth:"calc(100% - 6px)"},[`& .${qe.inputRoot}`]:{[`.${qe.hasPopupIcon}&, .${qe.hasClearIcon}&`]:{paddingRight:30},[`.${qe.hasPopupIcon}.${qe.hasClearIcon}&`]:{paddingRight:56},[`& .${qe.input}`]:{width:0,minWidth:30}},[`& .${kr.root}`]:{paddingBottom:1,"& .MuiInput-input":{padding:"4px 4px 4px 0px"}},[`& .${kr.root}.${Hn.sizeSmall}`]:{[`& .${kr.input}`]:{padding:"2px 4px 3px 0"}},[`& .${ia.root}`]:{padding:9,[`.${qe.hasPopupIcon}&, .${qe.hasClearIcon}&`]:{paddingRight:39},[`.${qe.hasPopupIcon}.${qe.hasClearIcon}&`]:{paddingRight:65},[`& .${qe.input}`]:{padding:"7.5px 4px 7.5px 5px"},[`& .${qe.endAdornment}`]:{right:9}},[`& .${ia.root}.${Hn.sizeSmall}`]:{paddingTop:6,paddingBottom:6,paddingLeft:6,[`& .${qe.input}`]:{padding:"2.5px 4px 2.5px 8px"}},[`& .${Pn.root}`]:{paddingTop:19,paddingLeft:8,[`.${qe.hasPopupIcon}&, .${qe.hasClearIcon}&`]:{paddingRight:39},[`.${qe.hasPopupIcon}.${qe.hasClearIcon}&`]:{paddingRight:65},[`& .${Pn.input}`]:{padding:"7px 4px"},[`& .${qe.endAdornment}`]:{right:9}},[`& .${Pn.root}.${Hn.sizeSmall}`]:{paddingBottom:1,[`& .${Pn.input}`]:{padding:"2.5px 4px"}},[`& .${Hn.hiddenLabel}`]:{paddingTop:8},[`& .${Pn.root}.${Hn.hiddenLabel}`]:{paddingTop:0,paddingBottom:0,[`& .${qe.input}`]:{paddingTop:16,paddingBottom:17}},[`& .${Pn.root}.${Hn.hiddenLabel}.${Hn.sizeSmall}`]:{[`& .${qe.input}`]:{paddingTop:8,paddingBottom:9}},[`& .${qe.input}`]:{flexGrow:1,textOverflow:"ellipsis",opacity:0},variants:[{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{[`& .${qe.tag}`]:{margin:2,maxWidth:"calc(100% - 4px)"}}},{props:{inputFocused:!0},style:{[`& .${qe.input}`]:{opacity:1}}},{props:{multiple:!0},style:{[`& .${qe.inputRoot}`]:{flexWrap:"wrap"}}}]}),lO=pe("div",{name:"MuiAutocomplete",slot:"EndAdornment"})({position:"absolute",right:0,top:"50%",transform:"translate(0, -50%)"}),iO=pe(rl,{name:"MuiAutocomplete",slot:"ClearIndicator"})({marginRight:-2,padding:4,visibility:"hidden"}),sO=pe(rl,{name:"MuiAutocomplete",slot:"PopupIndicator",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.popupIndicator,l.popupOpen&&r.popupIndicatorOpen]}})({padding:2,marginRight:-2,variants:[{props:{popupOpen:!0},style:{transform:"rotate(180deg)"}}]}),uO=pe(lb,{name:"MuiAutocomplete",slot:"Popper",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[{[`& .${qe.option}`]:r.option},r.popper,l.disablePortal&&r.popperDisablePortal]}})(tt(({theme:n})=>({zIndex:(n.vars||n).zIndex.modal,variants:[{props:{disablePortal:!0},style:{position:"absolute"}}]}))),cO=pe(hn,{name:"MuiAutocomplete",slot:"Paper"})(tt(({theme:n})=>({...n.typography.body1,overflow:"auto"}))),fO=pe("div",{name:"MuiAutocomplete",slot:"Loading"})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,padding:"14px 16px"}))),dO=pe("div",{name:"MuiAutocomplete",slot:"NoOptions"})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,padding:"14px 16px"}))),pO=pe("ul",{name:"MuiAutocomplete",slot:"Listbox"})(tt(({theme:n})=>({listStyle:"none",margin:0,padding:"8px 0",maxHeight:"40vh",overflow:"auto",position:"relative",[`& .${qe.option}`]:{minHeight:48,display:"flex",overflow:"hidden",justifyContent:"flex-start",alignItems:"center",cursor:"pointer",paddingTop:6,boxSizing:"border-box",outline:"0",WebkitTapHighlightColor:"transparent",paddingBottom:6,paddingLeft:16,paddingRight:16,[n.breakpoints.up("sm")]:{minHeight:"auto"},[`&.${qe.focused}`]:{backgroundColor:(n.vars||n).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},'&[aria-disabled="true"]':{opacity:(n.vars||n).palette.action.disabledOpacity,pointerEvents:"none"},[`&.${qe.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},'&[aria-selected="true"]':{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:yt(n.palette.primary.main,n.palette.action.selectedOpacity),[`&.${qe.focused}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:yt(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:(n.vars||n).palette.action.selected}},[`&.${qe.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:yt(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)}}}}))),mO=pe(Hd,{name:"MuiAutocomplete",slot:"GroupLabel"})(tt(({theme:n})=>({backgroundColor:(n.vars||n).palette.background.paper,top:-8}))),hO=pe("ul",{name:"MuiAutocomplete",slot:"GroupUl"})({padding:0,[`& .${qe.option}`]:{paddingLeft:24}}),nl=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiAutocomplete"}),{autoComplete:u=!1,autoHighlight:c=!1,autoSelect:d=!1,blurOnSelect:p=!1,ChipProps:m,className:h,clearIcon:y=Cv||(Cv=S.jsx(dE,{fontSize:"small"})),clearOnBlur:x=!i.freeSolo,clearOnEscape:R=!1,clearText:M="Clear",closeText:E="Close",componentsProps:v,defaultValue:w=i.multiple?[]:null,disableClearable:j=!1,disableCloseOnSelect:P=!1,disabled:D=!1,disabledItemsFocusable:$=!1,disableListWrap:z=!1,disablePortal:N=!1,filterOptions:I,filterSelectedOptions:G=!1,forcePopupIcon:K="auto",freeSolo:b=!1,fullWidth:B=!1,getLimitTagsText:V=lt=>`+${lt}`,getOptionDisabled:ae,getOptionKey:ee,getOptionLabel:_,isOptionEqualToValue:T,groupBy:L,handleHomeEndKeys:Y=!i.freeSolo,id:X,includeInputInList:A=!1,inputValue:U,limitTags:J=-1,ListboxComponent:ne,ListboxProps:fe,loading:ue=!1,loadingText:le="Loading…",multiple:ve=!1,noOptionsText:xe="No options",onChange:be,onClose:me,onHighlightChange:Re,onInputChange:Ne,onOpen:Le,open:$e,openOnFocus:nt=!1,openText:Ve="Open",options:at,PaperComponent:he,PopperComponent:st,popupIcon:Te=Tv||(Tv=S.jsx(ib,{})),readOnly:He=!1,renderGroup:ye,renderInput:qt,renderOption:ut,renderTags:At,renderValue:rt,selectOnFocus:ke=!i.freeSolo,size:Ye="medium",slots:et={},slotProps:vt={},value:Ae,...Gt}=i,{getRootProps:Cn,getInputProps:Zt,getInputLabelProps:Ce,getPopupIndicatorProps:Ue,getClearProps:ot,getItemProps:Gn,getListboxProps:ha,getOptionProps:po,value:tn,dirty:zn,expanded:ga,id:Ua,popupOpen:Dn,focused:ur,focusedItem:cr,anchorEl:Ea,setAnchorEl:yl,inputValue:Xn,groupedOptions:zt}=TE({...i,componentName:"Autocomplete"}),Xt=!j&&!D&&zn&&!He,Kn=(!b||K===!0)&&K!==!1,{onMouseDown:Pr}=Zt(),{ref:vl,...mo}=ha(),Qn=_||(lt=>lt.label??lt),Bt={...i,disablePortal:N,expanded:ga,focused:ur,fullWidth:B,getOptionLabel:Qn,hasClearIcon:Xt,hasPopupIcon:Kn,inputFocused:cr===-1,popupOpen:Dn,size:Ye},Mt=rO(Bt),yn={slots:{paper:he,popper:st,...et},slotProps:{chip:m,listbox:fe,...v,...vt}},[ya,va]=wt("listbox",{elementType:pO,externalForwardedProps:yn,ownerState:Bt,className:Mt.listbox,additionalProps:mo,ref:vl}),[te,re]=wt("paper",{elementType:hn,externalForwardedProps:yn,ownerState:Bt,className:Mt.paper}),[ge,Be]=wt("popper",{elementType:lb,externalForwardedProps:yn,ownerState:Bt,className:Mt.popper,additionalProps:{disablePortal:N,style:{width:Ea?Ea.clientWidth:null},role:"presentation",anchorEl:Ea,open:Dn}});let Ee;const Dt=lt=>({className:Mt.tag,disabled:D,...Gn(lt)});if(ve?tn.length>0&&(At?Ee=At(tn,Dt,Bt):rt?Ee=rt(tn,Dt,Bt):Ee=tn.map((lt,bn)=>{const{key:Wn,...qa}=Dt({index:bn});return S.jsx(XR,{label:Qn(lt),size:Ye,...qa,...yn.slotProps.chip},Wn)})):rt&&tn!=null&&(Ee=rt(tn,Dt,Bt)),J>-1&&Array.isArray(Ee)){const lt=Ee.length-J;!ur&&lt>0&&(Ee=Ee.splice(0,J),Ee.push(S.jsx("span",{className:Mt.tag,children:V(lt)},Ee.length)))}const vn=ye||(lt=>S.jsxs("li",{children:[S.jsx(mO,{className:Mt.groupLabel,ownerState:Bt,component:"div",children:lt.group}),S.jsx(hO,{className:Mt.groupUl,ownerState:Bt,children:lt.children})]},lt.key)),ho=ut||((lt,bn)=>{const{key:Wn,...qa}=lt;return S.jsx("li",{...qa,children:Qn(bn)},Wn)}),Ur=(lt,bn)=>{const Wn=po({option:lt,index:bn});return ho({...Wn,className:Mt.option},lt,{selected:Wn["aria-selected"],index:bn,inputValue:Xn},Bt)},Fn=yn.slotProps.clearIndicator,go=yn.slotProps.popupIndicator;return S.jsxs(O.Fragment,{children:[S.jsx(oO,{ref:l,className:Me(Mt.root,h),ownerState:Bt,...Cn(Gt),children:qt({id:Ua,disabled:D,fullWidth:!0,size:Ye==="small"?"small":void 0,InputLabelProps:Ce(),InputProps:{ref:yl,className:Mt.inputRoot,startAdornment:Ee,onMouseDown:lt=>{lt.target===lt.currentTarget&&Pr(lt)},...(Xt||Kn)&&{endAdornment:S.jsxs(lO,{className:Mt.endAdornment,ownerState:Bt,children:[Xt?S.jsx(iO,{...ot(),"aria-label":M,title:M,ownerState:Bt,...Fn,className:Me(Mt.clearIndicator,Fn?.className),children:y}):null,Kn?S.jsx(sO,{...Ue(),disabled:D,"aria-label":Dn?E:Ve,title:Dn?E:Ve,ownerState:Bt,...go,className:Me(Mt.popupIndicator,go?.className),children:Te}):null]})}},inputProps:{className:Mt.input,disabled:D,readOnly:He,...Zt()}})}),Ea?S.jsx(uO,{as:ge,...Be,children:S.jsxs(cO,{as:te,...re,children:[ue&&zt.length===0?S.jsx(fO,{className:Mt.loading,ownerState:Bt,children:le}):null,zt.length===0&&!b&&!ue?S.jsx(dO,{className:Mt.noOptions,ownerState:Bt,role:"presentation",onMouseDown:lt=>{lt.preventDefault()},children:xe}):null,zt.length>0?S.jsx(ya,{as:ne,...va,children:zt.map((lt,bn)=>L?vn({key:lt.key,group:lt.group,children:lt.options.map((Wn,qa)=>Ur(Wn,lt.index+qa))}):Ur(lt,bn))}):null]})}):null]})}),gO={entering:{opacity:1},entered:{opacity:1}},yO=O.forwardRef(function(r,l){const i=Gu(),u={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{addEndListener:c,appear:d=!0,children:p,easing:m,in:h,onEnter:y,onEntered:x,onEntering:R,onExit:M,onExited:E,onExiting:v,style:w,timeout:j=u,TransitionComponent:P=Pa,...D}=r,$=O.useRef(null),z=cn($,Xi(p),l),N=ee=>_=>{if(ee){const T=$.current;_===void 0?ee(T):ee(T,_)}},I=N(R),G=N((ee,_)=>{I0(ee);const T=Ru({style:w,timeout:j,easing:m},{mode:"enter"});ee.style.webkitTransition=i.transitions.create("opacity",T),ee.style.transition=i.transitions.create("opacity",T),y&&y(ee,_)}),K=N(x),b=N(v),B=N(ee=>{const _=Ru({style:w,timeout:j,easing:m},{mode:"exit"});ee.style.webkitTransition=i.transitions.create("opacity",_),ee.style.transition=i.transitions.create("opacity",_),M&&M(ee)}),V=N(E),ae=ee=>{c&&c($.current,ee)};return S.jsx(P,{appear:d,in:h,nodeRef:$,onEnter:G,onEntered:K,onEntering:I,onExit:B,onExited:V,onExiting:b,addEndListener:ae,timeout:j,...D,children:(ee,{ownerState:_,...T})=>O.cloneElement(p,{style:{opacity:0,visibility:ee==="exited"&&!h?"hidden":void 0,...gO[ee],...w,...p.props.style},ref:z,...T})})});function vO(n){return Xe("MuiBackdrop",n)}Ke("MuiBackdrop",["root","invisible"]);const bO=n=>{const{classes:r,invisible:l}=n;return Qe({root:["root",l&&"invisible"]},vO,r)},SO=pe("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.invisible&&r.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),xO=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiBackdrop"}),{children:u,className:c,component:d="div",invisible:p=!1,open:m,components:h={},componentsProps:y={},slotProps:x={},slots:R={},TransitionComponent:M,transitionDuration:E,...v}=i,w={...i,component:d,invisible:p},j=bO(w),P={transition:M,root:h.Root,...R},D={...y,...x},$={component:d,slots:P,slotProps:D},[z,N]=wt("root",{elementType:SO,externalForwardedProps:$,className:Me(j.root,c),ownerState:w}),[I,G]=wt("transition",{elementType:yO,externalForwardedProps:$,ownerState:w});return S.jsx(I,{in:m,timeout:E,...v,...G,children:S.jsx(z,{"aria-hidden":!0,...N,classes:j,ref:l,children:u})})}),CO=Ke("MuiBox",["root"]),TO=Yu(),Ht=N2({themeId:Ta,defaultTheme:TO,defaultClassName:CO.root,generateClassName:m0.generate});function EO(n){return Xe("MuiButton",n)}const ro=Ke("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),RO=O.createContext({}),OO=O.createContext(void 0),MO=n=>{const{color:r,disableElevation:l,fullWidth:i,size:u,variant:c,loading:d,loadingPosition:p,classes:m}=n,h={root:["root",d&&"loading",c,`${c}${de(r)}`,`size${de(u)}`,`${c}Size${de(u)}`,`color${de(r)}`,l&&"disableElevation",i&&"fullWidth",d&&`loadingPosition${de(p)}`],startIcon:["icon","startIcon",`iconSize${de(u)}`],endIcon:["icon","endIcon",`iconSize${de(u)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},y=Qe(h,EO,m);return{...m,...y}},sb=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],wO=pe(ki,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[l.variant],r[`${l.variant}${de(l.color)}`],r[`size${de(l.size)}`],r[`${l.variant}Size${de(l.size)}`],l.color==="inherit"&&r.colorInherit,l.disableElevation&&r.disableElevation,l.fullWidth&&r.fullWidth,l.loading&&r.loading]}})(tt(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[300]:n.palette.grey[800],l=n.palette.mode==="light"?n.palette.grey.A100:n.palette.grey[700];return{...n.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create(["background-color","box-shadow","border-color","color"],{duration:n.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${ro.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(n.vars||n).shadows[2],"&:hover":{boxShadow:(n.vars||n).shadows[4],"@media (hover: none)":{boxShadow:(n.vars||n).shadows[2]}},"&:active":{boxShadow:(n.vars||n).shadows[8]},[`&.${ro.focusVisible}`]:{boxShadow:(n.vars||n).shadows[6]},[`&.${ro.disabled}`]:{color:(n.vars||n).palette.action.disabled,boxShadow:(n.vars||n).shadows[0],backgroundColor:(n.vars||n).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${ro.disabled}`]:{border:`1px solid ${(n.vars||n).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(n.palette).filter(xn()).map(([i])=>({props:{color:i},style:{"--variant-textColor":(n.vars||n).palette[i].main,"--variant-outlinedColor":(n.vars||n).palette[i].main,"--variant-outlinedBorder":n.vars?`rgba(${n.vars.palette[i].mainChannel} / 0.5)`:yt(n.palette[i].main,.5),"--variant-containedColor":(n.vars||n).palette[i].contrastText,"--variant-containedBg":(n.vars||n).palette[i].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(n.vars||n).palette[i].dark,"--variant-textBg":n.vars?`rgba(${n.vars.palette[i].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette[i].main,n.palette.action.hoverOpacity),"--variant-outlinedBorder":(n.vars||n).palette[i].main,"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette[i].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette[i].main,n.palette.action.hoverOpacity)}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedBg:r,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedHoverBg:l,"--variant-textBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette.text.primary,n.palette.action.hoverOpacity),"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette.text.primary,n.palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:n.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${ro.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${ro.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:n.transitions.create(["background-color","box-shadow","border-color"],{duration:n.transitions.duration.short}),[`&.${ro.loading}`]:{color:"transparent"}}}]}})),AO=pe("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.startIcon,l.loading&&r.startIconLoadingStart,r[`iconSize${de(l.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...sb]})),zO=pe("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.endIcon,l.loading&&r.endIconLoadingEnd,r[`iconSize${de(l.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...sb]})),DO=pe("span",{name:"MuiButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(n.vars||n).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]})),Ev=pe("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),Rv=O.forwardRef(function(r,l){const i=O.useContext(RO),u=O.useContext(OO),c=ji(i,r),d=Je({props:c,name:"MuiButton"}),{children:p,color:m="primary",component:h="button",className:y,disabled:x=!1,disableElevation:R=!1,disableFocusRipple:M=!1,endIcon:E,focusVisibleClassName:v,fullWidth:w=!1,id:j,loading:P=null,loadingIndicator:D,loadingPosition:$="center",size:z="medium",startIcon:N,type:I,variant:G="text",...K}=d,b=hl(j),B=D??S.jsx(K0,{"aria-labelledby":b,color:"inherit",size:16}),V={...d,color:m,component:h,disabled:x,disableElevation:R,disableFocusRipple:M,fullWidth:w,loading:P,loadingIndicator:B,loadingPosition:$,size:z,type:I,variant:G},ae=MO(V),ee=(N||P&&$==="start")&&S.jsx(AO,{className:ae.startIcon,ownerState:V,children:N||S.jsx(Ev,{className:ae.loadingIconPlaceholder,ownerState:V})}),_=(E||P&&$==="end")&&S.jsx(zO,{className:ae.endIcon,ownerState:V,children:E||S.jsx(Ev,{className:ae.loadingIconPlaceholder,ownerState:V})}),T=u||"",L=typeof P=="boolean"?S.jsx("span",{className:ae.loadingWrapper,style:{display:"contents"},children:P&&S.jsx(DO,{className:ae.loadingIndicator,ownerState:V,children:B})}):null;return S.jsxs(wO,{ownerState:V,className:Me(i.className,ae.root,y,T),component:h,disabled:x||P,focusRipple:!M,focusVisibleClassName:Me(ae.focusVisible,v),ref:l,type:I,id:P?b:j,...K,classes:ae,children:[ee,$!=="end"&&L,p,$==="end"&&L,_]})});function $O(n){return Xe("PrivateSwitchBase",n)}Ke("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const jO=n=>{const{classes:r,checked:l,disabled:i,edge:u}=n,c={root:["root",l&&"checked",i&&"disabled",u&&`edge${de(u)}`],input:["input"]};return Qe(c,$O,r)},kO=pe(ki,{name:"MuiSwitchBase"})({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:n,ownerState:r})=>n==="start"&&r.size!=="small",style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:n,ownerState:r})=>n==="end"&&r.size!=="small",style:{marginRight:-12}}]}),NO=pe("input",{name:"MuiSwitchBase",shouldForwardProp:Yn})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),BO=O.forwardRef(function(r,l){const{autoFocus:i,checked:u,checkedIcon:c,defaultChecked:d,disabled:p,disableFocusRipple:m=!1,edge:h=!1,icon:y,id:x,inputProps:R,inputRef:M,name:E,onBlur:v,onChange:w,onFocus:j,readOnly:P,required:D=!1,tabIndex:$,type:z,value:N,slots:I={},slotProps:G={},...K}=r,[b,B]=sl({controlled:u,default:!!d,name:"SwitchBase",state:"checked"}),V=Hr(),ae=ue=>{j&&j(ue),V&&V.onFocus&&V.onFocus(ue)},ee=ue=>{v&&v(ue),V&&V.onBlur&&V.onBlur(ue)},_=ue=>{if(ue.nativeEvent.defaultPrevented)return;const le=ue.target.checked;B(le),w&&w(ue,le)};let T=p;V&&typeof T>"u"&&(T=V.disabled);const L=z==="checkbox"||z==="radio",Y={...r,checked:b,disabled:T,disableFocusRipple:m,edge:h},X=jO(Y),A={slots:I,slotProps:{input:R,...G}},[U,J]=wt("root",{ref:l,elementType:kO,className:X.root,shouldForwardComponentProp:!0,externalForwardedProps:{...A,component:"span",...K},getSlotProps:ue=>({...ue,onFocus:le=>{ue.onFocus?.(le),ae(le)},onBlur:le=>{ue.onBlur?.(le),ee(le)}}),ownerState:Y,additionalProps:{centerRipple:!0,focusRipple:!m,disabled:T,role:void 0,tabIndex:null}}),[ne,fe]=wt("input",{ref:M,elementType:NO,className:X.input,externalForwardedProps:A,getSlotProps:ue=>({...ue,onChange:le=>{ue.onChange?.(le),_(le)}}),ownerState:Y,additionalProps:{autoFocus:i,checked:u,defaultChecked:d,disabled:T,id:L?x:void 0,name:E,readOnly:P,required:D,tabIndex:$,type:z,...z==="checkbox"&&N===void 0?{}:{value:N}}});return S.jsxs(U,{...J,children:[S.jsx(ne,{...fe}),b?c:y]})}),_O=fn(S.jsx("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"})),LO=fn(S.jsx("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})),HO=fn(S.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}));function PO(n){return Xe("MuiCheckbox",n)}const vd=Ke("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]),UO=n=>{const{classes:r,indeterminate:l,color:i,size:u}=n,c={root:["root",l&&"indeterminate",`color${de(i)}`,`size${de(u)}`]},d=Qe(c,PO,r);return{...r,...d}},qO=pe(BO,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiCheckbox",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.indeterminate&&r.indeterminate,r[`size${de(l.size)}`],l.color!=="default"&&r[`color${de(l.color)}`]]}})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.action.activeChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette.action.active,n.palette.action.hoverOpacity)}}},...Object.entries(n.palette).filter(xn()).map(([r])=>({props:{color:r,disableRipple:!1},style:{"&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette[r].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:yt(n.palette[r].main,n.palette.action.hoverOpacity)}}})),...Object.entries(n.palette).filter(xn()).map(([r])=>({props:{color:r},style:{[`&.${vd.checked}, &.${vd.indeterminate}`]:{color:(n.vars||n).palette[r].main},[`&.${vd.disabled}`]:{color:(n.vars||n).palette.action.disabled}}})),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}))),IO=S.jsx(LO,{}),VO=S.jsx(_O,{}),YO=S.jsx(HO,{}),GO=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiCheckbox"}),{checkedIcon:u=IO,color:c="primary",icon:d=VO,indeterminate:p=!1,indeterminateIcon:m=YO,inputProps:h,size:y="medium",disableRipple:x=!1,className:R,slots:M={},slotProps:E={},...v}=i,w=p?m:d,j=p?m:u,P={...i,disableRipple:x,color:c,indeterminate:p,size:y},D=UO(P),$=E.input??h,[z,N]=wt("root",{ref:l,elementType:qO,className:Me(D.root,R),shouldForwardComponentProp:!0,externalForwardedProps:{slots:M,slotProps:E,...v},ownerState:P,additionalProps:{type:"checkbox",icon:O.cloneElement(w,{fontSize:w.props.fontSize??y}),checkedIcon:O.cloneElement(j,{fontSize:j.props.fontSize??y}),disableRipple:x,slots:M,slotProps:{input:_0(typeof $=="function"?$(P):$,{"data-indeterminate":p})}}});return S.jsx(z,{...N,classes:D})}),Ud=typeof fp({})=="function",XO=(n,r)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...r&&!n.vars&&{colorScheme:n.palette.mode}}),KO=n=>({color:(n.vars||n).palette.text.primary,...n.typography.body1,backgroundColor:(n.vars||n).palette.background.default,"@media print":{backgroundColor:(n.vars||n).palette.common.white}}),ub=(n,r=!1)=>{const l={};r&&n.colorSchemes&&typeof n.getColorSchemeSelector=="function"&&Object.entries(n.colorSchemes).forEach(([c,d])=>{const p=n.getColorSchemeSelector(c);p.startsWith("@")?l[p]={":root":{colorScheme:d.palette?.mode}}:l[p.replace(/\s*&/,"")]={colorScheme:d.palette?.mode}});let i={html:XO(n,r),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:n.typography.fontWeightBold},body:{margin:0,...KO(n),"&::backdrop":{backgroundColor:(n.vars||n).palette.background.default}},...l};const u=n.components?.MuiCssBaseline?.styleOverrides;return u&&(i=[i,u]),i},yu="mui-ecs",QO=n=>{const r=ub(n,!1),l=Array.isArray(r)?r[0]:r;return!n.vars&&l&&(l.html[`:root:has(${yu})`]={colorScheme:n.palette.mode}),n.colorSchemes&&Object.entries(n.colorSchemes).forEach(([i,u])=>{const c=n.getColorSchemeSelector(i);c.startsWith("@")?l[c]={[`:root:not(:has(.${yu}))`]:{colorScheme:u.palette?.mode}}:l[c.replace(/\s*&/,"")]={[`&:not(:has(.${yu}))`]:{colorScheme:u.palette?.mode}}}),r},FO=fp(Ud?({theme:n,enableColorScheme:r})=>ub(n,r):({theme:n})=>QO(n));function WO(n){const r=Je({props:n,name:"MuiCssBaseline"}),{children:l,enableColorScheme:i=!1}=r;return S.jsxs(O.Fragment,{children:[Ud&&S.jsx(FO,{enableColorScheme:i}),!Ud&&!i&&S.jsx("span",{className:yu,style:{display:"none"}}),l]})}function cb(n=window){const r=n.document.documentElement.clientWidth;return n.innerWidth-r}function ZO(n){const r=In(n);return r.body===n?ir(n).innerWidth>r.documentElement.clientWidth:n.scrollHeight>n.clientHeight}function Mi(n,r){r?n.setAttribute("aria-hidden","true"):n.removeAttribute("aria-hidden")}function Ov(n){return parseInt(ir(n).getComputedStyle(n).paddingRight,10)||0}function JO(n){const l=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(n.tagName),i=n.tagName==="INPUT"&&n.getAttribute("type")==="hidden";return l||i}function Mv(n,r,l,i,u){const c=[r,l,...i];[].forEach.call(n.children,d=>{const p=!c.includes(d),m=!JO(d);p&&m&&Mi(d,u)})}function bd(n,r){let l=-1;return n.some((i,u)=>r(i)?(l=u,!0):!1),l}function eM(n,r){const l=[],i=n.container;if(!r.disableScrollLock){if(ZO(i)){const d=cb(ir(i));l.push({value:i.style.paddingRight,property:"padding-right",el:i}),i.style.paddingRight=`${Ov(i)+d}px`;const p=In(i).querySelectorAll(".mui-fixed");[].forEach.call(p,m=>{l.push({value:m.style.paddingRight,property:"padding-right",el:m}),m.style.paddingRight=`${Ov(m)+d}px`})}let c;if(i.parentNode instanceof DocumentFragment)c=In(i).body;else{const d=i.parentElement,p=ir(i);c=d?.nodeName==="HTML"&&p.getComputedStyle(d).overflowY==="scroll"?d:i}l.push({value:c.style.overflow,property:"overflow",el:c},{value:c.style.overflowX,property:"overflow-x",el:c},{value:c.style.overflowY,property:"overflow-y",el:c}),c.style.overflow="hidden"}return()=>{l.forEach(({value:c,el:d,property:p})=>{c?d.style.setProperty(p,c):d.style.removeProperty(p)})}}function tM(n){const r=[];return[].forEach.call(n.children,l=>{l.getAttribute("aria-hidden")==="true"&&r.push(l)}),r}class nM{constructor(){this.modals=[],this.containers=[]}add(r,l){let i=this.modals.indexOf(r);if(i!==-1)return i;i=this.modals.length,this.modals.push(r),r.modalRef&&Mi(r.modalRef,!1);const u=tM(l);Mv(l,r.mount,r.modalRef,u,!0);const c=bd(this.containers,d=>d.container===l);return c!==-1?(this.containers[c].modals.push(r),i):(this.containers.push({modals:[r],container:l,restore:null,hiddenSiblings:u}),i)}mount(r,l){const i=bd(this.containers,c=>c.modals.includes(r)),u=this.containers[i];u.restore||(u.restore=eM(u,l))}remove(r,l=!0){const i=this.modals.indexOf(r);if(i===-1)return i;const u=bd(this.containers,d=>d.modals.includes(r)),c=this.containers[u];if(c.modals.splice(c.modals.indexOf(r),1),this.modals.splice(i,1),c.modals.length===0)c.restore&&c.restore(),r.modalRef&&Mi(r.modalRef,l),Mv(c.container,r.mount,r.modalRef,c.hiddenSiblings,!1),this.containers.splice(u,1);else{const d=c.modals[c.modals.length-1];d.modalRef&&Mi(d.modalRef,!1)}return i}isTopModal(r){return this.modals.length>0&&this.modals[this.modals.length-1]===r}}const aM=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function rM(n){const r=parseInt(n.getAttribute("tabindex")||"",10);return Number.isNaN(r)?n.contentEditable==="true"||(n.nodeName==="AUDIO"||n.nodeName==="VIDEO"||n.nodeName==="DETAILS")&&n.getAttribute("tabindex")===null?0:n.tabIndex:r}function oM(n){if(n.tagName!=="INPUT"||n.type!=="radio"||!n.name)return!1;const r=i=>n.ownerDocument.querySelector(`input[type="radio"]${i}`);let l=r(`[name="${n.name}"]:checked`);return l||(l=r(`[name="${n.name}"]`)),l!==n}function lM(n){return!(n.disabled||n.tagName==="INPUT"&&n.type==="hidden"||oM(n))}function iM(n){const r=[],l=[];return Array.from(n.querySelectorAll(aM)).forEach((i,u)=>{const c=rM(i);c===-1||!lM(i)||(c===0?r.push(i):l.push({documentOrder:u,tabIndex:c,node:i}))}),l.sort((i,u)=>i.tabIndex===u.tabIndex?i.documentOrder-u.documentOrder:i.tabIndex-u.tabIndex).map(i=>i.node).concat(r)}function sM(){return!0}function uM(n){const{children:r,disableAutoFocus:l=!1,disableEnforceFocus:i=!1,disableRestoreFocus:u=!1,getTabbable:c=iM,isEnabled:d=sM,open:p}=n,m=O.useRef(!1),h=O.useRef(null),y=O.useRef(null),x=O.useRef(null),R=O.useRef(null),M=O.useRef(!1),E=O.useRef(null),v=cn(Xi(r),E),w=O.useRef(null);O.useEffect(()=>{!p||!E.current||(M.current=!l)},[l,p]),O.useEffect(()=>{if(!p||!E.current)return;const D=In(E.current);return E.current.contains(D.activeElement)||(E.current.hasAttribute("tabIndex")||E.current.setAttribute("tabIndex","-1"),M.current&&E.current.focus()),()=>{u||(x.current&&x.current.focus&&(m.current=!0,x.current.focus()),x.current=null)}},[p]),O.useEffect(()=>{if(!p||!E.current)return;const D=In(E.current),$=I=>{w.current=I,!(i||!d()||I.key!=="Tab")&&D.activeElement===E.current&&I.shiftKey&&(m.current=!0,y.current&&y.current.focus())},z=()=>{const I=E.current;if(I===null)return;if(!D.hasFocus()||!d()||m.current){m.current=!1;return}if(I.contains(D.activeElement)||i&&D.activeElement!==h.current&&D.activeElement!==y.current)return;if(D.activeElement!==R.current)R.current=null;else if(R.current!==null)return;if(!M.current)return;let G=[];if((D.activeElement===h.current||D.activeElement===y.current)&&(G=c(E.current)),G.length>0){const K=!!(w.current?.shiftKey&&w.current?.key==="Tab"),b=G[0],B=G[G.length-1];typeof b!="string"&&typeof B!="string"&&(K?B.focus():b.focus())}else I.focus()};D.addEventListener("focusin",z),D.addEventListener("keydown",$,!0);const N=setInterval(()=>{D.activeElement&&D.activeElement.tagName==="BODY"&&z()},50);return()=>{clearInterval(N),D.removeEventListener("focusin",z),D.removeEventListener("keydown",$,!0)}},[l,i,u,d,p,c]);const j=D=>{x.current===null&&(x.current=D.relatedTarget),M.current=!0,R.current=D.target;const $=r.props.onFocus;$&&$(D)},P=D=>{x.current===null&&(x.current=D.relatedTarget),M.current=!0};return S.jsxs(O.Fragment,{children:[S.jsx("div",{tabIndex:p?0:-1,onFocus:P,ref:h,"data-testid":"sentinelStart"}),O.cloneElement(r,{ref:v,onFocus:j}),S.jsx("div",{tabIndex:p?0:-1,onFocus:P,ref:y,"data-testid":"sentinelEnd"})]})}function cM(n){return typeof n=="function"?n():n}function fM(n){return n?n.props.hasOwnProperty("in"):!1}const wv=()=>{},uu=new nM;function dM(n){const{container:r,disableEscapeKeyDown:l=!1,disableScrollLock:i=!1,closeAfterTransition:u=!1,onTransitionEnter:c,onTransitionExited:d,children:p,onClose:m,open:h,rootRef:y}=n,x=O.useRef({}),R=O.useRef(null),M=O.useRef(null),E=cn(M,y),[v,w]=O.useState(!h),j=fM(p);let P=!0;(n["aria-hidden"]==="false"||n["aria-hidden"]===!1)&&(P=!1);const D=()=>In(R.current),$=()=>(x.current.modalRef=M.current,x.current.mount=R.current,x.current),z=()=>{uu.mount($(),{disableScrollLock:i}),M.current&&(M.current.scrollTop=0)},N=Ca(()=>{const _=cM(r)||D().body;uu.add($(),_),M.current&&z()}),I=()=>uu.isTopModal($()),G=Ca(_=>{R.current=_,_&&(h&&I()?z():M.current&&Mi(M.current,P))}),K=O.useCallback(()=>{uu.remove($(),P)},[P]);O.useEffect(()=>()=>{K()},[K]),O.useEffect(()=>{h?N():(!j||!u)&&K()},[h,K,j,u,N]);const b=_=>T=>{_.onKeyDown?.(T),!(T.key!=="Escape"||T.which===229||!I())&&(l||(T.stopPropagation(),m&&m(T,"escapeKeyDown")))},B=_=>T=>{_.onClick?.(T),T.target===T.currentTarget&&m&&m(T,"backdropClick")};return{getRootProps:(_={})=>{const T=G0(n);delete T.onTransitionEnter,delete T.onTransitionExited;const L={...T,..._};return{role:"presentation",...L,onKeyDown:b(L),ref:E}},getBackdropProps:(_={})=>{const T=_;return{"aria-hidden":!0,...T,onClick:B(T),open:h}},getTransitionProps:()=>{const _=()=>{w(!1),c&&c()},T=()=>{w(!0),d&&d(),u&&K()};return{onEnter:Zy(_,p?.props.onEnter??wv),onExited:Zy(T,p?.props.onExited??wv)}},rootRef:E,portalRef:G,isTopModal:I,exited:v,hasTransition:j}}function pM(n){return Xe("MuiModal",n)}Ke("MuiModal",["root","hidden","backdrop"]);const mM=n=>{const{open:r,exited:l,classes:i}=n;return Qe({root:["root",!r&&l&&"hidden"],backdrop:["backdrop"]},pM,i)},hM=pe("div",{name:"MuiModal",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,!l.open&&l.exited&&r.hidden]}})(tt(({theme:n})=>({position:"fixed",zIndex:(n.vars||n).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:r})=>!r.open&&r.exited,style:{visibility:"hidden"}}]}))),gM=pe(xO,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),yM=O.forwardRef(function(r,l){const i=Je({name:"MuiModal",props:r}),{BackdropComponent:u=gM,BackdropProps:c,classes:d,className:p,closeAfterTransition:m=!1,children:h,container:y,component:x,components:R={},componentsProps:M={},disableAutoFocus:E=!1,disableEnforceFocus:v=!1,disableEscapeKeyDown:w=!1,disablePortal:j=!1,disableRestoreFocus:P=!1,disableScrollLock:D=!1,hideBackdrop:$=!1,keepMounted:z=!1,onClose:N,onTransitionEnter:I,onTransitionExited:G,open:K,slotProps:b={},slots:B={},theme:V,...ae}=i,ee={...i,closeAfterTransition:m,disableAutoFocus:E,disableEnforceFocus:v,disableEscapeKeyDown:w,disablePortal:j,disableRestoreFocus:P,disableScrollLock:D,hideBackdrop:$,keepMounted:z},{getRootProps:_,getBackdropProps:T,getTransitionProps:L,portalRef:Y,isTopModal:X,exited:A,hasTransition:U}=dM({...ee,rootRef:l}),J={...ee,exited:A},ne=mM(J),fe={};if(h.props.tabIndex===void 0&&(fe.tabIndex="-1"),U){const{onEnter:me,onExited:Re}=L();fe.onEnter=me,fe.onExited=Re}const ue={slots:{root:R.Root,backdrop:R.Backdrop,...B},slotProps:{...M,...b}},[le,ve]=wt("root",{ref:l,elementType:hM,externalForwardedProps:{...ue,...ae,component:x},getSlotProps:_,ownerState:J,className:Me(p,ne?.root,!J.open&&J.exited&&ne?.hidden)}),[xe,be]=wt("backdrop",{ref:c?.ref,elementType:u,externalForwardedProps:ue,shouldForwardComponentProp:!0,additionalProps:c,getSlotProps:me=>T({...me,onClick:Re=>{me?.onClick&&me.onClick(Re)}}),className:Me(c?.className,ne?.backdrop),ownerState:J});return!z&&!K&&(!U||A)?null:S.jsx(ob,{ref:Y,container:y,disablePortal:j,children:S.jsxs(le,{...ve,children:[!$&&u?S.jsx(xe,{...be}):null,S.jsx(uM,{disableEnforceFocus:v,disableAutoFocus:E,disableRestoreFocus:P,isEnabled:X,open:K,children:O.cloneElement(h,fe)})]})})}),vM=n=>{const{classes:r,disableUnderline:l,startAdornment:i,endAdornment:u,size:c,hiddenLabel:d,multiline:p}=n,m={root:["root",!l&&"underline",i&&"adornedStart",u&&"adornedEnd",c==="small"&&`size${de(c)}`,d&&"hiddenLabel",p&&"multiline"],input:["input"]},h=Qe(m,nO,r);return{...r,...h}},bM=pe(Fu,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiFilledInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[...Ku(n,r),!l.disableUnderline&&r.underline]}})(tt(({theme:n})=>{const r=n.palette.mode==="light",l=r?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",i=r?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",u=r?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",c=r?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return{position:"relative",backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i,borderTopLeftRadius:(n.vars||n).shape.borderRadius,borderTopRightRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),"&:hover":{backgroundColor:n.vars?n.vars.palette.FilledInput.hoverBg:u,"@media (hover: none)":{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i}},[`&.${Pn.focused}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i},[`&.${Pn.disabled}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.disabledBg:c},variants:[{props:({ownerState:d})=>!d.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${Pn.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${Pn.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`:l}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${Pn.disabled}, .${Pn.error}):before`]:{borderBottom:`1px solid ${(n.vars||n).palette.text.primary}`},[`&.${Pn.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(xn()).map(([d])=>({props:{disableUnderline:!1,color:d},style:{"&::after":{borderBottom:`2px solid ${(n.vars||n).palette[d]?.main}`}}})),{props:({ownerState:d})=>d.startAdornment,style:{paddingLeft:12}},{props:({ownerState:d})=>d.endAdornment,style:{paddingRight:12}},{props:({ownerState:d})=>d.multiline,style:{padding:"25px 12px 8px"}},{props:({ownerState:d,size:p})=>d.multiline&&p==="small",style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel&&d.size==="small",style:{paddingTop:8,paddingBottom:9}}]}})),SM=pe(Wu,{name:"MuiFilledInput",slot:"Input",overridesResolver:Qu})(tt(({theme:n})=>({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12,...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:r})=>r.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}},{props:({ownerState:r})=>r.hiddenLabel&&r.size==="small",style:{paddingTop:8,paddingBottom:9}},{props:({ownerState:r})=>r.multiline,style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]}))),Ep=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFilledInput"}),{disableUnderline:u=!1,components:c={},componentsProps:d,fullWidth:p=!1,hiddenLabel:m,inputComponent:h="input",multiline:y=!1,slotProps:x,slots:R={},type:M="text",...E}=i,v={...i,disableUnderline:u,fullWidth:p,inputComponent:h,multiline:y,type:M},w=vM(i),j={root:{ownerState:v},input:{ownerState:v}},P=x??d?gn(j,x??d):j,D=R.root??c.Root??bM,$=R.input??c.Input??SM;return S.jsx(Tp,{slots:{root:D,input:$},slotProps:P,fullWidth:p,inputComponent:h,multiline:y,ref:l,type:M,...E,classes:w})});Ep.muiName="Input";function xM(n){return Xe("MuiFormControl",n)}Ke("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const CM=n=>{const{classes:r,margin:l,fullWidth:i}=n,u={root:["root",l!=="none"&&`margin${de(l)}`,i&&"fullWidth"]};return Qe(u,xM,r)},TM=pe("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[`margin${de(l.margin)}`],l.fullWidth&&r.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),EM=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFormControl"}),{children:u,className:c,color:d="primary",component:p="div",disabled:m=!1,error:h=!1,focused:y,fullWidth:x=!1,hiddenLabel:R=!1,margin:M="none",required:E=!1,size:v="medium",variant:w="outlined",...j}=i,P={...i,color:d,component:p,disabled:m,error:h,fullWidth:x,hiddenLabel:R,margin:M,required:E,size:v,variant:w},D=CM(P),[$,z]=O.useState(()=>{let _=!1;return u&&O.Children.forEach(u,T=>{if(!hu(T,["Input","Select"]))return;const L=hu(T,["Select"])?T.props.input:T;L&&WR(L.props)&&(_=!0)}),_}),[N,I]=O.useState(()=>{let _=!1;return u&&O.Children.forEach(u,T=>{hu(T,["Input","Select"])&&(wu(T.props,!0)||wu(T.props.inputProps,!0))&&(_=!0)}),_}),[G,K]=O.useState(!1);m&&G&&K(!1);const b=y!==void 0&&!m?y:G;let B;O.useRef(!1);const V=O.useCallback(()=>{I(!0)},[]),ae=O.useCallback(()=>{I(!1)},[]),ee=O.useMemo(()=>({adornedStart:$,setAdornedStart:z,color:d,disabled:m,error:h,filled:N,focused:b,fullWidth:x,hiddenLabel:R,size:v,onBlur:()=>{K(!1)},onFocus:()=>{K(!0)},onEmpty:ae,onFilled:V,registerEffect:B,required:E,variant:w}),[$,d,m,h,N,b,x,R,B,ae,V,E,v,w]);return S.jsx(Cp.Provider,{value:ee,children:S.jsx(TM,{as:p,ownerState:P,className:Me(D.root,c),ref:l,...j,children:u})})});function RM(n){return Xe("MuiFormControlLabel",n)}const Ti=Ke("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]),OM=n=>{const{classes:r,disabled:l,labelPlacement:i,error:u,required:c}=n,d={root:["root",l&&"disabled",`labelPlacement${de(i)}`,u&&"error",c&&"required"],label:["label",l&&"disabled"],asterisk:["asterisk",u&&"error"]};return Qe(d,RM,r)},MM=pe("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[{[`& .${Ti.label}`]:r.label},r.root,r[`labelPlacement${de(l.labelPlacement)}`]]}})(tt(({theme:n})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${Ti.disabled}`]:{cursor:"default"},[`& .${Ti.label}`]:{[`&.${Ti.disabled}`]:{color:(n.vars||n).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:r})=>r==="start"||r==="top"||r==="bottom",style:{marginLeft:16}}]}))),wM=pe("span",{name:"MuiFormControlLabel",slot:"Asterisk"})(tt(({theme:n})=>({[`&.${Ti.error}`]:{color:(n.vars||n).palette.error.main}}))),AM=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFormControlLabel"}),{checked:u,className:c,componentsProps:d={},control:p,disabled:m,disableTypography:h,inputRef:y,label:x,labelPlacement:R="end",name:M,onChange:E,required:v,slots:w={},slotProps:j={},value:P,...D}=i,$=Hr(),z=m??p.props.disabled??$?.disabled,N=v??p.props.required,I={disabled:z,required:N};["checked","name","onChange","value","inputRef"].forEach(_=>{typeof p.props[_]>"u"&&typeof i[_]<"u"&&(I[_]=i[_])});const G=fo({props:i,muiFormControl:$,states:["error"]}),K={...i,disabled:z,labelPlacement:R,required:N,error:G.error},b=OM(K),B={slots:w,slotProps:{...d,...j}},[V,ae]=wt("typography",{elementType:_e,externalForwardedProps:B,ownerState:K});let ee=x;return ee!=null&&ee.type!==_e&&!h&&(ee=S.jsx(V,{component:"span",...ae,className:Me(b.label,ae?.className),children:ee})),S.jsxs(MM,{className:Me(b.root,c),ownerState:K,ref:l,...D,children:[O.cloneElement(p,I),N?S.jsxs("div",{children:[ee,S.jsxs(wM,{ownerState:K,"aria-hidden":!0,className:b.asterisk,children:[" ","*"]})]}):ee]})});function zM(n){return Xe("MuiFormHelperText",n)}const Av=Ke("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var zv;const DM=n=>{const{classes:r,contained:l,size:i,disabled:u,error:c,filled:d,focused:p,required:m}=n,h={root:["root",u&&"disabled",c&&"error",i&&`size${de(i)}`,l&&"contained",p&&"focused",d&&"filled",m&&"required"]};return Qe(h,zM,r)},$M=pe("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.size&&r[`size${de(l.size)}`],l.contained&&r.contained,l.filled&&r.filled]}})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${Av.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${Av.error}`]:{color:(n.vars||n).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:({ownerState:r})=>r.contained,style:{marginLeft:14,marginRight:14}}]}))),jM=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFormHelperText"}),{children:u,className:c,component:d="p",disabled:p,error:m,filled:h,focused:y,margin:x,required:R,variant:M,...E}=i,v=Hr(),w=fo({props:i,muiFormControl:v,states:["variant","size","disabled","error","filled","focused","required"]}),j={...i,component:d,contained:w.variant==="filled"||w.variant==="outlined",variant:w.variant,size:w.size,disabled:w.disabled,error:w.error,filled:w.filled,focused:w.focused,required:w.required};delete j.ownerState;const P=DM(j);return S.jsx($M,{as:d,className:Me(P.root,c),ref:l,...E,ownerState:j,children:u===" "?zv||(zv=S.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):u})});function kM(n){return Xe("MuiFormLabel",n)}const wi=Ke("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),NM=n=>{const{classes:r,color:l,focused:i,disabled:u,error:c,filled:d,required:p}=n,m={root:["root",`color${de(l)}`,u&&"disabled",c&&"error",d&&"filled",i&&"focused",p&&"required"],asterisk:["asterisk",c&&"error"]};return Qe(m,kM,r)},BM=pe("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.color==="secondary"&&r.colorSecondary,l.filled&&r.filled]}})(tt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.body1,lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(n.palette).filter(xn()).map(([r])=>({props:{color:r},style:{[`&.${wi.focused}`]:{color:(n.vars||n).palette[r].main}}})),{props:{},style:{[`&.${wi.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${wi.error}`]:{color:(n.vars||n).palette.error.main}}}]}))),_M=pe("span",{name:"MuiFormLabel",slot:"Asterisk"})(tt(({theme:n})=>({[`&.${wi.error}`]:{color:(n.vars||n).palette.error.main}}))),LM=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiFormLabel"}),{children:u,className:c,color:d,component:p="label",disabled:m,error:h,filled:y,focused:x,required:R,...M}=i,E=Hr(),v=fo({props:i,muiFormControl:E,states:["color","required","focused","disabled","error","filled"]}),w={...i,color:v.color||"primary",component:p,disabled:v.disabled,error:v.error,filled:v.filled,focused:v.focused,required:v.required},j=NM(w);return S.jsxs(BM,{as:p,ownerState:w,className:Me(j.root,c),ref:l,...M,children:[u,v.required&&S.jsxs(_M,{ownerState:w,"aria-hidden":!0,className:j.asterisk,children:[" ","*"]})]})}),we=PC({createStyledComponent:pe("div",{name:"MuiGrid",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.container&&r.container]}}),componentName:"MuiGrid",useThemeProps:n=>Je({props:n,name:"MuiGrid"}),useTheme:Gu});function qd(n){return`scale(${n}, ${n**2})`}const HM={entering:{opacity:1,transform:qd(1)},entered:{opacity:1,transform:"none"}},Sd=typeof navigator<"u"&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),Id=O.forwardRef(function(r,l){const{addEndListener:i,appear:u=!0,children:c,easing:d,in:p,onEnter:m,onEntered:h,onEntering:y,onExit:x,onExited:R,onExiting:M,style:E,timeout:v="auto",TransitionComponent:w=Pa,...j}=r,P=q0(),D=O.useRef(),$=Gu(),z=O.useRef(null),N=cn(z,Xi(c),l),I=_=>T=>{if(_){const L=z.current;T===void 0?_(L):_(L,T)}},G=I(y),K=I((_,T)=>{I0(_);const{duration:L,delay:Y,easing:X}=Ru({style:E,timeout:v,easing:d},{mode:"enter"});let A;v==="auto"?(A=$.transitions.getAutoHeightDuration(_.clientHeight),D.current=A):A=L,_.style.transition=[$.transitions.create("opacity",{duration:A,delay:Y}),$.transitions.create("transform",{duration:Sd?A:A*.666,delay:Y,easing:X})].join(","),m&&m(_,T)}),b=I(h),B=I(M),V=I(_=>{const{duration:T,delay:L,easing:Y}=Ru({style:E,timeout:v,easing:d},{mode:"exit"});let X;v==="auto"?(X=$.transitions.getAutoHeightDuration(_.clientHeight),D.current=X):X=T,_.style.transition=[$.transitions.create("opacity",{duration:X,delay:L}),$.transitions.create("transform",{duration:Sd?X:X*.666,delay:Sd?L:L||X*.333,easing:Y})].join(","),_.style.opacity=0,_.style.transform=qd(.75),x&&x(_)}),ae=I(R),ee=_=>{v==="auto"&&P.start(D.current||0,_),i&&i(z.current,_)};return S.jsx(w,{appear:u,in:p,nodeRef:z,onEnter:K,onEntered:b,onEntering:G,onExit:V,onExited:ae,onExiting:B,addEndListener:ee,timeout:v==="auto"?null:v,...j,children:(_,{ownerState:T,...L})=>O.cloneElement(c,{style:{opacity:0,transform:qd(.75),visibility:_==="exited"&&!p?"hidden":void 0,...HM[_],...E,...c.props.style},ref:N,...L})})});Id&&(Id.muiSupportAuto=!0);const PM=n=>{const{classes:r,disableUnderline:l}=n,u=Qe({root:["root",!l&&"underline"],input:["input"]},eO,r);return{...r,...u}},UM=pe(Fu,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[...Ku(n,r),!l.disableUnderline&&r.underline]}})(tt(({theme:n})=>{let l=n.palette.mode==="light"?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return n.vars&&(l=`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`),{position:"relative",variants:[{props:({ownerState:i})=>i.formControl,style:{"label + &":{marginTop:16}}},{props:({ownerState:i})=>!i.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${kr.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${kr.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${l}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${kr.disabled}, .${kr.error}):before`]:{borderBottom:`2px solid ${(n.vars||n).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${l}`}},[`&.${kr.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(xn()).map(([i])=>({props:{color:i,disableUnderline:!1},style:{"&::after":{borderBottom:`2px solid ${(n.vars||n).palette[i].main}`}}}))]}})),qM=pe(Wu,{name:"MuiInput",slot:"Input",overridesResolver:Qu})({}),Rp=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiInput"}),{disableUnderline:u=!1,components:c={},componentsProps:d,fullWidth:p=!1,inputComponent:m="input",multiline:h=!1,slotProps:y,slots:x={},type:R="text",...M}=i,E=PM(i),w={root:{ownerState:{disableUnderline:u}}},j=y??d?gn(y??d,w):w,P=x.root??c.Root??UM,D=x.input??c.Input??qM;return S.jsx(Tp,{slots:{root:P,input:D},slotProps:j,fullWidth:p,inputComponent:m,multiline:h,ref:l,type:R,...M,classes:E})});Rp.muiName="Input";function IM(n){return Xe("MuiInputLabel",n)}Ke("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const VM=n=>{const{classes:r,formControl:l,size:i,shrink:u,disableAnimation:c,variant:d,required:p}=n,m={root:["root",l&&"formControl",!c&&"animated",u&&"shrink",i&&i!=="medium"&&`size${de(i)}`,d],asterisk:[p&&"asterisk"]},h=Qe(m,IM,r);return{...r,...h}},YM=pe(LM,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiInputLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[{[`& .${wi.asterisk}`]:r.asterisk},r.root,l.formControl&&r.formControl,l.size==="small"&&r.sizeSmall,l.shrink&&r.shrink,!l.disableAnimation&&r.animated,l.focused&&r.focused,r[l.variant]]}})(tt(({theme:n})=>({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:({ownerState:r})=>r.formControl,style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:({ownerState:r})=>r.shrink,style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:({ownerState:r})=>!r.disableAnimation,style:{transition:n.transitions.create(["color","transform","max-width"],{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:({variant:r,ownerState:l})=>r==="filled"&&l.shrink,style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:({variant:r,ownerState:l,size:i})=>r==="filled"&&l.shrink&&i==="small",style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:({variant:r,ownerState:l})=>r==="outlined"&&l.shrink,style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}))),GM=O.forwardRef(function(r,l){const i=Je({name:"MuiInputLabel",props:r}),{disableAnimation:u=!1,margin:c,shrink:d,variant:p,className:m,...h}=i,y=Hr();let x=d;typeof x>"u"&&y&&(x=y.filled||y.focused||y.adornedStart);const R=fo({props:i,muiFormControl:y,states:["size","variant","required","focused"]}),M={...i,disableAnimation:u,formControl:y,shrink:x,size:R.size,variant:R.variant,required:R.required,focused:R.focused},E=VM(M);return S.jsx(YM,{"data-shrink":x,ref:l,className:Me(E.root,m),...h,ownerState:M,classes:E})}),XM=O.createContext({});function KM(n){return Xe("MuiList",n)}Ke("MuiList",["root","padding","dense","subheader"]);const QM=n=>{const{classes:r,disablePadding:l,dense:i,subheader:u}=n;return Qe({root:["root",!l&&"padding",i&&"dense",u&&"subheader"]},KM,r)},FM=pe("ul",{name:"MuiList",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,!l.disablePadding&&r.padding,l.dense&&r.dense,l.subheader&&r.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:({ownerState:n})=>!n.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:n})=>n.subheader,style:{paddingTop:0}}]}),WM=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiList"}),{children:u,className:c,component:d="ul",dense:p=!1,disablePadding:m=!1,subheader:h,...y}=i,x=O.useMemo(()=>({dense:p}),[p]),R={...i,component:d,dense:p,disablePadding:m},M=QM(R);return S.jsx(XM.Provider,{value:x,children:S.jsxs(FM,{as:d,className:Me(M.root,c),ref:l,ownerState:R,...y,children:[h,u]})})});function xd(n,r,l){return n===r?n.firstChild:r&&r.nextElementSibling?r.nextElementSibling:l?null:n.firstChild}function Dv(n,r,l){return n===r?l?n.firstChild:n.lastChild:r&&r.previousElementSibling?r.previousElementSibling:l?null:n.lastChild}function fb(n,r){if(r===void 0)return!0;let l=n.innerText;return l===void 0&&(l=n.textContent),l=l.trim().toLowerCase(),l.length===0?!1:r.repeating?l[0]===r.keys[0]:l.startsWith(r.keys.join(""))}function vi(n,r,l,i,u,c){let d=!1,p=u(n,r,r?l:!1);for(;p;){if(p===n.firstChild){if(d)return!1;d=!0}const m=i?!1:p.disabled||p.getAttribute("aria-disabled")==="true";if(!p.hasAttribute("tabindex")||!fb(p,c)||m)p=u(n,p,l);else return p.focus(),!0}return!1}const ZM=O.forwardRef(function(r,l){const{actions:i,autoFocus:u=!1,autoFocusItem:c=!1,children:d,className:p,disabledItemsFocusable:m=!1,disableListWrap:h=!1,onKeyDown:y,variant:x="selectedMenu",...R}=r,M=O.useRef(null),E=O.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});fa(()=>{u&&M.current.focus()},[u]),O.useImperativeHandle(i,()=>({adjustStyleForScrollbar:(D,{direction:$})=>{const z=!M.current.style.width;if(D.clientHeight<M.current.clientHeight&&z){const N=`${cb(ir(D))}px`;M.current.style[$==="rtl"?"paddingLeft":"paddingRight"]=N,M.current.style.width=`calc(100% + ${N})`}return M.current}}),[]);const v=D=>{const $=M.current,z=D.key;if(D.ctrlKey||D.metaKey||D.altKey){y&&y(D);return}const I=In($).activeElement;if(z==="ArrowDown")D.preventDefault(),vi($,I,h,m,xd);else if(z==="ArrowUp")D.preventDefault(),vi($,I,h,m,Dv);else if(z==="Home")D.preventDefault(),vi($,null,h,m,xd);else if(z==="End")D.preventDefault(),vi($,null,h,m,Dv);else if(z.length===1){const G=E.current,K=z.toLowerCase(),b=performance.now();G.keys.length>0&&(b-G.lastTime>500?(G.keys=[],G.repeating=!0,G.previousKeyMatched=!0):G.repeating&&K!==G.keys[0]&&(G.repeating=!1)),G.lastTime=b,G.keys.push(K);const B=I&&!G.repeating&&fb(I,G);G.previousKeyMatched&&(B||vi($,I,!1,m,xd,G))?D.preventDefault():G.previousKeyMatched=!1}y&&y(D)},w=cn(M,l);let j=-1;O.Children.forEach(d,(D,$)=>{if(!O.isValidElement(D)){j===$&&(j+=1,j>=d.length&&(j=-1));return}D.props.disabled||(x==="selectedMenu"&&D.props.selected||j===-1)&&(j=$),j===$&&(D.props.disabled||D.props.muiSkipListHighlight||D.type.muiSkipListHighlight)&&(j+=1,j>=d.length&&(j=-1))});const P=O.Children.map(d,(D,$)=>{if($===j){const z={};return c&&(z.autoFocus=!0),D.props.tabIndex===void 0&&x==="selectedMenu"&&(z.tabIndex=0),O.cloneElement(D,z)}return D});return S.jsx(WM,{role:"menu",ref:w,className:p,onKeyDown:v,tabIndex:u?0:-1,...R,children:P})});function JM(n){return Xe("MuiPopover",n)}Ke("MuiPopover",["root","paper"]);function $v(n,r){let l=0;return typeof r=="number"?l=r:r==="center"?l=n.height/2:r==="bottom"&&(l=n.height),l}function jv(n,r){let l=0;return typeof r=="number"?l=r:r==="center"?l=n.width/2:r==="right"&&(l=n.width),l}function kv(n){return[n.horizontal,n.vertical].map(r=>typeof r=="number"?`${r}px`:r).join(" ")}function cu(n){return typeof n=="function"?n():n}const ew=n=>{const{classes:r}=n;return Qe({root:["root"],paper:["paper"]},JM,r)},tw=pe(yM,{name:"MuiPopover",slot:"Root"})({}),db=pe(hn,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),nw=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiPopover"}),{action:u,anchorEl:c,anchorOrigin:d={vertical:"top",horizontal:"left"},anchorPosition:p,anchorReference:m="anchorEl",children:h,className:y,container:x,elevation:R=8,marginThreshold:M=16,open:E,PaperProps:v={},slots:w={},slotProps:j={},transformOrigin:P={vertical:"top",horizontal:"left"},TransitionComponent:D,transitionDuration:$="auto",TransitionProps:z={},disableScrollLock:N=!1,...I}=i,G=O.useRef(),K={...i,anchorOrigin:d,anchorReference:m,elevation:R,marginThreshold:M,transformOrigin:P,TransitionComponent:D,transitionDuration:$,TransitionProps:z},b=ew(K),B=O.useCallback(()=>{if(m==="anchorPosition")return p;const me=cu(c),Ne=(me&&me.nodeType===1?me:In(G.current).body).getBoundingClientRect();return{top:Ne.top+$v(Ne,d.vertical),left:Ne.left+jv(Ne,d.horizontal)}},[c,d.horizontal,d.vertical,p,m]),V=O.useCallback(me=>({vertical:$v(me,P.vertical),horizontal:jv(me,P.horizontal)}),[P.horizontal,P.vertical]),ae=O.useCallback(me=>{const Re={width:me.offsetWidth,height:me.offsetHeight},Ne=V(Re);if(m==="none")return{top:null,left:null,transformOrigin:kv(Ne)};const Le=B();let $e=Le.top-Ne.vertical,nt=Le.left-Ne.horizontal;const Ve=$e+Re.height,at=nt+Re.width,he=ir(cu(c)),st=he.innerHeight-M,Te=he.innerWidth-M;if(M!==null&&$e<M){const He=$e-M;$e-=He,Ne.vertical+=He}else if(M!==null&&Ve>st){const He=Ve-st;$e-=He,Ne.vertical+=He}if(M!==null&&nt<M){const He=nt-M;nt-=He,Ne.horizontal+=He}else if(at>Te){const He=at-Te;nt-=He,Ne.horizontal+=He}return{top:`${Math.round($e)}px`,left:`${Math.round(nt)}px`,transformOrigin:kv(Ne)}},[c,m,B,V,M]),[ee,_]=O.useState(E),T=O.useCallback(()=>{const me=G.current;if(!me)return;const Re=ae(me);Re.top!==null&&me.style.setProperty("top",Re.top),Re.left!==null&&(me.style.left=Re.left),me.style.transformOrigin=Re.transformOrigin,_(!0)},[ae]);O.useEffect(()=>(N&&window.addEventListener("scroll",T),()=>window.removeEventListener("scroll",T)),[c,N,T]);const L=()=>{T()},Y=()=>{_(!1)};O.useEffect(()=>{E&&T()}),O.useImperativeHandle(u,()=>E?{updatePosition:()=>{T()}}:null,[E,T]),O.useEffect(()=>{if(!E)return;const me=B0(()=>{T()}),Re=ir(cu(c));return Re.addEventListener("resize",me),()=>{me.clear(),Re.removeEventListener("resize",me)}},[c,E,T]);let X=$;const A={slots:{transition:D,...w},slotProps:{transition:z,paper:v,...j}},[U,J]=wt("transition",{elementType:Id,externalForwardedProps:A,ownerState:K,getSlotProps:me=>({...me,onEntering:(Re,Ne)=>{me.onEntering?.(Re,Ne),L()},onExited:Re=>{me.onExited?.(Re),Y()}}),additionalProps:{appear:!0,in:E}});$==="auto"&&!U.muiSupportAuto&&(X=void 0);const ne=x||(c?In(cu(c)).body:void 0),[fe,{slots:ue,slotProps:le,...ve}]=wt("root",{ref:l,elementType:tw,externalForwardedProps:{...A,...I},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:w.backdrop},slotProps:{backdrop:_0(typeof j.backdrop=="function"?j.backdrop(K):j.backdrop,{invisible:!0})},container:ne,open:E},ownerState:K,className:Me(b.root,y)}),[xe,be]=wt("paper",{ref:G,className:b.paper,elementType:db,externalForwardedProps:A,shouldForwardComponentProp:!0,additionalProps:{elevation:R,style:ee?void 0:{opacity:0}},ownerState:K});return S.jsx(fe,{...ve,...!Pd(fe)&&{slots:ue,slotProps:le,disableScrollLock:N},children:S.jsx(U,{...J,timeout:X,children:S.jsx(xe,{...be,children:h})})})});function aw(n){return Xe("MuiMenu",n)}Ke("MuiMenu",["root","paper","list"]);const rw={vertical:"top",horizontal:"right"},ow={vertical:"top",horizontal:"left"},lw=n=>{const{classes:r}=n;return Qe({root:["root"],paper:["paper"],list:["list"]},aw,r)},iw=pe(nw,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiMenu",slot:"Root"})({}),sw=pe(db,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),uw=pe(ZM,{name:"MuiMenu",slot:"List"})({outline:0}),cw=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiMenu"}),{autoFocus:u=!0,children:c,className:d,disableAutoFocusItem:p=!1,MenuListProps:m={},onClose:h,open:y,PaperProps:x={},PopoverClasses:R,transitionDuration:M="auto",TransitionProps:{onEntering:E,...v}={},variant:w="selectedMenu",slots:j={},slotProps:P={},...D}=i,$=R0(),z={...i,autoFocus:u,disableAutoFocusItem:p,MenuListProps:m,onEntering:E,PaperProps:x,transitionDuration:M,TransitionProps:v,variant:w},N=lw(z),I=u&&!p&&y,G=O.useRef(null),K=(X,A)=>{G.current&&G.current.adjustStyleForScrollbar(X,{direction:$?"rtl":"ltr"}),E&&E(X,A)},b=X=>{X.key==="Tab"&&(X.preventDefault(),h&&h(X,"tabKeyDown"))};let B=-1;O.Children.map(c,(X,A)=>{O.isValidElement(X)&&(X.props.disabled||(w==="selectedMenu"&&X.props.selected||B===-1)&&(B=A))});const V={slots:j,slotProps:{list:m,transition:v,paper:x,...P}},ae=rb({elementType:j.root,externalSlotProps:P.root,ownerState:z,className:[N.root,d]}),[ee,_]=wt("paper",{className:N.paper,elementType:sw,externalForwardedProps:V,shouldForwardComponentProp:!0,ownerState:z}),[T,L]=wt("list",{className:Me(N.list,m.className),elementType:uw,shouldForwardComponentProp:!0,externalForwardedProps:V,getSlotProps:X=>({...X,onKeyDown:A=>{b(A),X.onKeyDown?.(A)}}),ownerState:z}),Y=typeof V.slotProps.transition=="function"?V.slotProps.transition(z):V.slotProps.transition;return S.jsx(iw,{onClose:h,anchorOrigin:{vertical:"bottom",horizontal:$?"right":"left"},transformOrigin:$?rw:ow,slots:{root:j.root,paper:ee,backdrop:j.backdrop,...j.transition&&{transition:j.transition}},slotProps:{root:ae,paper:_,backdrop:typeof P.backdrop=="function"?P.backdrop(z):P.backdrop,transition:{...Y,onEntering:(...X)=>{K(...X),Y?.onEntering?.(...X)}}},open:y,ref:l,transitionDuration:M,ownerState:z,...D,classes:R,children:S.jsx(T,{actions:G,autoFocus:u&&(B===-1||p),autoFocusItem:I,variant:w,...L,children:c})})});function fw(n){return Xe("MuiNativeSelect",n)}const Op=Ke("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),dw=n=>{const{classes:r,variant:l,disabled:i,multiple:u,open:c,error:d}=n,p={select:["select",l,i&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${de(l)}`,c&&"iconOpen",i&&"disabled"]};return Qe(p,fw,r)},pb=pe("select",{name:"MuiNativeSelect"})(({theme:n})=>({MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},[`&.${Op.disabled}`]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(n.vars||n).palette.background.paper},variants:[{props:({ownerState:r})=>r.variant!=="filled"&&r.variant!=="outlined",style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(n.vars||n).shape.borderRadius,"&:focus":{borderRadius:(n.vars||n).shape.borderRadius},"&&&":{paddingRight:32}}}]})),pw=pe(pb,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:Yn,overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.select,r[l.variant],l.error&&r.error,{[`&.${Op.multiple}`]:r.multiple}]}})({}),mb=pe("svg",{name:"MuiNativeSelect"})(({theme:n})=>({position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(n.vars||n).palette.action.active,[`&.${Op.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:({ownerState:r})=>r.open,style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]})),mw=pe(mb,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.icon,l.variant&&r[`icon${de(l.variant)}`],l.open&&r.iconOpen]}})({}),hw=O.forwardRef(function(r,l){const{className:i,disabled:u,error:c,IconComponent:d,inputRef:p,variant:m="standard",...h}=r,y={...r,disabled:u,variant:m,error:c},x=dw(y);return S.jsxs(O.Fragment,{children:[S.jsx(pw,{ownerState:y,className:Me(x.select,i),disabled:u,ref:p||l,...h}),r.multiple?null:S.jsx(mw,{as:d,ownerState:y,className:x.icon})]})});var Nv;const gw=pe("fieldset",{name:"MuiNotchedOutlined",shouldForwardProp:Yn})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),yw=pe("legend",{name:"MuiNotchedOutlined",shouldForwardProp:Yn})(tt(({theme:n})=>({float:"unset",width:"auto",overflow:"hidden",variants:[{props:({ownerState:r})=>!r.withLabel,style:{padding:0,lineHeight:"11px",transition:n.transitions.create("width",{duration:150,easing:n.transitions.easing.easeOut})}},{props:({ownerState:r})=>r.withLabel,style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:n.transitions.create("max-width",{duration:50,easing:n.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:({ownerState:r})=>r.withLabel&&r.notched,style:{maxWidth:"100%",transition:n.transitions.create("max-width",{duration:100,easing:n.transitions.easing.easeOut,delay:50})}}]})));function vw(n){const{children:r,classes:l,className:i,label:u,notched:c,...d}=n,p=u!=null&&u!=="",m={...n,notched:c,withLabel:p};return S.jsx(gw,{"aria-hidden":!0,className:i,ownerState:m,...d,children:S.jsx(yw,{ownerState:m,children:p?S.jsx("span",{children:u}):Nv||(Nv=S.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"}))})})}const bw=n=>{const{classes:r}=n,i=Qe({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},tO,r);return{...r,...i}},Sw=pe(Fu,{shouldForwardProp:n=>Yn(n)||n==="classes",name:"MuiOutlinedInput",slot:"Root",overridesResolver:Ku})(tt(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(n.vars||n).shape.borderRadius,[`&:hover .${ia.notchedOutline}`]:{borderColor:(n.vars||n).palette.text.primary},"@media (hover: none)":{[`&:hover .${ia.notchedOutline}`]:{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}},[`&.${ia.focused} .${ia.notchedOutline}`]:{borderWidth:2},variants:[...Object.entries(n.palette).filter(xn()).map(([l])=>({props:{color:l},style:{[`&.${ia.focused} .${ia.notchedOutline}`]:{borderColor:(n.vars||n).palette[l].main}}})),{props:{},style:{[`&.${ia.error} .${ia.notchedOutline}`]:{borderColor:(n.vars||n).palette.error.main},[`&.${ia.disabled} .${ia.notchedOutline}`]:{borderColor:(n.vars||n).palette.action.disabled}}},{props:({ownerState:l})=>l.startAdornment,style:{paddingLeft:14}},{props:({ownerState:l})=>l.endAdornment,style:{paddingRight:14}},{props:({ownerState:l})=>l.multiline,style:{padding:"16.5px 14px"}},{props:({ownerState:l,size:i})=>l.multiline&&i==="small",style:{padding:"8.5px 14px"}}]}})),xw=pe(vw,{name:"MuiOutlinedInput",slot:"NotchedOutline"})(tt(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}})),Cw=pe(Wu,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:Qu})(tt(({theme:n})=>({padding:"16.5px 14px",...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:({ownerState:r})=>r.multiline,style:{padding:0}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}}]}))),Mp=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiOutlinedInput"}),{components:u={},fullWidth:c=!1,inputComponent:d="input",label:p,multiline:m=!1,notched:h,slots:y={},slotProps:x={},type:R="text",...M}=i,E=bw(i),v=Hr(),w=fo({props:i,muiFormControl:v,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),j={...i,color:w.color||"primary",disabled:w.disabled,error:w.error,focused:w.focused,formControl:v,fullWidth:c,hiddenLabel:w.hiddenLabel,multiline:m,size:w.size,type:R},P=y.root??u.Root??Sw,D=y.input??u.Input??Cw,[$,z]=wt("notchedOutline",{elementType:xw,className:E.notchedOutline,shouldForwardComponentProp:!0,ownerState:j,externalForwardedProps:{slots:y,slotProps:x},additionalProps:{label:p!=null&&p!==""&&w.required?S.jsxs(O.Fragment,{children:[p," ","*"]}):p}});return S.jsx(Tp,{slots:{root:P,input:D},slotProps:x,renderSuffix:N=>S.jsx($,{...z,notched:typeof h<"u"?h:!!(N.startAdornment||N.filled||N.focused)}),fullWidth:c,inputComponent:d,multiline:m,ref:l,type:R,...M,classes:{...E,notchedOutline:null}})});Mp.muiName="Input";function hb(n){return Xe("MuiSelect",n)}const bi=Ke("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]);var Bv;const Tw=pe(pb,{name:"MuiSelect",slot:"Select",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[{[`&.${bi.select}`]:r.select},{[`&.${bi.select}`]:r[l.variant]},{[`&.${bi.error}`]:r.error},{[`&.${bi.multiple}`]:r.multiple}]}})({[`&.${bi.select}`]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),Ew=pe(mb,{name:"MuiSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.icon,l.variant&&r[`icon${de(l.variant)}`],l.open&&r.iconOpen]}})({}),Rw=pe("input",{shouldForwardProp:n=>N0(n)&&n!=="classes",name:"MuiSelect",slot:"NativeInput"})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function _v(n,r){return typeof r=="object"&&r!==null?n===r:String(n)===String(r)}function Ow(n){return n==null||typeof n=="string"&&!n.trim()}const Mw=n=>{const{classes:r,variant:l,disabled:i,multiple:u,open:c,error:d}=n,p={select:["select",l,i&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${de(l)}`,c&&"iconOpen",i&&"disabled"],nativeInput:["nativeInput"]};return Qe(p,hb,r)},ww=O.forwardRef(function(r,l){const{"aria-describedby":i,"aria-label":u,autoFocus:c,autoWidth:d,children:p,className:m,defaultOpen:h,defaultValue:y,disabled:x,displayEmpty:R,error:M=!1,IconComponent:E,inputRef:v,labelId:w,MenuProps:j={},multiple:P,name:D,onBlur:$,onChange:z,onClose:N,onFocus:I,onOpen:G,open:K,readOnly:b,renderValue:B,required:V,SelectDisplayProps:ae={},tabIndex:ee,type:_,value:T,variant:L="standard",...Y}=r,[X,A]=sl({controlled:T,default:y,name:"Select"}),[U,J]=sl({controlled:K,default:h,name:"Select"}),ne=O.useRef(null),fe=O.useRef(null),[ue,le]=O.useState(null),{current:ve}=O.useRef(K!=null),[xe,be]=O.useState(),me=cn(l,v),Re=O.useCallback(Ce=>{fe.current=Ce,Ce&&le(Ce)},[]),Ne=ue?.parentNode;O.useImperativeHandle(me,()=>({focus:()=>{fe.current.focus()},node:ne.current,value:X}),[X]),O.useEffect(()=>{h&&U&&ue&&!ve&&(be(d?null:Ne.clientWidth),fe.current.focus())},[ue,d]),O.useEffect(()=>{c&&fe.current.focus()},[c]),O.useEffect(()=>{if(!w)return;const Ce=In(fe.current).getElementById(w);if(Ce){const Ue=()=>{getSelection().isCollapsed&&fe.current.focus()};return Ce.addEventListener("click",Ue),()=>{Ce.removeEventListener("click",Ue)}}},[w]);const Le=(Ce,Ue)=>{Ce?G&&G(Ue):N&&N(Ue),ve||(be(d?null:Ne.clientWidth),J(Ce))},$e=Ce=>{Ce.button===0&&(Ce.preventDefault(),fe.current.focus(),Le(!0,Ce))},nt=Ce=>{Le(!1,Ce)},Ve=O.Children.toArray(p),at=Ce=>{const Ue=Ve.find(ot=>ot.props.value===Ce.target.value);Ue!==void 0&&(A(Ue.props.value),z&&z(Ce,Ue))},he=Ce=>Ue=>{let ot;if(Ue.currentTarget.hasAttribute("tabindex")){if(P){ot=Array.isArray(X)?X.slice():[];const Gn=X.indexOf(Ce.props.value);Gn===-1?ot.push(Ce.props.value):ot.splice(Gn,1)}else ot=Ce.props.value;if(Ce.props.onClick&&Ce.props.onClick(Ue),X!==ot&&(A(ot),z)){const Gn=Ue.nativeEvent||Ue,ha=new Gn.constructor(Gn.type,Gn);Object.defineProperty(ha,"target",{writable:!0,value:{value:ot,name:D}}),z(ha,Ce)}P||Le(!1,Ue)}},st=Ce=>{b||[" ","ArrowUp","ArrowDown","Enter"].includes(Ce.key)&&(Ce.preventDefault(),Le(!0,Ce))},Te=ue!==null&&U,He=Ce=>{!Te&&$&&(Object.defineProperty(Ce,"target",{writable:!0,value:{value:X,name:D}}),$(Ce))};delete Y["aria-invalid"];let ye,qt;const ut=[];let At=!1;(wu({value:X})||R)&&(B?ye=B(X):At=!0);const rt=Ve.map(Ce=>{if(!O.isValidElement(Ce))return null;let Ue;if(P){if(!Array.isArray(X))throw new Error(or(2));Ue=X.some(ot=>_v(ot,Ce.props.value)),Ue&&At&&ut.push(Ce.props.children)}else Ue=_v(X,Ce.props.value),Ue&&At&&(qt=Ce.props.children);return O.cloneElement(Ce,{"aria-selected":Ue?"true":"false",onClick:he(Ce),onKeyUp:ot=>{ot.key===" "&&ot.preventDefault(),Ce.props.onKeyUp&&Ce.props.onKeyUp(ot)},role:"option",selected:Ue,value:void 0,"data-value":Ce.props.value})});At&&(P?ut.length===0?ye=null:ye=ut.reduce((Ce,Ue,ot)=>(Ce.push(Ue),ot<ut.length-1&&Ce.push(", "),Ce),[]):ye=qt);let ke=xe;!d&&ve&&ue&&(ke=Ne.clientWidth);let Ye;typeof ee<"u"?Ye=ee:Ye=x?null:0;const et=ae.id||(D?`mui-component-select-${D}`:void 0),vt={...r,variant:L,value:X,open:Te,error:M},Ae=Mw(vt),Gt={...j.PaperProps,...j.slotProps?.paper},Cn={...j.MenuListProps,...j.slotProps?.list},Zt=hl();return S.jsxs(O.Fragment,{children:[S.jsx(Tw,{as:"div",ref:Re,tabIndex:Ye,role:"combobox","aria-controls":Te?Zt:void 0,"aria-disabled":x?"true":void 0,"aria-expanded":Te?"true":"false","aria-haspopup":"listbox","aria-label":u,"aria-labelledby":[w,et].filter(Boolean).join(" ")||void 0,"aria-describedby":i,"aria-required":V?"true":void 0,"aria-invalid":M?"true":void 0,onKeyDown:st,onMouseDown:x||b?null:$e,onBlur:He,onFocus:I,...ae,ownerState:vt,className:Me(ae.className,Ae.select,m),id:et,children:Ow(ye)?Bv||(Bv=S.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):ye}),S.jsx(Rw,{"aria-invalid":M,value:Array.isArray(X)?X.join(","):X,name:D,ref:ne,"aria-hidden":!0,onChange:at,tabIndex:-1,disabled:x,className:Ae.nativeInput,autoFocus:c,required:V,...Y,ownerState:vt}),S.jsx(Ew,{as:E,className:Ae.icon,ownerState:vt}),S.jsx(cw,{id:`menu-${D||""}`,anchorEl:Ne,open:Te,onClose:nt,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...j,slotProps:{...j.slotProps,list:{"aria-labelledby":w,role:"listbox","aria-multiselectable":P?"true":void 0,disableListWrap:!0,id:Zt,...Cn},paper:{...Gt,style:{minWidth:ke,...Gt!=null?Gt.style:null}}},children:rt})]})}),Aw=n=>{const{classes:r}=n,i=Qe({root:["root"]},hb,r);return{...r,...i}},wp={name:"MuiSelect",slot:"Root",shouldForwardProp:n=>Yn(n)&&n!=="variant"},zw=pe(Rp,wp)(""),Dw=pe(Mp,wp)(""),$w=pe(Ep,wp)(""),gb=O.forwardRef(function(r,l){const i=Je({name:"MuiSelect",props:r}),{autoWidth:u=!1,children:c,classes:d={},className:p,defaultOpen:m=!1,displayEmpty:h=!1,IconComponent:y=ib,id:x,input:R,inputProps:M,label:E,labelId:v,MenuProps:w,multiple:j=!1,native:P=!1,onClose:D,onOpen:$,open:z,renderValue:N,SelectDisplayProps:I,variant:G="outlined",...K}=i,b=P?hw:ww,B=Hr(),V=fo({props:i,muiFormControl:B,states:["variant","error"]}),ae=V.variant||G,ee={...i,variant:ae,classes:d},_=Aw(ee),{root:T,...L}=_,Y=R||{standard:S.jsx(zw,{ownerState:ee}),outlined:S.jsx(Dw,{label:E,ownerState:ee}),filled:S.jsx($w,{ownerState:ee})}[ae],X=cn(l,Xi(Y));return S.jsx(O.Fragment,{children:O.cloneElement(Y,{inputComponent:b,inputProps:{children:c,error:V.error,IconComponent:y,variant:ae,type:void 0,multiple:j,...P?{id:x}:{autoWidth:u,defaultOpen:m,displayEmpty:h,labelId:v,MenuProps:w,onClose:D,onOpen:$,open:z,renderValue:N,SelectDisplayProps:{id:x,...I}},...M,classes:M?gn(L,M.classes):L,...R?R.props.inputProps:{}},...(j&&P||h)&&ae==="outlined"?{notched:!0}:{},ref:X,className:Me(Y.props.className,p,_.root),...!R&&{variant:ae},...K})})});gb.muiName="Select";const yb=O.createContext();function jw(n){return Xe("MuiTable",n)}Ke("MuiTable",["root","stickyHeader"]);const kw=n=>{const{classes:r,stickyHeader:l}=n;return Qe({root:["root",l&&"stickyHeader"]},jw,r)},Nw=pe("table",{name:"MuiTable",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.stickyHeader&&r.stickyHeader]}})(tt(({theme:n})=>({display:"table",width:"100%",borderCollapse:"collapse",borderSpacing:0,"& caption":{...n.typography.body2,padding:n.spacing(2),color:(n.vars||n).palette.text.secondary,textAlign:"left",captionSide:"bottom"},variants:[{props:({ownerState:r})=>r.stickyHeader,style:{borderCollapse:"separate"}}]}))),Lv="table",Bw=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTable"}),{className:u,component:c=Lv,padding:d="normal",size:p="medium",stickyHeader:m=!1,...h}=i,y={...i,component:c,padding:d,size:p,stickyHeader:m},x=kw(y),R=O.useMemo(()=>({padding:d,size:p,stickyHeader:m}),[d,p,m]);return S.jsx(yb.Provider,{value:R,children:S.jsx(Nw,{as:c,role:c===Lv?null:"table",ref:l,className:Me(x.root,u),ownerState:y,...h})})}),Zu=O.createContext();function _w(n){return Xe("MuiTableBody",n)}Ke("MuiTableBody",["root"]);const Lw=n=>{const{classes:r}=n;return Qe({root:["root"]},_w,r)},Hw=pe("tbody",{name:"MuiTableBody",slot:"Root"})({display:"table-row-group"}),Pw={variant:"body"},Hv="tbody",Uw=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableBody"}),{className:u,component:c=Hv,...d}=i,p={...i,component:c},m=Lw(p);return S.jsx(Zu.Provider,{value:Pw,children:S.jsx(Hw,{className:Me(m.root,u),as:c,ref:l,role:c===Hv?null:"rowgroup",ownerState:p,...d})})});function qw(n){return Xe("MuiTableCell",n)}const Iw=Ke("MuiTableCell",["root","head","body","footer","sizeSmall","sizeMedium","paddingCheckbox","paddingNone","alignLeft","alignCenter","alignRight","alignJustify","stickyHeader"]),Vw=n=>{const{classes:r,variant:l,align:i,padding:u,size:c,stickyHeader:d}=n,p={root:["root",l,d&&"stickyHeader",i!=="inherit"&&`align${de(i)}`,u!=="normal"&&`padding${de(u)}`,`size${de(c)}`]};return Qe(p,qw,r)},Yw=pe("td",{name:"MuiTableCell",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,r[l.variant],r[`size${de(l.size)}`],l.padding!=="normal"&&r[`padding${de(l.padding)}`],l.align!=="inherit"&&r[`align${de(l.align)}`],l.stickyHeader&&r.stickyHeader]}})(tt(({theme:n})=>({...n.typography.body2,display:"table-cell",verticalAlign:"inherit",borderBottom:n.vars?`1px solid ${n.vars.palette.TableCell.border}`:`1px solid
    ${n.palette.mode==="light"?Iu(yt(n.palette.divider,1),.88):qu(yt(n.palette.divider,1),.68)}`,textAlign:"left",padding:16,variants:[{props:{variant:"head"},style:{color:(n.vars||n).palette.text.primary,lineHeight:n.typography.pxToRem(24),fontWeight:n.typography.fontWeightMedium}},{props:{variant:"body"},style:{color:(n.vars||n).palette.text.primary}},{props:{variant:"footer"},style:{color:(n.vars||n).palette.text.secondary,lineHeight:n.typography.pxToRem(21),fontSize:n.typography.pxToRem(12)}},{props:{size:"small"},style:{padding:"6px 16px",[`&.${Iw.paddingCheckbox}`]:{width:24,padding:"0 12px 0 16px","& > *":{padding:0}}}},{props:{padding:"checkbox"},style:{width:48,padding:"0 0 0 4px"}},{props:{padding:"none"},style:{padding:0}},{props:{align:"left"},style:{textAlign:"left"}},{props:{align:"center"},style:{textAlign:"center"}},{props:{align:"right"},style:{textAlign:"right",flexDirection:"row-reverse"}},{props:{align:"justify"},style:{textAlign:"justify"}},{props:({ownerState:r})=>r.stickyHeader,style:{position:"sticky",top:0,zIndex:2,backgroundColor:(n.vars||n).palette.background.default}}]}))),Da=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableCell"}),{align:u="inherit",className:c,component:d,padding:p,scope:m,size:h,sortDirection:y,variant:x,...R}=i,M=O.useContext(yb),E=O.useContext(Zu),v=E&&E.variant==="head";let w;d?w=d:w=v?"th":"td";let j=m;w==="td"?j=void 0:!j&&v&&(j="col");const P=x||E&&E.variant,D={...i,align:u,component:w,padding:p||(M&&M.padding?M.padding:"normal"),size:h||(M&&M.size?M.size:"medium"),sortDirection:y,stickyHeader:P==="head"&&M&&M.stickyHeader,variant:P},$=Vw(D);let z=null;return y&&(z=y==="asc"?"ascending":"descending"),S.jsx(Yw,{as:w,ref:l,className:Me($.root,c),"aria-sort":z,scope:j,ownerState:D,...R})});function Gw(n){return Xe("MuiTableContainer",n)}Ke("MuiTableContainer",["root"]);const Xw=n=>{const{classes:r}=n;return Qe({root:["root"]},Gw,r)},Kw=pe("div",{name:"MuiTableContainer",slot:"Root"})({width:"100%",overflowX:"auto"}),Qw=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableContainer"}),{className:u,component:c="div",...d}=i,p={...i,component:c},m=Xw(p);return S.jsx(Kw,{ref:l,as:c,className:Me(m.root,u),ownerState:p,...d})});function Fw(n){return Xe("MuiTableHead",n)}Ke("MuiTableHead",["root"]);const Ww=n=>{const{classes:r}=n;return Qe({root:["root"]},Fw,r)},Zw=pe("thead",{name:"MuiTableHead",slot:"Root"})({display:"table-header-group"}),Jw={variant:"head"},Pv="thead",e5=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableHead"}),{className:u,component:c=Pv,...d}=i,p={...i,component:c},m=Ww(p);return S.jsx(Zu.Provider,{value:Jw,children:S.jsx(Zw,{as:c,className:Me(m.root,u),ref:l,role:c===Pv?null:"rowgroup",ownerState:p,...d})})});function t5(n){return Xe("MuiTableRow",n)}const Uv=Ke("MuiTableRow",["root","selected","hover","head","footer"]),n5=n=>{const{classes:r,selected:l,hover:i,head:u,footer:c}=n;return Qe({root:["root",l&&"selected",i&&"hover",u&&"head",c&&"footer"]},t5,r)},a5=pe("tr",{name:"MuiTableRow",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:l}=n;return[r.root,l.head&&r.head,l.footer&&r.footer]}})(tt(({theme:n})=>({color:"inherit",display:"table-row",verticalAlign:"middle",outline:0,[`&.${Uv.hover}:hover`]:{backgroundColor:(n.vars||n).palette.action.hover},[`&.${Uv.selected}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:yt(n.palette.primary.main,n.palette.action.selectedOpacity),"&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:yt(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity)}}}))),qv="tr",Iv=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTableRow"}),{className:u,component:c=qv,hover:d=!1,selected:p=!1,...m}=i,h=O.useContext(Zu),y={...i,component:c,hover:d,selected:p,head:h&&h.variant==="head",footer:h&&h.variant==="footer"},x=n5(y);return S.jsx(a5,{as:c,ref:l,className:Me(x.root,u),role:c===qv?null:"row",ownerState:y,...m})});function r5(n){return Xe("MuiTextField",n)}Ke("MuiTextField",["root"]);const o5={standard:Rp,filled:Ep,outlined:Mp},l5=n=>{const{classes:r}=n;return Qe({root:["root"]},r5,r)},i5=pe(EM,{name:"MuiTextField",slot:"Root"})({}),Ft=O.forwardRef(function(r,l){const i=Je({props:r,name:"MuiTextField"}),{autoComplete:u,autoFocus:c=!1,children:d,className:p,color:m="primary",defaultValue:h,disabled:y=!1,error:x=!1,FormHelperTextProps:R,fullWidth:M=!1,helperText:E,id:v,InputLabelProps:w,inputProps:j,InputProps:P,inputRef:D,label:$,maxRows:z,minRows:N,multiline:I=!1,name:G,onBlur:K,onChange:b,onFocus:B,placeholder:V,required:ae=!1,rows:ee,select:_=!1,SelectProps:T,slots:L={},slotProps:Y={},type:X,value:A,variant:U="outlined",...J}=i,ne={...i,autoFocus:c,color:m,disabled:y,error:x,fullWidth:M,multiline:I,required:ae,select:_,variant:U},fe=l5(ne),ue=hl(v),le=E&&ue?`${ue}-helper-text`:void 0,ve=$&&ue?`${ue}-label`:void 0,xe=o5[U],be={slots:L,slotProps:{input:P,inputLabel:w,htmlInput:j,formHelperText:R,select:T,...Y}},me={},Re=be.slotProps.inputLabel;U==="outlined"&&(Re&&typeof Re.shrink<"u"&&(me.notched=Re.shrink),me.label=$),_&&((!T||!T.native)&&(me.id=void 0),me["aria-describedby"]=void 0);const[Ne,Le]=wt("root",{elementType:i5,shouldForwardComponentProp:!0,externalForwardedProps:{...be,...J},ownerState:ne,className:Me(fe.root,p),ref:l,additionalProps:{disabled:y,error:x,fullWidth:M,required:ae,color:m,variant:U}}),[$e,nt]=wt("input",{elementType:xe,externalForwardedProps:be,additionalProps:me,ownerState:ne}),[Ve,at]=wt("inputLabel",{elementType:GM,externalForwardedProps:be,ownerState:ne}),[he,st]=wt("htmlInput",{elementType:"input",externalForwardedProps:be,ownerState:ne}),[Te,He]=wt("formHelperText",{elementType:jM,externalForwardedProps:be,ownerState:ne}),[ye,qt]=wt("select",{elementType:gb,externalForwardedProps:be,ownerState:ne}),ut=S.jsx($e,{"aria-describedby":le,autoComplete:u,autoFocus:c,defaultValue:h,fullWidth:M,multiline:I,name:G,rows:ee,maxRows:z,minRows:N,type:X,value:A,id:ue,inputRef:D,onBlur:K,onChange:b,onFocus:B,placeholder:V,inputProps:st,slots:{input:L.htmlInput?he:void 0},...nt});return S.jsxs(Ne,{...Le,children:[$!=null&&$!==""&&S.jsx(Ve,{htmlFor:ue,id:ve,...at,children:$}),_?S.jsx(ye,{"aria-describedby":le,id:ue,labelId:ve,value:A,input:ut,...qt,children:d}):ut,E&&S.jsx(Te,{id:le,...He,children:E})]})}),s5=x0({themeId:Ta}),u5=fn(S.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),c5=fn(S.jsx("path",{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m2 14H7v-2h7zm3-4H7v-2h10zm0-4H7V7h10z"})),f5=fn(S.jsx("path",{d:"M4 9h4v11H4zm12 4h4v7h-4zm-6-9h4v16h-4z"})),d5=fn(S.jsx("path",{d:"M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 18H4V8h16z"})),p5=fn(S.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"})),m5=fn(S.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"})),h5=fn(S.jsx("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"})),g5=fn([S.jsx("circle",{cx:"12",cy:"19",r:"2"},"0"),S.jsx("path",{d:"M10 3h4v12h-4z"},"1")]),y5=fn(S.jsx("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"})),v5=fn(S.jsx("path",{d:"M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"})),b5=fn(S.jsx("path",{d:"M1 21h22L12 2zm12-3h-2v-2h2zm0-4h-2v-4h2z"}));var vu={exports:{}},S5=vu.exports,Vv;function x5(){return Vv||(Vv=1,function(n,r){(function(l,i){n.exports=i()})(S5,function(){var l=1e3,i=6e4,u=36e5,c="millisecond",d="second",p="minute",m="hour",h="day",y="week",x="month",R="quarter",M="year",E="date",v="Invalid Date",w=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,j=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,P={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(ee){var _=["th","st","nd","rd"],T=ee%100;return"["+ee+(_[(T-20)%10]||_[T]||_[0])+"]"}},D=function(ee,_,T){var L=String(ee);return!L||L.length>=_?ee:""+Array(_+1-L.length).join(T)+ee},$={s:D,z:function(ee){var _=-ee.utcOffset(),T=Math.abs(_),L=Math.floor(T/60),Y=T%60;return(_<=0?"+":"-")+D(L,2,"0")+":"+D(Y,2,"0")},m:function ee(_,T){if(_.date()<T.date())return-ee(T,_);var L=12*(T.year()-_.year())+(T.month()-_.month()),Y=_.clone().add(L,x),X=T-Y<0,A=_.clone().add(L+(X?-1:1),x);return+(-(L+(T-Y)/(X?Y-A:A-Y))||0)},a:function(ee){return ee<0?Math.ceil(ee)||0:Math.floor(ee)},p:function(ee){return{M:x,y:M,w:y,d:h,D:E,h:m,m:p,s:d,ms:c,Q:R}[ee]||String(ee||"").toLowerCase().replace(/s$/,"")},u:function(ee){return ee===void 0}},z="en",N={};N[z]=P;var I="$isDayjsObject",G=function(ee){return ee instanceof V||!(!ee||!ee[I])},K=function ee(_,T,L){var Y;if(!_)return z;if(typeof _=="string"){var X=_.toLowerCase();N[X]&&(Y=X),T&&(N[X]=T,Y=X);var A=_.split("-");if(!Y&&A.length>1)return ee(A[0])}else{var U=_.name;N[U]=_,Y=U}return!L&&Y&&(z=Y),Y||!L&&z},b=function(ee,_){if(G(ee))return ee.clone();var T=typeof _=="object"?_:{};return T.date=ee,T.args=arguments,new V(T)},B=$;B.l=K,B.i=G,B.w=function(ee,_){return b(ee,{locale:_.$L,utc:_.$u,x:_.$x,$offset:_.$offset})};var V=function(){function ee(T){this.$L=K(T.locale,null,!0),this.parse(T),this.$x=this.$x||T.x||{},this[I]=!0}var _=ee.prototype;return _.parse=function(T){this.$d=function(L){var Y=L.date,X=L.utc;if(Y===null)return new Date(NaN);if(B.u(Y))return new Date;if(Y instanceof Date)return new Date(Y);if(typeof Y=="string"&&!/Z$/i.test(Y)){var A=Y.match(w);if(A){var U=A[2]-1||0,J=(A[7]||"0").substring(0,3);return X?new Date(Date.UTC(A[1],U,A[3]||1,A[4]||0,A[5]||0,A[6]||0,J)):new Date(A[1],U,A[3]||1,A[4]||0,A[5]||0,A[6]||0,J)}}return new Date(Y)}(T),this.init()},_.init=function(){var T=this.$d;this.$y=T.getFullYear(),this.$M=T.getMonth(),this.$D=T.getDate(),this.$W=T.getDay(),this.$H=T.getHours(),this.$m=T.getMinutes(),this.$s=T.getSeconds(),this.$ms=T.getMilliseconds()},_.$utils=function(){return B},_.isValid=function(){return this.$d.toString()!==v},_.isSame=function(T,L){var Y=b(T);return this.startOf(L)<=Y&&Y<=this.endOf(L)},_.isAfter=function(T,L){return b(T)<this.startOf(L)},_.isBefore=function(T,L){return this.endOf(L)<b(T)},_.$g=function(T,L,Y){return B.u(T)?this[L]:this.set(Y,T)},_.unix=function(){return Math.floor(this.valueOf()/1e3)},_.valueOf=function(){return this.$d.getTime()},_.startOf=function(T,L){var Y=this,X=!!B.u(L)||L,A=B.p(T),U=function(be,me){var Re=B.w(Y.$u?Date.UTC(Y.$y,me,be):new Date(Y.$y,me,be),Y);return X?Re:Re.endOf(h)},J=function(be,me){return B.w(Y.toDate()[be].apply(Y.toDate("s"),(X?[0,0,0,0]:[23,59,59,999]).slice(me)),Y)},ne=this.$W,fe=this.$M,ue=this.$D,le="set"+(this.$u?"UTC":"");switch(A){case M:return X?U(1,0):U(31,11);case x:return X?U(1,fe):U(0,fe+1);case y:var ve=this.$locale().weekStart||0,xe=(ne<ve?ne+7:ne)-ve;return U(X?ue-xe:ue+(6-xe),fe);case h:case E:return J(le+"Hours",0);case m:return J(le+"Minutes",1);case p:return J(le+"Seconds",2);case d:return J(le+"Milliseconds",3);default:return this.clone()}},_.endOf=function(T){return this.startOf(T,!1)},_.$set=function(T,L){var Y,X=B.p(T),A="set"+(this.$u?"UTC":""),U=(Y={},Y[h]=A+"Date",Y[E]=A+"Date",Y[x]=A+"Month",Y[M]=A+"FullYear",Y[m]=A+"Hours",Y[p]=A+"Minutes",Y[d]=A+"Seconds",Y[c]=A+"Milliseconds",Y)[X],J=X===h?this.$D+(L-this.$W):L;if(X===x||X===M){var ne=this.clone().set(E,1);ne.$d[U](J),ne.init(),this.$d=ne.set(E,Math.min(this.$D,ne.daysInMonth())).$d}else U&&this.$d[U](J);return this.init(),this},_.set=function(T,L){return this.clone().$set(T,L)},_.get=function(T){return this[B.p(T)]()},_.add=function(T,L){var Y,X=this;T=Number(T);var A=B.p(L),U=function(fe){var ue=b(X);return B.w(ue.date(ue.date()+Math.round(fe*T)),X)};if(A===x)return this.set(x,this.$M+T);if(A===M)return this.set(M,this.$y+T);if(A===h)return U(1);if(A===y)return U(7);var J=(Y={},Y[p]=i,Y[m]=u,Y[d]=l,Y)[A]||1,ne=this.$d.getTime()+T*J;return B.w(ne,this)},_.subtract=function(T,L){return this.add(-1*T,L)},_.format=function(T){var L=this,Y=this.$locale();if(!this.isValid())return Y.invalidDate||v;var X=T||"YYYY-MM-DDTHH:mm:ssZ",A=B.z(this),U=this.$H,J=this.$m,ne=this.$M,fe=Y.weekdays,ue=Y.months,le=Y.meridiem,ve=function(me,Re,Ne,Le){return me&&(me[Re]||me(L,X))||Ne[Re].slice(0,Le)},xe=function(me){return B.s(U%12||12,me,"0")},be=le||function(me,Re,Ne){var Le=me<12?"AM":"PM";return Ne?Le.toLowerCase():Le};return X.replace(j,function(me,Re){return Re||function(Ne){switch(Ne){case"YY":return String(L.$y).slice(-2);case"YYYY":return B.s(L.$y,4,"0");case"M":return ne+1;case"MM":return B.s(ne+1,2,"0");case"MMM":return ve(Y.monthsShort,ne,ue,3);case"MMMM":return ve(ue,ne);case"D":return L.$D;case"DD":return B.s(L.$D,2,"0");case"d":return String(L.$W);case"dd":return ve(Y.weekdaysMin,L.$W,fe,2);case"ddd":return ve(Y.weekdaysShort,L.$W,fe,3);case"dddd":return fe[L.$W];case"H":return String(U);case"HH":return B.s(U,2,"0");case"h":return xe(1);case"hh":return xe(2);case"a":return be(U,J,!0);case"A":return be(U,J,!1);case"m":return String(J);case"mm":return B.s(J,2,"0");case"s":return String(L.$s);case"ss":return B.s(L.$s,2,"0");case"SSS":return B.s(L.$ms,3,"0");case"Z":return A}return null}(me)||A.replace(":","")})},_.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},_.diff=function(T,L,Y){var X,A=this,U=B.p(L),J=b(T),ne=(J.utcOffset()-this.utcOffset())*i,fe=this-J,ue=function(){return B.m(A,J)};switch(U){case M:X=ue()/12;break;case x:X=ue();break;case R:X=ue()/3;break;case y:X=(fe-ne)/6048e5;break;case h:X=(fe-ne)/864e5;break;case m:X=fe/u;break;case p:X=fe/i;break;case d:X=fe/l;break;default:X=fe}return Y?X:B.a(X)},_.daysInMonth=function(){return this.endOf(x).$D},_.$locale=function(){return N[this.$L]},_.locale=function(T,L){if(!T)return this.$L;var Y=this.clone(),X=K(T,L,!0);return X&&(Y.$L=X),Y},_.clone=function(){return B.w(this.$d,this)},_.toDate=function(){return new Date(this.valueOf())},_.toJSON=function(){return this.isValid()?this.toISOString():null},_.toISOString=function(){return this.$d.toISOString()},_.toString=function(){return this.$d.toUTCString()},ee}(),ae=V.prototype;return b.prototype=ae,[["$ms",c],["$s",d],["$m",p],["$H",m],["$W",h],["$M",x],["$y",M],["$D",E]].forEach(function(ee){ae[ee[1]]=function(_){return this.$g(_,ee[0],ee[1])}}),b.extend=function(ee,_){return ee.$i||(ee(_,V,b),ee.$i=!0),b},b.locale=K,b.isDayjs=G,b.unix=function(ee){return b(1e3*ee)},b.en=N[z],b.Ls=N,b.p={},b})}(vu)),vu.exports}var C5=x5();const $a=Vd(C5),vb=Yu({palette:{primary:{main:"#2EC0CB"},secondary:{main:"#6c757d"},success:{main:"#28a745",light:"#d4edda",contrastText:"#155724"},info:{main:"#17a2b8"},error:{main:"#dc3545"},background:{default:"#f4f6f8",paper:"#ffffff"}},typography:{fontFamily:'Roobert, "Inter", sans-serif'},components:{MuiCssBaseline:{styleOverrides:`
        body {
          font-family: 'Roobert', "Inter", sans-serif;
          background-color: #f4f6f8; /* Explicitly set a light background for the body */
        }
      `},MuiButton:{styleOverrides:{root:{borderRadius:8}}},MuiPaper:{styleOverrides:{root:{borderRadius:8}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8}}}},MuiAutocomplete:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8}}}}}}),Yv=["IOCS","SWServices","Total-Exp","Tru-North"],T5=["Project Management","Training","Meeting","Documentation"],E5=["Discovery Phase","Not Yet Assigned","On Hold","Waiting for Customer Approval","Waiting for Internal Approval"],R5=["Work in Progress","On Hold","Completed","Request for PCD Change Approval","Request for Closure Approval"],O5=["admin","Bibhash Kanti Roy","Bryce Connors","David Campbell","John Talbert","Nikil Ram D","Niraj kumar Mishra","Prasad H","Ravi Tomar","Sreekrishna Narayana","Stuart Mckay","Thomas Remmel","Vishwanath A.B"],M5=["Daily","Weekly","Monthly","Yearly"],Gv=[{id:1,fileName:"ProjectPlan.pdf",description:"Initial project plan",uploadedBy:"admin",uploadedDate:"2024-06-15"},{id:2,fileName:"RequirementsDoc.docx",description:"Detailed requirements",uploadedBy:"John Talbert",uploadedDate:"2024-06-20"}];function w5(){const n=vb,r=s5(n.breakpoints.down("sm")),[l,i]=O.useState([]),[u,c]=O.useState({id:null,taskId:"",taskName:"",taskDescription:"",project:null,division:"",customer:"",taskType:null,currentStatus:null,taskStatus:null,assignedTo:null,estimatedHours:"",actualHours:"",taskPCD:"",startDate:"",endDate:"",repeatTask:!1,repeatFrequency:null,comments:"",completed:!1}),d=v=>{const{name:w,value:j}=v.target;c(P=>({...P,[w]:j}))},p=(v,w)=>{c(j=>({...j,[v]:w}))},m=v=>{const{name:w,value:j}=v.target;c(P=>({...P,[w]:j}))},h=v=>{const w=v.target.checked;c(j=>({...j,repeatTask:w,repeatFrequency:w?j.repeatFrequency:null}))},y=()=>{if(u.taskName.trim()===""){console.log("Task Name cannot be empty.");return}u.id?i(l.map(v=>v.id===u.id?{...v,...u}:v)):i([...l,{...u,id:Date.now(),completed:!1}]),E()},x=v=>{i(l.filter(w=>w.id!==v))},R=v=>{i(l.map(w=>w.id===v?{...w,completed:!w.completed}:w))},M=v=>{c({...v,taskPCD:v.taskPCD?$a(v.taskPCD).format("YYYY-MM-DD"):"",startDate:v.startDate?$a(v.startDate).format("YYYY-MM-DD"):"",endDate:v.endDate?$a(v.endDate).format("YYYY-MM-DD"):""})},E=()=>{c({id:null,taskId:"",taskName:"",taskDescription:"",project:null,division:"",customer:"",taskType:null,currentStatus:null,taskStatus:null,assignedTo:null,estimatedHours:"",actualHours:"",taskPCD:"",startDate:"",endDate:"",repeatTask:!1,repeatFrequency:null,comments:"",completed:!1})};return S.jsxs(Ht,{sx:{width:"100vw",height:"100vh",backgroundColor:"#f8f9fa",overflow:"hidden",display:"flex",flexDirection:"column"},children:[S.jsx(Ht,{sx:{px:{xs:1,sm:2,md:3},py:{xs:1,sm:1.5,md:2},borderBottom:"1px solid #e0e0e0",backgroundColor:"white",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:S.jsx(_e,{variant:"h4",component:"h1",align:"center",sx:{color:n.palette.primary.main,fontWeight:700,fontSize:{xs:"1.5rem",sm:"1.8rem",md:"2.2rem",lg:"2.5rem"},fontFamily:"Roobert, sans-serif",margin:0},children:"My Detailed Task Tracker"})}),S.jsx(Ht,{sx:{flex:1,overflow:"hidden",px:{xs:1,sm:2,md:3},py:{xs:1,sm:1.5,md:2}},children:S.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},sx:{width:"100%",height:"100%",overflow:"hidden"},children:[S.jsxs(we,{item:!0,xs:12,lg:8,xl:9,sx:{height:"100%",overflow:"auto","&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#c1c1c1",borderRadius:"3px"}},children:[S.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[S.jsx(we,{item:!0,xs:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5}},children:[S.jsx(_e,{variant:"h6",component:"h2",sx:{mb:1.5,color:n.palette.primary.dark,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Basic Task Information"}),S.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[S.jsx(we,{item:!0,xs:12,sm:6,md:4,lg:3,xl:3,children:S.jsx(Ft,{fullWidth:!0,label:"Task ID",name:"taskId",value:u.taskId,onChange:d,variant:"outlined",size:r?"small":"medium"})}),S.jsx(we,{item:!0,xs:12,sm:6,md:4,lg:3,xl:3,children:S.jsx(Ft,{fullWidth:!0,label:"Task Name *",name:"taskName",value:u.taskName,onChange:d,variant:"outlined",size:r?"small":"medium"})}),S.jsx(we,{item:!0,xs:12,sm:6,md:4,lg:3,xl:3,children:S.jsx(Ft,{fullWidth:!0,label:"Priority",name:"priority",value:u.priority||"",onChange:d,variant:"outlined",placeholder:"High/Medium/Low",size:r?"small":"medium"})}),S.jsx(we,{item:!0,xs:12,sm:6,md:4,lg:3,xl:3,children:S.jsx(Ft,{fullWidth:!0,label:"Category",name:"category",value:u.category||"",onChange:d,variant:"outlined",placeholder:"Enter category",size:r?"small":"medium"})}),S.jsx(we,{item:!0,xs:12,children:S.jsx(Ft,{fullWidth:!0,label:"Task Description *",name:"taskDescription",value:u.taskDescription,onChange:d,variant:"outlined",multiline:!0,rows:3})})]})]})}),S.jsx(we,{item:!0,xs:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5}},children:[S.jsx(_e,{variant:"h6",component:"h2",sx:{mb:1.5,color:n.palette.primary.dark,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Project & Organizational Details"}),S.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[S.jsx(we,{item:!0,xs:12,sm:6,md:4,lg:3,xl:3,children:S.jsx(nl,{fullWidth:!0,options:Yv,value:u.project,onChange:(v,w)=>p("project",w),renderInput:v=>S.jsx(Ft,{...v,label:"Project *",variant:"outlined",size:r?"small":"medium"})})}),S.jsx(we,{item:!0,xs:12,sm:6,md:4,lg:3,xl:3,children:S.jsx(Ft,{fullWidth:!0,label:"Division",name:"division",value:u.division,onChange:d,variant:"outlined",size:r?"small":"medium"})}),S.jsx(we,{item:!0,xs:12,sm:6,md:4,lg:3,xl:3,children:S.jsx(Ft,{fullWidth:!0,label:"Customer",name:"customer",value:u.customer,onChange:d,variant:"outlined",size:r?"small":"medium"})}),S.jsx(we,{item:!0,xs:12,sm:6,md:4,lg:3,xl:3,children:S.jsx(Ft,{fullWidth:!0,label:"Department",name:"department",value:u.department||"",onChange:d,variant:"outlined",placeholder:"Enter department",size:r?"small":"medium"})})]})]})}),S.jsx(we,{item:!0,xs:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5}},children:[S.jsx(_e,{variant:"h6",component:"h2",sx:{mb:1.5,color:n.palette.primary.dark,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Task Classification & Assignment"}),S.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(nl,{fullWidth:!0,options:T5,value:u.taskType,onChange:(v,w)=>p("taskType",w),renderInput:v=>S.jsx(Ft,{...v,label:"Task Type *",variant:"outlined"})})}),S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(nl,{fullWidth:!0,options:E5,value:u.currentStatus,onChange:(v,w)=>p("currentStatus",w),renderInput:v=>S.jsx(Ft,{...v,label:"Current Status *",variant:"outlined"})})}),S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(nl,{fullWidth:!0,options:R5,value:u.taskStatus,onChange:(v,w)=>p("taskStatus",w),renderInput:v=>S.jsx(Ft,{...v,label:"Task Status",variant:"outlined"})})}),S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(nl,{fullWidth:!0,options:O5,value:u.assignedTo,onChange:(v,w)=>p("assignedTo",w),renderInput:v=>S.jsx(Ft,{...v,label:"Assigned To",variant:"outlined"})})})]})]})}),S.jsx(we,{item:!0,xs:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5}},children:[S.jsx(_e,{variant:"h6",component:"h2",sx:{mb:1.5,color:n.palette.primary.dark,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Scheduling & Effort"}),S.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(Ft,{fullWidth:!0,label:"Estimated Hours (hh:mm) *",name:"estimatedHours",value:u.estimatedHours,onChange:d,variant:"outlined",placeholder:"HH:MM"})}),S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(Ft,{fullWidth:!0,label:"Actual Hours",name:"actualHours",value:u.actualHours,onChange:d,variant:"outlined",placeholder:"HH:MM"})}),S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(Ft,{fullWidth:!0,label:"Task PCD *",name:"taskPCD",type:"date",value:u.taskPCD,onChange:m,variant:"outlined",InputLabelProps:{shrink:!0}})}),S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(Ft,{fullWidth:!0,label:"Start Date",name:"startDate",type:"date",value:u.startDate,onChange:m,variant:"outlined",InputLabelProps:{shrink:!0}})}),S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(Ft,{fullWidth:!0,label:"End Date",name:"endDate",type:"date",value:u.endDate,onChange:m,variant:"outlined",InputLabelProps:{shrink:!0}})}),S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(AM,{control:S.jsx(GO,{checked:u.repeatTask,onChange:h,name:"repeatTask",color:"primary"}),label:"Repeat Task"})}),u.repeatTask&&S.jsx(we,{item:!0,xs:12,sm:6,md:3,children:S.jsx(nl,{fullWidth:!0,options:M5,value:u.repeatFrequency,onChange:(v,w)=>p("repeatFrequency",w),renderInput:v=>S.jsx(Ft,{...v,label:"Repeat Frequency",variant:"outlined"})})})]})]})}),S.jsx(we,{item:!0,xs:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5}},children:[S.jsx(_e,{variant:"h6",component:"h2",sx:{mb:1.5,color:n.palette.primary.dark,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Comments"}),S.jsx(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:S.jsx(we,{item:!0,xs:12,children:S.jsx(Ft,{fullWidth:!0,label:"Comments",name:"comments",value:u.comments,onChange:d,variant:"outlined",multiline:!0,rows:2,size:r?"small":"medium"})})})]})}),S.jsx(we,{item:!0,xs:12,children:S.jsxs(Ht,{sx:{display:"flex",gap:2,flexDirection:r?"column":"row",justifyContent:"flex-end",mt:2},children:[S.jsx(Rv,{variant:"contained",color:"primary",startIcon:S.jsx(u5,{}),onClick:y,fullWidth:r,children:u.id?"Update Task":"Add Task"}),S.jsx(Rv,{variant:"outlined",color:"secondary",startIcon:S.jsx(y5,{}),onClick:E,fullWidth:r,children:"Clear Form"})]})})]})," "]})," ",S.jsx(we,{item:!0,xs:12,lg:4,xl:3,sx:{height:"100%",overflow:"auto","&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#c1c1c1",borderRadius:"3px"}},children:S.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[S.jsx(we,{item:!0,xs:12,sm:6,lg:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[S.jsxs(_e,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:n.palette.primary.dark,display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[S.jsx(d5,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Today's Tasks"]}),l.filter(v=>{const w=$a().format("YYYY-MM-DD");return v.startDate===w||v.taskPCD===w}).length===0?S.jsx(_e,{variant:"body2",color:"textSecondary",children:"No tasks scheduled for today"}):S.jsx(Ht,{children:l.filter(v=>{const w=$a().format("YYYY-MM-DD");return v.startDate===w||v.taskPCD===w}).slice(0,3).map(v=>S.jsxs(Ht,{sx:{mb:1,p:1,backgroundColor:"#f5f5f5",borderRadius:1},children:[S.jsx(_e,{variant:"body2",fontWeight:"bold",children:v.taskName}),S.jsx(_e,{variant:"caption",color:"textSecondary",children:v.project?.label||"No Project"})]},v.id))})]})}),S.jsx(we,{item:!0,xs:12,sm:6,lg:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[S.jsxs(_e,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:n.palette.primary.dark,display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[S.jsx(g5,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Priority Breakdown"]}),S.jsx(Ht,{children:["High","Medium","Low"].map(v=>{const w=l.filter(P=>P.priority===v&&!P.completed).length,j=v==="High"?"#f44336":v==="Medium"?"#ff9800":"#4caf50";return S.jsxs(Ht,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[S.jsxs(Ht,{sx:{display:"flex",alignItems:"center"},children:[S.jsx(Ht,{sx:{width:12,height:12,backgroundColor:j,borderRadius:"50%",mr:1}}),S.jsxs(_e,{variant:"body2",children:[v," Priority"]})]}),S.jsx(_e,{variant:"body2",fontWeight:"bold",children:w})]},v)})})]})}),S.jsx(we,{item:!0,xs:12,sm:6,lg:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[S.jsxs(_e,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:n.palette.primary.dark,display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[S.jsx(c5,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Pending by Project"]}),S.jsx(Ht,{children:Yv.slice(0,4).map(v=>{const w=l.filter(j=>j.project?.value===v.value&&!j.completed).length;return S.jsxs(Ht,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[S.jsx(_e,{variant:"body2",sx:{flex:1,overflow:"hidden",textOverflow:"ellipsis"},children:v.label}),S.jsx(_e,{variant:"body2",fontWeight:"bold",color:w>0?"error.main":"success.main",children:w})]},v.value)})})]})}),S.jsx(we,{item:!0,xs:12,sm:6,lg:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[S.jsxs(_e,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:"#f44336",display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[S.jsx(b5,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Urgent Tasks"]}),l.filter(v=>{const w=$a(v.taskPCD),j=$a();return!v.completed&&w.isValid()&&w.diff(j,"days")<=2}).length===0?S.jsx(_e,{variant:"body2",color:"textSecondary",children:"No urgent tasks"}):S.jsx(Ht,{children:l.filter(v=>{const w=$a(v.taskPCD),j=$a();return!v.completed&&w.isValid()&&w.diff(j,"days")<=2}).slice(0,3).map(v=>S.jsxs(Ht,{sx:{mb:1,p:1,backgroundColor:"#ffebee",borderRadius:1,border:"1px solid #ffcdd2"},children:[S.jsx(_e,{variant:"body2",fontWeight:"bold",color:"error.main",children:v.taskName}),S.jsxs(_e,{variant:"caption",color:"textSecondary",children:["Due: ",$a(v.taskPCD).format("MMM DD, YYYY")]})]},v.id))})]})}),S.jsx(we,{item:!0,xs:12,sm:12,lg:12,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[S.jsxs(_e,{variant:"subtitle1",component:"h3",sx:{mb:1.5,color:n.palette.primary.dark,display:"flex",alignItems:"center",fontSize:{xs:"0.9rem",md:"1rem"},fontWeight:600},children:[S.jsx(f5,{sx:{mr:1,fontSize:{xs:"1rem",md:"1.2rem"}}}),"Quick Stats"]}),S.jsxs(we,{container:!0,spacing:2,children:[S.jsx(we,{item:!0,xs:6,children:S.jsxs(Ht,{sx:{textAlign:"center",p:1,backgroundColor:"#e3f2fd",borderRadius:1},children:[S.jsx(_e,{variant:"h4",fontWeight:"bold",color:"primary.main",children:l.length}),S.jsx(_e,{variant:"caption",color:"textSecondary",children:"Total Tasks"})]})}),S.jsx(we,{item:!0,xs:6,children:S.jsxs(Ht,{sx:{textAlign:"center",p:1,backgroundColor:"#e8f5e8",borderRadius:1},children:[S.jsx(_e,{variant:"h4",fontWeight:"bold",color:"success.main",children:l.filter(v=>v.completed).length}),S.jsx(_e,{variant:"caption",color:"textSecondary",children:"Completed"})]})}),S.jsx(we,{item:!0,xs:6,children:S.jsxs(Ht,{sx:{textAlign:"center",p:1,backgroundColor:"#fff3e0",borderRadius:1},children:[S.jsx(_e,{variant:"h4",fontWeight:"bold",color:"warning.main",children:l.filter(v=>!v.completed).length}),S.jsx(_e,{variant:"caption",color:"textSecondary",children:"Pending"})]})}),S.jsx(we,{item:!0,xs:6,children:S.jsxs(Ht,{sx:{textAlign:"center",p:1,backgroundColor:"#fce4ec",borderRadius:1},children:[S.jsxs(_e,{variant:"h4",fontWeight:"bold",color:"error.main",children:[Math.round(l.filter(v=>v.completed).length/Math.max(l.length,1)*100),"%"]}),S.jsx(_e,{variant:"caption",color:"textSecondary",children:"Progress"})]})})]})]})})]})})," ",S.jsx(we,{item:!0,xs:12,children:S.jsxs(we,{container:!0,spacing:{xs:1,sm:1.5,md:2},children:[S.jsx(we,{item:!0,xs:12,md:6,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[S.jsx(_e,{variant:"h6",component:"h3",sx:{mb:1.5,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Attachment Details"}),Gv.length===0?S.jsx(_e,{variant:"body2",color:"textSecondary",children:"No attachments available."}):S.jsx(Qw,{sx:{maxHeight:200},children:S.jsxs(Bw,{size:"small",children:[S.jsx(e5,{children:S.jsxs(Iv,{children:[S.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"Sl."}),S.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"Select"}),S.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"View"}),S.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"File Name"}),S.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:"Description"})]})}),S.jsx(Uw,{children:Gv.slice(0,3).map((v,w)=>S.jsxs(Iv,{children:[S.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:w+1}),S.jsx(Da,{sx:{py:.5},children:S.jsx("input",{type:"checkbox"})}),S.jsx(Da,{sx:{py:.5},children:S.jsx(rl,{size:"small","aria-label":"view attachment",children:S.jsx(v5,{})})}),S.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:v.fileName}),S.jsx(Da,{sx:{py:.5,fontSize:"0.75rem"},children:v.description})]},v.id))})]})})]})}),S.jsx(we,{item:!0,xs:12,md:6,children:S.jsxs(hn,{elevation:2,sx:{p:{xs:1.5,sm:2,md:2.5},height:"fit-content"},children:[S.jsx(_e,{variant:"h6",component:"h3",sx:{mb:1.5,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Your Tasks"}),l.length===0?S.jsx(_e,{variant:"body2",color:"textSecondary",sx:{textAlign:"center",py:2},children:"No tasks yet! Add a new task above to get started."}):S.jsx(Ht,{sx:{maxHeight:300,overflow:"auto"},children:S.jsx(we,{container:!0,spacing:1,children:l.slice(0,4).map(v=>S.jsx(we,{item:!0,xs:12,sm:6,children:S.jsxs(hn,{elevation:2,sx:{p:r?2:3,display:"flex",flexDirection:"column",justifyContent:"space-between",minHeight:"200px",borderColor:v.completed?n.palette.success.main:n.palette.grey[300],borderWidth:1,borderStyle:"solid",position:"relative",overflow:"hidden",transition:"transform 0.3s, box-shadow 0.3s","&:hover":{transform:"translateY(-5px)",boxShadow:n.shadows[6]}},children:[S.jsxs(Ht,{children:[S.jsxs(_e,{variant:"h6",component:"h3",sx:{textDecoration:v.completed?"line-through":"none",mb:1,wordBreak:"break-word"},children:[v.taskName," ",v.taskId&&`(#${v.taskId})`]}),v.project&&S.jsxs(_e,{variant:"body2",color:"textSecondary",children:["Project: ",v.project]}),v.assignedTo&&S.jsxs(_e,{variant:"body2",color:"textSecondary",children:["Assigned To: ",v.assignedTo]}),v.taskType&&S.jsxs(_e,{variant:"body2",color:"textSecondary",children:["Type: ",v.taskType]}),v.currentStatus&&S.jsxs(_e,{variant:"body2",color:"textSecondary",children:["Status: ",v.currentStatus]}),v.estimatedHours&&S.jsxs(_e,{variant:"body2",color:"textSecondary",children:["Est. Hours: ",v.estimatedHours]}),v.taskPCD&&S.jsxs(_e,{variant:"body2",color:"textSecondary",children:["PCD: ",v.taskPCD]}),v.startDate&&S.jsxs(_e,{variant:"body2",color:"textSecondary",children:["Start Date: ",v.startDate]}),v.endDate&&S.jsxs(_e,{variant:"body2",color:"textSecondary",children:["End Date: ",v.endDate]}),v.repeatTask&&v.repeatFrequency&&S.jsxs(_e,{variant:"body2",color:"textSecondary",children:["Repeat: ",v.repeatFrequency]})]}),S.jsxs(Ht,{sx:{mt:2,display:"flex",justifyContent:"flex-end",gap:1},children:[S.jsx(rl,{color:v.completed?"success":"default",onClick:()=>R(v.id),"aria-label":v.completed?"Mark Incomplete":"Mark Complete",children:S.jsx(p5,{})}),S.jsx(rl,{color:"info",onClick:()=>M(v),"aria-label":"Edit Task",children:S.jsx(h5,{})}),S.jsx(rl,{color:"error",onClick:()=>x(v.id),"aria-label":"Delete Task",children:S.jsx(m5,{})})]}),v.completed&&S.jsx(Ht,{sx:{position:"absolute",top:0,right:0,backgroundColor:n.palette.success.light,color:n.palette.success.contrastText,px:1.5,py:.5,borderBottomLeftRadius:8,fontSize:"0.75rem",fontWeight:"bold",textTransform:"uppercase"},children:"Completed"})]})},v.id))})})]})})]})})]})})]})}function A5(){return S.jsxs(bT,{theme:vb,children:[S.jsx(WO,{}),S.jsx(w5,{})]})}kS.createRoot(document.getElementById("root")).render(S.jsx(O.StrictMode,{children:S.jsx(A5,{})}));
